# CSV Upload: Update Existing PayStubs Implementation Plan

## Background

### Current Issue
When uploading CSV data for employees who already have PayStubs in a timesheet, the system creates duplicate PayStubs instead of updating the existing ones. This causes:
- Multiple PayStub records for the same employee in one timesheet
- Data fragmentation (employee's hours split across multiple PayStubs)
- Confusion in the UI showing duplicate employee entries

### Current Behavior
1. CSV upload always uses `addPayStubs` mutation input
2. Backend creates new PayStub records without checking for existing ones
3. No merge logic exists for combining new CSV data with existing PayStub details

### Desired Behavior
1. System should find existing PayStubs for employees in the CSV
2. Update existing PayStub details with new data from CSV
3. Only create new PayStubs for employees not already in the timesheet
4. <PERSON><PERSON> conflicts when CSV data would overwrite existing detail data

## Technical Architecture

### Data Model
```typescript
// PayStub (one per employee per timesheet)
interface PayStub {
    id: string;
    employeeId: string;
    timeSheetId: string;
    details: PayStubDetail[];
}

// PayStubDetail (one per work date)
interface PayStubDetail {
    id: string;
    payStubId: string;
    workDate: string;      // YYYY-MM-DD format
    stHours: number;
    otHours: number;
    dtHours: number;
    // ... other fields
}
```

### Key Constraint
- **Business Rule**: Each employee can have only ONE PayStub per timesheet
- **Current Violation**: CSV upload creates additional PayStubs for existing employees

## Implementation Steps

### Phase 1: Frontend - Fetch and Match Existing PayStubs

#### 1.1 Modify UploadTimeSheetContent.tsx

Add logic to use existing PayStub data:

```typescript
// The component already receives timeSheetRef which includes payStubsConnection
// We need to extract and use this data

interface ExistingPayStubMap {
    [employeeId: string]: {
        payStubId: string;
        existingDetails: Map<string, PayStubDetail>; // keyed by workDate
    };
}
```

#### 1.2 Create Matching Logic

Before processing CSV data, build a map of existing PayStubs:

```typescript
const buildExistingPayStubMap = (payStubs: PayStub[]): ExistingPayStubMap => {
    const map: ExistingPayStubMap = {};
    
    payStubs.forEach(payStub => {
        const detailsMap = new Map<string, PayStubDetail>();
        payStub.details.forEach(detail => {
            detailsMap.set(detail.workDate, detail);
        });
        
        map[payStub.employeeId] = {
            payStubId: payStub.id,
            existingDetails: detailsMap
        };
    });
    
    return map;
};
```

### Phase 2: Data Transformation with Merge Logic

#### 2.1 Modify dataTransformation.ts

Update the transformation logic to handle both create and update scenarios:

```typescript
interface TransformResult {
    payStubsToAdd: PayStubUpload[];      // New PayStubs
    payStubsToModify: PayStubModify[];   // Existing PayStubs to update
    conflicts: ConflictInfo[];            // Details that would overwrite existing data
}

const transformWithExistingData = (
    processedEntries: ProcessedTimesheetEntry[],
    employeeData: Employee[],
    existingPayStubMap: ExistingPayStubMap
): TransformResult => {
    // Group by employee
    // Check if employee has existing PayStub
    // Merge or create accordingly
    // Track conflicts
};
```

#### 2.2 Conflict Detection

```typescript
interface ConflictInfo {
    employeeId: string;
    employeeName: string;
    workDate: string;
    existingHours: { st: number; ot: number; dt: number };
    newHours: { st: number; ot: number; dt: number };
}
```

### Phase 3: User Conflict Resolution

#### 3.1 Create Conflict Resolution Dialog

New component: `ConflictResolutionDialog.tsx`

```typescript
interface ConflictResolutionProps {
    conflicts: ConflictInfo[];
    onResolve: (resolution: 'overwrite' | 'skip' | 'cancel') => void;
}
```

Options:
1. **Overwrite All**: Replace existing hours with CSV data
2. **Skip Conflicts**: Only add new dates, skip dates with existing data
3. **Cancel**: Cancel the entire upload

#### 3.2 Update Upload Flow

```typescript
// In handleFileUpload
if (conflicts.length > 0) {
    setConflicts(conflicts);
    setShowConflictDialog(true);
    // Wait for user resolution before proceeding
    return;
}
```

### Phase 4: Update GraphQL Mutation

#### 4.1 Modify BulkAddPayStubsMutation.ts

Update to handle both add and modify operations:

```typescript
const mutation = graphql`
    mutation BulkAddPayStubsMutation($input: ModifyTimeSheetInput!) {
        modifyTimeSheet(input: $input) {
            timeSheet {
                id
                payStubs {
                    # ... existing fields
                }
            }
            errors
        }
    }
`;

// Update input structure
const input = {
    id: timeSheetId,
    addPayStubs: payStubsToAdd,      // New PayStubs
    modifyPayStubs: payStubsToModify, // Updated PayStubs
};
```

#### 4.2 Update Optimistic Response

Ensure optimistic updates handle both new and modified PayStubs correctly.

### Phase 5: Backend Updates (Optional Enhancement)

While the current backend supports `modifyPayStubs`, we might want to add:

1. **Upsert Logic**: Single operation that creates or updates based on employee/timesheet combination
2. **Batch Validation**: Ensure no duplicate PayStubs are created
3. **Audit Trail**: Track when PayStub details are overwritten

## Implementation Sequence

1. **Step 1**: Extract existing PayStub data in UploadTimeSheetContent
2. **Step 2**: Create ExistingPayStubMap utility
3. **Step 3**: Update dataTransformation to separate add/modify operations
4. **Step 4**: Implement conflict detection
5. **Step 5**: Create ConflictResolutionDialog component
6. **Step 6**: Update mutation to handle both operations
7. **Step 7**: Test with various scenarios
8. **Step 8**: Update documentation

## Test Scenarios

### Scenario 1: New Employee
- CSV contains employee not in timesheet
- Expected: Create new PayStub with all details

### Scenario 2: Existing Employee, New Dates
- CSV contains new dates for existing employee
- Expected: Update existing PayStub, add new details

### Scenario 3: Existing Employee, Overlapping Dates
- CSV contains dates that already have hours
- Expected: Show conflict dialog, handle per user choice

### Scenario 4: Mixed Upload
- CSV contains mix of new employees and existing employees
- Expected: Create new PayStubs for new employees, update existing ones

## Edge Cases

1. **Deleted PayStubs**: Handle case where PayStub was marked for deletion
2. **Empty Details**: Handle PayStubs with no detail records
3. **Large Conflicts**: UI consideration for many conflicts
4. **Partial Success**: Handle when some operations succeed but others fail

## UI/UX Considerations

### Conflict Resolution Dialog Mock
```
┌─────────────────────────────────────────────┐
│ Conflicts Found                             │
├─────────────────────────────────────────────┤
│ Some employees already have hours for       │
│ these dates. How would you like to proceed? │
│                                             │
│ • John Doe (7/14/2025)                     │
│   Existing: 8 ST, 0 OT                     │
│   New: 11 ST, 0 OT                         │
│                                             │
│ • Jane Smith (7/15/2025)                   │
│   Existing: 8 ST, 2 OT                     │
│   New: 6 ST, 4 OT                          │
│                                             │
│ [Overwrite All] [Skip Conflicts] [Cancel]   │
└─────────────────────────────────────────────┘
```

## Success Criteria

1. ✅ No duplicate PayStubs created for existing employees
2. ✅ Existing PayStub details are updated correctly
3. ✅ User has control over conflict resolution
4. ✅ Clear feedback on what was created vs. updated
5. ✅ Maintains data integrity (no orphaned records)
6. ✅ Performance acceptable for large CSV files

## Dependencies

- Requires access to existing PayStub data in UploadTimeSheetContent
- GraphQL schema already supports modifyPayStubs operation
- No backend changes required for basic implementation

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Data loss from overwrites | Conflict resolution dialog |
| Performance with large timesheets | Implement pagination if needed |
| Complex merge logic | Comprehensive unit tests |
| User confusion | Clear UI feedback and documentation |

## Estimated Effort

- Frontend Development: 3-4 days
- Testing: 1-2 days
- Documentation: 0.5 days
- Total: ~1 week

## Future Enhancements

1. **Selective Overwrite**: Allow user to choose which specific fields to overwrite
2. **Preview Mode**: Show what will change before confirming
3. **Undo Capability**: Allow reverting recent CSV uploads
4. **Merge Strategies**: Different strategies for combining hours (add vs. replace)