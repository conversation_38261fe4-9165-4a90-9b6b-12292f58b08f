# CLAUDE.md - Project Guidelines for CLAUDE CLI Tool

This document provides essential information for the CLAUDE CLI tool to effectively navigate, understand, and assist with the `eprlive24/timesheet-fix8` repository.

## 1. Project Overview

This is a monorepo containing several interconnected applications:
*   **`aspire/`**: An ASP.NET Aspire AppHost project for orchestrating the backend, frontend, and other services (Kafka, Traefik) in local development and cloud deployments.
*   **`backend/`**: A .NET 9.0 GraphQL API service built with HotChocolate and Entity Framework Core, using SQL Server. It handles core business logic, data access, and provides a GraphQL endpoint.
*   **`identity/`**: An ASP.NET Core Identity and OpenIddict server for authentication and authorization, managing user identities and token introspection.
*   **`ui/`**: A frontend application built with React, Vite, React Spectrum, and Relay, consuming the GraphQL API.

The project uses `pnpm` as its package manager.

## 2. Key Technologies

*   **Backend:**
    *   .NET 9.0
    *   ASP.NET Core
    *   HotChocolate (GraphQL)
    *   Entity Framework Core
    *   SQL Server
    *   OAuth 2.0 / OpenID Connect
*   **Frontend:**
    *   React
    *   Vite
    *   React Spectrum (UI Library)
    *   Relay (GraphQL Client)
    *   TypeScript
    *   pnpm
*   **Orchestration:**
    *   .NET Aspire
*   **Infrastructure:**
    *   Docker
    *   Traefik
    *   Kafka

## 3. Getting Started

### 3.1. Prerequisites

*   .NET 9.0 SDK
*   Node.js (v21.5.0 or greater for `ui` project)
*   SQL Server instance
*   pnpm (for `ui` project)

### 3.2. Running the Applications

#### Aspire (Orchestration)

The `aspire` project can orchestrate all services.
To run in development mode:
```bash
dotnet watch run --project aspire/EPRAspire.AppHost.csproj --launch-profile Development
```

#### Backend

The GraphQL API will be available at `/graphql`.
To run in development mode:
```bash
dotnet watch run --project backend/backend.csproj --launch-profile Development
```
Before running, ensure the database connection string in `backend/appsettings.json` is updated and migrations are applied:
```bash
dotnet ef database update --project backend/backend.csproj
```

#### Identity

To run in development mode:
```bash
dotnet run --project identity/EPRIdentity.Web.csproj --launch-profile Development
```
Before running, ensure the database connection string in `identity/appsettings.json` is updated and migrations are applied:
```bash
dotnet ef database update --project identity/EPRIdentity.Web.csproj --context EPRIdentityDbContext
```

#### UI

The UI application will typically run on `http://localhost:3001`.
1.  Install dependencies:
    ```bash
    cd ui
    pnpm install
    ```
2.  Generate Relay files:
    ```bash
    pnpm relay
    ```
3.  Start development server:
    ```bash
    pnpm dev
    ```
    Ensure `NEXT_PUBLIC_API_URL` in `ui/.env.local` points to the backend GraphQL endpoint (e.g., `http://localhost:5000/graphql`).

## 4. Development Workflows

### 4.1. Backend (C# / .NET)

*   **Database Migrations:**
    *   Add new migration: `dotnet ef migrations add MigrationName --project backend/backend.csproj`
    *   Apply migrations: `dotnet ef database update --project backend/backend.csproj`
*   **Testing:**
    *   Run tests: `dotnet test backend/backend.csproj` (or specific test projects if available)

### 4.2. Frontend (React / TypeScript)

*   **Relay Schema Update:**
    ```bash
    cd ui
    pnpm relay:schema # Updates schema from backend (http://localhost:5100/graphql by default)
    pnpm relay        # Generates Relay files after schema changes
    ```
*   **Code Quality:**
    ```bash
    cd ui
    pnpm lint         # Run ESLint
    pnpm lint:fix     # Fix ESLint issues
    pnpm format       # Format code with Prettier
    ```
*   **Testing:**
    ```bash
    cd ui
    pnpm test         # Run all tests
    pnpm test:unit    # Run unit tests
    pnpm test:integration # Run integration tests
    ```
*   **Type Checking:**
    ```bash
    cd ui
    pnpm type-check
    ```

## 5. Code Style and Conventions

All code written for this project, especially in the `ui` directory, must adhere to the guidelines outlined in:
*   [`ui/docs/REACT-RULES.md`](ui/docs/REACT-RULES.md) - Covers general React best practices, component naming, hooks usage, state management, and performance.
*   [`ui/docs/RELAY-PITFALLS.md`](ui/docs/RELAY-PITFALLS.md) - Details common pitfalls and nuances when working with Relay, including fragment composition, data flow, and type safety.

## 6. Project Structure

*   `aspire/`: .NET Aspire AppHost project.
*   `backend/`: .NET GraphQL API.
    *   `Controllers/`: API controllers.
    *   `Data/`: DTOs, Models, EF Core context.
    *   `Migrations/`: EF Core database migrations.
    *   `Services/`: Business logic.
    *   `Types/`: GraphQL type definitions (Inputs, Mutations, Queries, Outputs).
*   `identity/`: .NET Identity and OpenIddict server.
*   `ui/`: React frontend application.
    *   `app/`: Next.js app directory (or similar structure for Vite).
    *   `lib/`: Library code and Relay generated files.
    *   `src/`: Source code.
        *   `components/`: React components.
        *   `constants/`: Constant values (e.g., `text.ts` for UI text).
        *   `context/`: React Contexts (e.g., `TimesheetUIContext`).
        *   `hooks/`: Custom React hooks.
        *   `services/`: Frontend service layer.

## 6. Important Notes

*   **Relay-Centric Frontend:** The `ui` project heavily relies on Relay for data fetching. Avoid duplicating server data in local React state or context.
*   **Text Constants:** All user-facing text in the `ui` project should be defined in `ui/src/constants/text.ts`.
*   **Defensive Programming:** Backend projects emphasize comprehensive null checks (`ArgumentNullException.ThrowIfNull()`) and robust error handling.
*   **Environment-Based Configuration:** Both backend and identity projects prefer environment detection (`IsDevelopment()`) over custom configuration flags for conditional behavior.
*   **Security Best Practices:** Security is a high priority, with features like token introspection, token binding, and comprehensive input validation implemented.
*   **Server Management for Testing:** When testing changes that require the UI or backend server to be running, the CLAUDE CLI tool should **not** attempt to start these servers automatically. Instead, it should inform the user that the server needs to be running and ask them to start it if it's not already. This is to avoid conflicts if the user already has the servers running.
*   **Git commit rules:** NEVER git add/commit changes, unless *explicity* asked to do so. And even then do not add Claude attribution to the commit message.
