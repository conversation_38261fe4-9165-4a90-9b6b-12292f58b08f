using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace EPREvents.Diagnostics;

/// <summary>
/// Middleware that short-circuits requests while the database is unavailable.
/// Produces either a standard HTTP 503 response or, for GraphQL endpoints,
/// a valid GraphQL error envelope.
/// </summary>
public sealed class DatabaseUnavailableMiddleware
{
    private readonly RequestDelegate _next;
    private readonly DatabaseAvailability _availability;
    private readonly ILogger<DatabaseUnavailableMiddleware> _logger;

    public DatabaseUnavailableMiddleware(RequestDelegate next, DatabaseAvailability availability, ILogger<DatabaseUnavailableMiddleware> logger)
    {
        _next = next;
        _availability = availability;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (_availability.IsAvailable)
        {
            await _next(context);
            return;
        }

        // DB is down – respond appropriately
        var path = context.Request.Path.Value ?? string.Empty;
        _logger.LogWarning("Database unavailable – returning synthetic response for {Path}", path);

        if (path.StartsWith("/graphql", StringComparison.OrdinalIgnoreCase))
        {
            context.Response.StatusCode = StatusCodes.Status200OK;
            context.Response.ContentType = "application/json";
            var payload = new { errors = new[] { new { message = "Database temporarily unavailable." } } };
            await context.Response.WriteAsync(JsonSerializer.Serialize(payload));
        }
        else
        {
            context.Response.StatusCode = StatusCodes.Status503ServiceUnavailable;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync("{\"error\":\"Database temporarily unavailable.\"}");
        }
    }
}
