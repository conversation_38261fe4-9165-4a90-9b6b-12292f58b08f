using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace EPREvents.Diagnostics;

/// <summary>
/// Hosted service that verifies database connectivity during application start-up.
/// It attempts to connect for <see cref="_timeout"/> (default 10 s). If it cannot
/// connect, it sets a shared <see cref="DatabaseAvailability"/> flag so that
/// middleware can return a graceful 503 / GraphQL error instead of letting the
/// service crash.  A clear <c>LogLevel.Critical</c> message is still emitted so
/// operators immediately know the root cause.
/// </summary>
/// <typeparam name="TContext">The <see cref="DbContext"/> whose database should be reachable.</typeparam>
public sealed class DatabaseStartupProbe<TContext> : IHostedService where TContext : DbContext
{
    private static readonly TimeSpan _startupTimeout = TimeSpan.FromSeconds(10);
    private static readonly TimeSpan _probeInterval = TimeSpan.FromSeconds(30);

    private Task? _backgroundTask;
    private CancellationTokenSource? _cts;
    private readonly IServiceProvider _services;
    private readonly ILogger<DatabaseStartupProbe<TContext>> _logger;
    private readonly DatabaseAvailability _availability;

    public DatabaseStartupProbe(
        IServiceProvider services,
        ILogger<DatabaseStartupProbe<TContext>> logger,
        DatabaseAvailability availability)
    {
        _services = services;
        _logger = logger;
        _availability = availability;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _backgroundTask = Task.Run(() => RunProbeLoopAsync(_cts.Token));
        return Task.CompletedTask;
    }

    private async Task RunProbeLoopAsync(CancellationToken token)
    {
        // Initial aggressive probe for up to _startupTimeout
        var sw = Stopwatch.StartNew();
        var deadline = DateTime.UtcNow + _startupTimeout;

        while (DateTime.UtcNow < deadline && !token.IsCancellationRequested)
        {
            try
            {
                using var scope = _services.CreateScope();
                var db = scope.ServiceProvider.GetRequiredService<TContext>();

                if (await db.Database.CanConnectAsync(token))
                {
                    _availability.IsAvailable = true;
                    _logger.LogInformation("✅ Successfully connected to SQL Server for {Context} after {ElapsedMs} ms", typeof(TContext).Name, sw.ElapsedMilliseconds);
                    return; // success
                }
            }
            catch (Exception ex) when (!token.IsCancellationRequested)
            {
                // Swallow and retry; only log at Debug to avoid noise.
                _logger.LogDebug(ex, "Database connection attempt failed for {Context}", typeof(TContext).Name);
            }

            // small back-off so we do not spin aggressively
            await Task.Delay(TimeSpan.FromMilliseconds(500), token);
        }

        // If we get here, we failed to connect in time.
        _logger.LogCritical(
            "❌ Unable to connect to SQL Server for {Context} within {TimeoutSeconds}s. The application will stay up but will report 503.",
            typeof(TContext).Name,
            _startupTimeout.TotalSeconds);

        try
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ Unable to connect to SQL Server for {typeof(TContext).Name} within {_startupTimeout.TotalSeconds}s. Application will serve 503 responses.");
        }
        finally
        {
            Console.ResetColor();
        }

        // Mark as unavailable so middleware can react
        _availability.IsAvailable = false;

        // enter periodic probe mode
        while (!token.IsCancellationRequested)
        {
            await Task.Delay(_probeInterval, token);
            try
            {
                using var scope = _services.CreateScope();
                var db = scope.ServiceProvider.GetRequiredService<TContext>();
                if (await db.Database.CanConnectAsync(token))
                {
                    if (!_availability.IsAvailable)
                    {
                        _availability.IsAvailable = true;
                        _logger.LogInformation("✅ Database for {Context} has become available.", typeof(TContext).Name);
                    }
                }
                else if (_availability.IsAvailable)
                {
                    _availability.IsAvailable = false;
                    _logger.LogCritical("❌ Lost connection to SQL Server for {Context}. Will serve 503 until recovery.", typeof(TContext).Name);
                }
            }
            catch (Exception ex) when (!token.IsCancellationRequested)
            {
                _logger.LogDebug(ex, "Periodic database check failed for {Context}", typeof(TContext).Name);
                if (_availability.IsAvailable)
                {
                    _availability.IsAvailable = false;
                    _logger.LogCritical("❌ Lost connection to SQL Server for {Context}. Will serve 503 until recovery.", typeof(TContext).Name);
                }
            }
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        if (_cts != null)
            _cts.Cancel();
        if (_backgroundTask != null)
            await _backgroundTask;
    }
}
