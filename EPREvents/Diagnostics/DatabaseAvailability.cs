using System.Threading;

namespace EPREvents.Diagnostics;

/// <summary>
/// Simple singleton used to share the database availability state with middleware
/// and other components.
/// </summary>
public sealed class DatabaseAvailability
{
    /// <summary>
    /// Gets or sets a value indicating whether the database is currently reachable.
    /// </summary>
    private volatile bool _isAvailable; // guarded by Volatile

    /// <summary>
    /// Gets or sets a value indicating whether the database is currently reachable.
    /// Thread-safe via <see cref="Volatile"/> reads/writes.
    /// </summary>
    public bool IsAvailable
    {
        get => Volatile.Read(ref _isAvailable);
        set => Volatile.Write(ref _isAvailable, value);
    }
}
