# GraphQL Subscriptions Implementation Report (Hot Chocolate v15)

**Target scope**: Provide real-time subscription support **ONLY** for _timesheets_, _paystubs_ and _pay-stub details_.

**Backend tech-stack recap**
- .NET 9 (ASP.NET Core)
- HotChocolate 15.1.7 with Relay conventions enabled
- GraphQL server registered in `Program.cs` via `AddGraphQLServer()`
- Mutation/Query types reside under `backend/Types/*`
- No subscription-related packages, configuration or code exists yet

---
## 1   GraphQL Subscriptions – Background (Hot Chocolate v15)

| Concept | Purpose |
|---------|---------|
|`Subscription` type|Special GraphQL root type whose fields stream events to clients.|
|Transport|Hot Chocolate supports **GraphQL over WebSockets** (recommended) and **SSE**. WebSockets are already part of `HotChocolate.AspNetCore`; you only need to enable them.|
|Pub/Sub provider|Determines how server instances exchange events. Providers: **In-Memory** (single-instance dev), **Redis**, **Postgres**, etc.|
|`ITopicEventSender` / `ITopicEventReceiver`| DI services used to publish and consume events. Events are grouped by **topics** (strings or typed objects).|
|`[Subscribe]`, `[Topic]`, `[EventMessage]`| Schema-builder attributes that declare a subscription field and map its payload.|
|Relay compatibility|Subscriptions work fine with Relay. The `@stream` or `@defer` directives are **not** required.|

> Hot Chocolate official docs: <https://chillicream.com/docs/hotchocolate/v15/defining-a-schema/subscriptions>

---
## 2   Current State Analysis

| Area | Status | Gaps |
|------|--------|------|
|NuGet packages|`HotChocolate.AspNetCore`, `HotChocolate.Data`, `HotChocolate.Types`, _etc._ | **Missing:** `HotChocolate.Subscriptions` (core) **and** a provider package (`HotChocolate.Subscriptions.Redis` or use In-Memory). |
|GraphQL server builder (`Program.cs`) | `.AddGraphQLServer() … .AddMutationConventions() .AddGlobalObjectIdentification()` | No `.AddSubscriptionType<>()` call; no `.AddInMemorySubscriptions()` or provider setup. |
|WebSocket middleware|Not present (`app.UseWebSockets()` is absent).|Required to negotiate WebSocket protocol before `MapGraphQL`.|
|Schema code|No `[SubscriptionType]` class in `backend/Types`.|Need new files under `backend/Types/Subscriptions/…`.|
|Mutations|Timesheet mutations live in `TimesheetMutations.cs`; they do not publish events.|Must inject `ITopicEventSender` and call `SendAsync` after successful writes.|
|Authorization|Queries/Mutations use `[Authorize]`.|Subscriptions need similar protection + WebSocket auth interceptor (optional).|

---
## 3   High-Level Integration Steps

1. **Add NuGet packages**  
   ¬ `dotnet add backend package HotChocolate.Subscriptions --version 15.1.7`  
   ¬ Choose a provider:  
   • **Simple/Solo instance** → add nothing else and use `AddInMemorySubscriptions()`  
   • **Multi-instance / Docker swarm / k8s** → also add `HotChocolate.Subscriptions.Redis` and configure `AddRedisSubscriptions(…)`.

2. **Enable WebSockets** (must run **before** `MapGraphQL`).
   ```csharp
   // Program.cs – before app.UseRouting();
   app.UseWebSockets();
   ```

3. **Extend GraphQL server registration**.
   ```csharp
   builder.Services.AddGraphQLServer()
       // existing calls
       .AddSubscriptionType<Subscriptions>()            // NEW root type
       .AddInMemorySubscriptions();                      // or AddRedisSubscriptions(...)
   ```

4. **Create Subscription Types**.
   Directory: `backend/Types/Subscriptions`

   Example `TimesheetSubscriptions.cs`:
   ```csharp
   using backend.Data.Models;
   using HotChocolate;
   using HotChocolate.Subscriptions;

   [SubscriptionType]
   [Authorize] // re-use existing policies
   public class TimesheetSubscriptions
   {
       // ❶ Timesheet Added
       [Subscribe]
       [Topic(nameof(TimesheetAdded))]
       public TimeSheet TimesheetAdded([EventMessage] TimeSheet timeSheet) => timeSheet;

       // ❷ Timesheet Updated
       [Subscribe]
       [Topic(nameof(TimesheetUpdated))]
       public TimeSheet TimesheetUpdated([EventMessage] TimeSheet timeSheet) => timeSheet;

       // ❸ PayStub Updated (payload could be PayStub or TimeSheet)
       [Subscribe]
       [Topic(nameof(PayStubUpdated))]
       public PayStub PayStubUpdated([EventMessage] PayStub payStub) => payStub;

       // ❹ PayStub Detail Updated
       [Subscribe]
       [Topic(nameof(PayStubDetailUpdated))]
       public PayStubDetail PayStubDetailUpdated([EventMessage] PayStubDetail detail) => detail;
   }
   ```

   Notes:
   - Using explicit `Topic` attributes keeps the topic string and method strongly coupled.
   - If you need to scope by employer, you can publish to dynamic topics such as `$"TimesheetAdded_{employerGuid}"` and annotate the subscription field with `[Topic]` or `[Topic("TimesheetAdded_{employerGuid}")]` using parameters.

5. **Publish Events from Mutations**.
   ```csharp
   public async Task<AddTimesheetPayload> AddTimesheetAsync(..., ITopicEventSender sender, ...)
   {
       // existing logic …
       await dbContext.SaveChangesAsync(cancellationToken);

       await sender.SendAsync(nameof(TimesheetSubscriptions.TimesheetAdded), timeSheet);
       return new AddTimesheetPayload(edge);
   }
   ```

   Repeat for update/delete operations and for pay-stub mutations.

6. **Authorization over WebSockets (optional but recommended)**
   - WebSocket transport does not automatically carry the bearer token from initial HTTP handshake.  
   - Implement `IHttpConnectionInterceptor` to copy the `Authorization` header into the WebSocket context or  
   - Use Cookie-to-Header middleware already present (if cookies are sent during initial handshake).
   - Apply `[Authorize]` attributes on subscription fields.

7. **Update CORS (if needed)** to include WebSocket origins (same host/port as HTTP typically OK).

8. **Run & Test**
   1. Start backend.
   2. Open Banana Cake Pop or GraphQL Playground and connect via `ws://localhost:<port>/graphql`.
   3. Execute:
      ```graphql
      subscription TimesheetAdded {
        timesheetAdded {
          id
          status
        }
      }
      ```
   4. Trigger the `AddTimesheet` mutation in a separate window; observe streamed result.

---
## 4   Detailed Code Changes

### 4.1 `backend.csproj`
```xml
<ItemGroup>
  <!-- NEW -->
  <PackageReference Include="HotChocolate.Subscriptions" Version="15.1.7" />
  <!-- If scaling horizontally -->
  <!--<PackageReference Include="HotChocolate.Subscriptions.Redis" Version="15.1.7" />-->
</ItemGroup>
```

### 4.2 `Program.cs`
1. At top of middleware pipeline _before_ `UseRouting`:
   ```csharp
   app.UseWebSockets();
   ```
2. Extend GraphQL builder chain (keep existing order):
   ```csharp
   .AddSubscriptionType<TimesheetSubscriptions>()
   .AddInMemorySubscriptions(); // or .AddRedisSubscriptions(...)
   ```

### 4.3 New files
```
backend/Types/Subscriptions/
  ├─ TimesheetSubscriptions.cs      // root subscription type
  └─ (Optional) PayStubSubscriptions.cs
```

### 4.4 Mutation Updates (example)
```diff
 public async Task<AddTimesheetPayload> AddTimesheetAsync(
     AddTimesheetInput input,
     EPRLiveDBContext dbContext,
+    ITopicEventSender sender,
     CancellationToken cancellationToken)
 {
     // existing save logic …
     await dbContext.SaveChangesAsync(cancellationToken);
+
+    await sender.SendAsync(nameof(TimesheetSubscriptions.TimesheetAdded), timeSheet);
     return new AddTimesheetPayload(edge);
 }
```

Repeat analogous changes in `UpdateTimesheetAsync`, `DeleteTimesheetAsync`, pay-stub and pay-stub detail mutations.

---
## 5   Provider Decision Matrix

| Provider | Pros | Cons |
|----------|------|------|
|In-Memory (default)|Zero config, ideal for local dev & single instance deployment.|Does **NOT** work when you scale the API horizontally; each node has isolated event queue.|
|Redis|Reliable, battle-tested pub/sub, easy Docker deployment.|Requires Redis infrastructure; extra package.|
|Postgres|Leverages existing DB; no extra infra.|LISTEN/NOTIFY limitations; payload size limit; still preview in HC v15.|

> **Recommended**: Start with **In-Memory**. Switch to Redis when you introduce multiple backend replicas; only 2-3 lines of code change.

---
## 6   Frontend / Relay Considerations (FYI)

- Relay Modern >=14 supports `graphql-ws` out of the box via the `subscriptions` network layer.
- Ensure you pass the same auth headers used for HTTP.
- Remember to dispose of subscription on component unmount.

_Not part of this backend change, but noted for completeness._

---
## 7   Testing Checklist

1. Unit-test subscription registration using `SchemaBuilder.New().AddQueryType<Query>().AddSubscriptionType<TimesheetSubscriptions>()…`.
2. Integration test with `WebApplicationFactory` and WebSocket client (see Hot Chocolate docs – WebSocketTestServer).
3. Verify authorization: connect without token → expect `UNAUTHENTICATED` error; connect with token → success.
4. Verify channel filtering (employerGuid or id) if implemented.

---
## 8   Potential Pitfalls & Mitigations

| Issue | Mitigation |
|-------|------------|
|WebSocket 413 ‘Request entity too large’|Increase `WebSocketOptions.ReceiveBufferSize` if payloads are big.|
|Connection drops in idle|Set `KeepAliveInterval` in `UseWebSockets`.|
|HTTP headers lost in upgrade|Implement `IHttpConnectionInterceptor` to forward headers→context.|
|Circular dependency errors after injecting `ITopicEventSender`|Inject via method parameter rather than constructor when possible.

---
## 9   Estimated Effort

| Task | Low | High |
|------|----:|-----:|
|Package + build config | 0.5 h | 1 h |
|Program.cs modifications | 0.25 h | 0.5 h |
|Create subscription classes | 1 h | 2 h |
|Update 4-5 mutations | 1.5 h | 3 h |
|Manual testing & docs | 1 h | 2 h |
|TOTAL | **4.25 h** | **8.5 h** |

---
## 10   Next Steps

1. Decide on pub/sub provider (stick with In-Memory for PoC).
2. Merge package & Program.cs changes.
3. Implement `TimesheetSubscriptions` and update relevant mutations.
4. Run local manual test via Banana Cake Pop.
5. Hand over to frontend to integrate Relay `subscriptions` network layer.

---

_This report prepared on 2025-07-23 22:01 (PDT)._
