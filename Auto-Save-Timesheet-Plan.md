# Implementation Plan: **Auto-Save for Non-Destructive Edits** on Timesheet Details Page

_Last updated: 2025-07-23_

> This plan supersedes **Require-Save-Before-CSV-Upload.md**.  That workflow will **not** be implemented; all requirements in that document are folded into the auto-save design below.

---

## 1. Objective

1. **Auto-save** every non-destructive edit (hour changes, rates, codes, adding new pay-stubs/details) automatically to the server.
2. **Explicit save** remains **only** for *destructive* actions (deleting pay-stubs or pay-stub details).
3. **CSV upload safety** – ensure no local drafts exist when upload starts (auto-save takes care of this unless deletions are pending).

Benefits:
* Removes explicit "Save" friction for routine edits.
* Fewer race conditions & duplicate-detection code (only server data matters).
* Clear, deliberate UX for irreversible operations.

---

## 2. Current Architecture Recap

* **Relay Store** – authoritative server data.
* **Zustand `TimesheetUIStore`** – holds *draft* maps (`payStubDrafts`, `detailDrafts`) and UI state.
* **Manual Save** – `commitModifyTimeSheetMutation` runs when user clicks Save.
* **CSV Upload** – required flushing drafts to avoid duplicates (prior plan).

---

## 3. Target Architecture

```
 edit → draft in store ───► debounce(1.5 s) ───► commitModifyTimeSheetMutation
                        ▲                               │
                        └──────── validation ───────────┘
```

* Drafts still created for immediate optimistic UI.
* A **debounced auto-save** flushes drafts if:
  * No destructive drafts are pending.
  * No validation errors.
  * No in-flight save.
* Destructive actions set a flag `markedForDeletion` per pay-stub; presence of any such flag suppresses auto-save and shows a **Save** button.

---

## 4. UX Specification

1. **Editing hours/rates/etc.**
   * Field loses focus → auto-save banner "saving…"; disappears on success.
2. **Delete Pay-Stub or Detail**
   * UI marks item visually (red strike-through).
   * Global toolbar shows **Save deletions** button (primary) and **Undo all**.
3. **CSV Upload button**
   * If deletions pending ➜ prompt to save/discard.
   * Otherwise opens immediately; auto-save guarantees clean state.
4. **Error states**
   * Network/validation error keeps drafts; toolbar shows **Retry save**.

---

## 5. Technical Changes

### 5.1 `TimesheetUIStore` (zustand)

1. **New helpers**
   ```ts
   hasDestructiveDrafts(tsId): boolean  // wrapper around markedForDeletion set
   hasValidationErrors(tsId): boolean
   ```
2. **Expose Environment** (`activeEnvironment` field) so store actions can commit mutations without prop-drilling.
3. **Auto-save scheduler**
   ```ts
   import { debounce } from '@/src/utils/debounce';

   const autoSave = debounce(
     (tsId: string) => {
       const state = get();
       if (state.hasDestructiveDrafts(tsId) || state.hasValidationErrors(tsId)) return;
       const input = flatToModifyInput(/* current drafts */);
       commitModifyTimeSheetMutation(state.activeEnvironment, { input });
     },
     1500
   );
   ```
4. **Hook into writers**  (`updatePayStubDraft`, `updateDetailField`, `addEmployeeWithMutation` optimistic path):
   ```ts
   updatePayStubDraft: (...) => {
     // existing draft logic...
     autoSave(timesheetId);
   }
   ```

### 5.2 Mutation utilities

* Ensure `flatToModifyInput` filters out deleted items when `markedForDeletion` present.
* Maintain optimistic updaters – unchanged.

### 5.3 UI Components

1. **SaveToolbar.tsx** (or create if absent)
   * Show if `hasDestructiveDrafts(tsId)` **or** `saveError`.
   * Button calls existing `saveAllChanges` (refactored to use same commit helper but include deletions).
2. **Delete buttons** mark drafts & call `markForDeletion`.
3. **Status Toast/Spinner** – optional small component subscribing to `isSaving`.
4. **UploadTimeSheetContent.tsx**
   * Replace `hasUnsavedChanges` check with `hasDestructiveDrafts`.
   * Prompt only when true.

### 5.4 Validation Flow

* Auto-save should *not* fire while validation errors exist.
* On validation resolve ➜ auto-save debouncer triggers again.

### 5.5 Tests

* **unit** – debounce util, store helpers.
* **component** – edit field ➜ expect commit called after debounce.
* **e2e** ‑ Cypress: edit, wait, reload, data persisted.
* **edge** – delete stub ➜ expect no auto-save until manual save.

### 5.6 Documentation

* Update architecture doc (section 5.3 **State Management**) with auto-save lifecycle diagram.
* Add FAQ: “Why still a Save button?”

---

## 6. File-by-File Todo Checklist

| File | Action |
|------|--------|
| `ui/src/store/timesheetUIStore.ts` | helpers, env field, autoSave debounce, modify draft writers |
| `ui/src/utils/debounce.ts` | ensure leading/trailing options if needed |
| `ui/src/relay/commitModifyTimeSheetMutation.ts` | no change |
| `ui/src/components/TimesheetDetail/...` | Create `SaveToolbar`, update delete buttons, tweak upload handler |
| `ui/src/components/TimesheetDetail/Timesheet-Data-Flow-Architecture.md` | add auto-save section |
| Tests | jest + cypress |

---

## 7. Acceptance Criteria

1. Editing non-destructive fields triggers mutation within ≤2 s of idle time.
2. Browser refresh after auto-save shows updated data without manual Save.
3. Deleting a pay-stub/detail **does not** auto-save; Save button required.
4. CSV upload opens without warning when no deletions pending; blocks otherwise.
5. All ESLint/type checks pass.

---

## 8. Effort Estimate

| Task | Hours |
|------|-------|
| Store refactor + debounce | 6 |
| UI components (toolbar, status) | 4 |
| Upload flow adjustment | 2 |
| Tests | 5 |
| Docs | 1 |
| **Total** | **~18 h (~2-3 dev days)** |

---

## 9. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Excess mutation spam | Debounce + diffing (input only changed fields) |
| Auto-save while editing stubs concurrently in another tab | Relay will last-write-wins; consider adding modified timestamp later |
| Validation prevents save indefinitely | Toast guidance + highlight invalid cells |

---
