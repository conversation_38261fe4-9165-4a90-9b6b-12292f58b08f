# Git Hooks Setup Script for Windows PowerShell
#
# This script configures Git to use the .githooks directory for all hooks
# instead of copying them to .git/hooks/
#
# Usage: .\.githooks\setup.ps1
#

param(
    [switch]$Force = $false
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if we're in a git repository
try {
    $gitDir = git rev-parse --git-dir 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "This script must be run from within a Git repository."
        exit 1
    }
} catch {
    Write-Error "This script must be run from within a Git repository."
    exit 1
}

Write-Status "Configuring Git to use source-controlled hooks..."

# Set core.hooksPath to .githooks
try {
    git config core.hooksPath .githooks
    if ($LASTEXITCODE -eq 0) {
        Write-Status "Successfully configured core.hooksPath to .githooks"
    } else {
        Write-Error "Failed to configure core.hooksPath"
        exit 1
    }
} catch {
    Write-Error "Failed to configure core.hooksPath: $_"
    exit 1
}

# Verify the configuration
$hooksPath = git config core.hooksPath
if ($hooksPath -eq ".githooks") {
    Write-Status "Configuration verified: Git will now use hooks from .githooks directory"
    Write-Host ""
    Write-Status "Setup complete! Git hooks are now source-controlled and active."
    Write-Host ""
    Write-Status "Available hooks:"
    Write-Status "- commit-msg: Automatically prepends issue numbers to commit messages"
    Write-Host ""
    Write-Status "Branch naming pattern: DevName-{issueNumber}-branch-summary"
    Write-Status "Example: Joseph-42-add-login → commits get prefixed with '#42 '"
    Write-Host ""
    Write-Status "To test security: run .githooks/test-security.ps1"
} else {
    Write-Error "Configuration verification failed. Expected '.githooks', got '$hooksPath'"
    exit 1
}

exit 0 