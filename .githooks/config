# Git Hooks Configuration File
# This file contains configuration settings for the Git hooks

# Protected branches that should skip issue number prepending
# Comma-separated list of branch names and patterns
PROTECTED_BRANCHES="main,develop,master,release/*,hotfix/*"

# Issue number validation settings
MAX_ISSUE_NUMBER=999999
MIN_ISSUE_NUMBER=1

# File size limits (in bytes)
MAX_COMMIT_MSG_LENGTH=10000

# Branch name validation settings
MAX_BRANCH_NAME_LENGTH=100

# Logging settings
LOG_LEVEL="INFO"  # Options: ERROR, WARNING, INFO, DEBUG

# Security settings
ALLOW_SPECIAL_CHARS_IN_BRANCH="false"
STRICT_BRANCH_VALIDATION="true"

# Hook behavior settings
SKIP_MERGE_COMMITS="true"
ALLOW_DEVELOPER_OVERRIDE="true"
FAIL_ON_INVALID_BRANCH="true" 