#!/bin/bash
#
# Security Test Suite for Git Hooks
#
# This script tests the Git hooks against various security vulnerabilities
# and edge cases to ensure they handle malicious input safely.
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test results
FAILED_TESTS=()

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Git Hooks Security Test Suite${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    FAILED_TESTS+=("$1")
}

print_summary() {
    echo
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Test Summary${NC}"
    echo -e "${BLUE}================================${NC}"
    echo "Total tests run: $TESTS_RUN"
    echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo
        echo -e "${RED}Failed tests:${NC}"
        for test in "${FAILED_TESTS[@]}"; do
            echo -e "  ${RED}✗${NC} $test"
        done
        echo
        exit 1
    else
        echo
        echo -e "${GREEN}All tests passed!${NC}"
        echo
        exit 0
    fi
}

# Create temporary test directory
TEST_DIR=$(mktemp -d)
cd "$TEST_DIR"

# Initialize a temporary git repository
git init -q
git config user.email "<EMAIL>"
git config user.name "Test User"

# Copy hooks to test directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cp "$SCRIPT_DIR/commit-msg" .git/hooks/
chmod +x .git/hooks/commit-msg

# Test function
run_test() {
    local test_name="$1"
    local branch_name="$2"
    local commit_msg="$3"
    local expected_result="$4"  # "pass" or "fail"
    local expected_output="$5"  # Optional: expected output pattern
    
    TESTS_RUN=$((TESTS_RUN + 1))
    print_test "$test_name"
    
    # Create test branch
    git checkout -b "$branch_name" -q 2>/dev/null || git checkout "$branch_name" -q
    
    # Create test commit message file
    echo "$commit_msg" > test_commit_msg.txt
    
    # Run the hook
    local output
    local exit_code
    output=$(.git/hooks/commit-msg test_commit_msg.txt 2>&1)
    exit_code=$?
    
    # Check result
    if [ "$expected_result" = "pass" ]; then
        if [ $exit_code -eq 0 ]; then
            if [ -n "$expected_output" ]; then
                if echo "$output" | grep -q "$expected_output"; then
                    print_pass "$test_name"
                else
                    print_fail "$test_name - Expected output not found: $expected_output"
                fi
            else
                print_pass "$test_name"
            fi
        else
            print_fail "$test_name - Expected pass but got exit code $exit_code"
        fi
    else
        if [ $exit_code -ne 0 ]; then
            if [ -n "$expected_output" ]; then
                if echo "$output" | grep -q "$expected_output"; then
                    print_pass "$test_name"
                else
                    print_fail "$test_name - Expected error output not found: $expected_output"
                fi
            else
                print_pass "$test_name"
            fi
        else
            print_fail "$test_name - Expected fail but got exit code $exit_code"
        fi
    fi
    
    # Clean up
    rm -f test_commit_msg.txt
    git checkout main -q 2>/dev/null || git checkout -b main -q
}

print_header

# Test 1: Normal operation
echo "## Testing Normal Operation"
run_test "Normal branch with valid issue number" "Joseph-42-add-login" "fix validation bug" "pass"

# Test 2: Security vulnerability tests
echo
echo "## Testing Security Vulnerabilities"

# Command injection attempts
run_test "Command injection with semicolon" "DevName-123-branch; rm -rf /" "test message" "fail" "Invalid branch name format"
run_test "Command injection with backticks" "DevName-123-branch\`malicious\`" "test message" "fail" "Invalid branch name format"
run_test "Command injection with dollar" "DevName-123-branch\$(dangerous)" "test message" "fail" "Invalid branch name format"
run_test "Command injection with pipe" "DevName-123-branch|evil" "test message" "fail" "Invalid branch name format"
run_test "Command injection with ampersand" "DevName-123-branch&background" "test message" "fail" "Invalid branch name format"
run_test "Command injection with redirect" "DevName-123-branch>output" "test message" "fail" "Invalid branch name format"
run_test "Command injection with input redirect" "DevName-123-branch<input" "test message" "fail" "Invalid branch name format"

# Test 3: Input validation tests
echo
echo "## Testing Input Validation"

# Branch name validation
run_test "Empty branch name" "" "test message" "fail" "No branch name provided"
run_test "Branch name with only special chars" "!@#$%^&*()" "test message" "fail" "Branch name contains only invalid characters"
run_test "Branch name too long" "$(printf 'a%.0s' {1..101})-123-branch" "test message" "fail" "Branch name too long"
run_test "Invalid branch format - no dev name" "123-branch" "test message" "fail" "Invalid branch name format"
run_test "Invalid branch format - no issue number" "DevName-branch" "test message" "fail" "Invalid branch name format"
run_test "Invalid branch format - no dash" "DevName123branch" "test message" "fail" "Invalid branch name format"

# Issue number validation
run_test "Issue number zero" "DevName-0-branch" "test message" "fail" "Issue number out of range"
run_test "Issue number negative" "DevName--1-branch" "test message" "fail" "Invalid branch name format"
run_test "Issue number too large" "DevName-9999999-branch" "test message" "fail" "Issue number out of range"
run_test "Issue number non-numeric" "DevName-abc-branch" "test message" "fail" "Invalid branch name format"

# Test 4: File operation tests
echo
echo "## Testing File Operations"

# Test with non-existent file
TESTS_RUN=$((TESTS_RUN + 1))
print_test "Non-existent commit message file"
git checkout -b "DevName-42-test" -q 2>/dev/null || git checkout "DevName-42-test" -q
output=$(.git/hooks/commit-msg non_existent_file.txt 2>&1)
exit_code=$?
if [ $exit_code -ne 0 ] && echo "$output" | grep -q "Commit message file does not exist"; then
    print_pass "Non-existent commit message file"
else
    print_fail "Non-existent commit message file - Expected failure with specific error"
fi

# Test with large file
TESTS_RUN=$((TESTS_RUN + 1))
print_test "Large commit message file"
git checkout -b "DevName-43-test" -q 2>/dev/null || git checkout "DevName-43-test" -q
# Create a file larger than 10KB
dd if=/dev/zero of=large_file.txt bs=1024 count=11 2>/dev/null
output=$(.git/hooks/commit-msg large_file.txt 2>&1)
exit_code=$?
if [ $exit_code -ne 0 ] && echo "$output" | grep -q "Commit message file too large"; then
    print_pass "Large commit message file"
else
    print_fail "Large commit message file - Expected failure with specific error"
fi
rm -f large_file.txt

# Test 5: Edge cases
echo
echo "## Testing Edge Cases"

run_test "Already prefixed message" "DevName-42-test" "#42 already prefixed" "pass" "already has correct prefix"
run_test "Different issue number prefix" "DevName-42-test" "#123 different issue" "pass" "different issue number"
run_test "Merge commit message" "DevName-42-test" "Merge pull request #8 from test" "pass" "Skipping merge commit"

# Test 6: Protected branches
echo
echo "## Testing Protected Branches"

run_test "Main branch" "main" "test message" "pass" "Skipping protected branch"
run_test "Develop branch" "develop" "test message" "pass" "Skipping protected branch"

# Test 7: Special characters in commit messages
echo
echo "## Testing Special Characters in Commit Messages"

run_test "Commit message with quotes" "DevName-42-test" "fix \"quoted\" text" "pass"
run_test "Commit message with apostrophes" "DevName-42-test" "fix user's issue" "pass"
run_test "Commit message with newlines" "DevName-42-test" "fix issue\nwith newline" "pass"

print_summary

# Clean up
cd /
rm -rf "$TEST_DIR" 