#!/bin/sh
#
# Git commit-msg hook for automatic issue number prepending
# 
# This hook automatically prepends issue numbers to commit messages based on branch names.
# Branch naming pattern: DevName-{issueNumber}-branch-summary
# Example: Joseph-42-add-login → commit messages get prefixed with "#42 "
#
# The hook will:
# 1. Extract issue number from current branch name
# 2. Skip if commit message already starts with the issue number
# 3. <PERSON>p merge commits
# 4. Fail if no issue number can be extracted from branch name
#

# Standardized error handling
readonly SCRIPT_NAME="commit-msg-hook"
readonly LOG_PREFIX="[$SCRIPT_NAME]"

log_error() {
    echo "$LOG_PREFIX ERROR: $1" >&2
}

log_warning() {
    echo "$LOG_PREFIX WARNING: $1" >&2
}

log_info() {
    echo "$LOG_PREFIX INFO: $1" >&2
}

# Exit with error and consistent message
exit_with_error() {
    local exit_code="$1"
    local message="$2"
    
    log_error "$message"
    log_error "Hook execution failed. Commit aborted."
    exit "$exit_code"
}

# Validate commit message file
validate_commit_msg_file() {
    local file="$1"
    
    # Check if file parameter is provided
    if [ -z "$file" ]; then
        exit_with_error 1 "No commit message file provided"
    fi
    
    # Check if file exists
    if [ ! -f "$file" ]; then
        exit_with_error 1 "Commit message file does not exist: $file"
    fi
    
    # Check if file is readable
    if [ ! -r "$file" ]; then
        exit_with_error 1 "Cannot read commit message file: $file"
    fi
    
    # Check if file is writable
    if [ ! -w "$file" ]; then
        exit_with_error 1 "Cannot write to commit message file: $file"
    fi
    
    # Check file size (prevent extremely large files)
    local file_size=$(wc -c < "$file" 2>/dev/null)
    if [ $? -ne 0 ]; then
        exit_with_error 1 "Cannot determine file size: $file"
    fi
    
    if [ "$file_size" -gt 10000 ]; then
        exit_with_error 1 "Commit message file too large (max 10KB): $file_size bytes"
    fi
}

# Sanitize branch name before processing
sanitize_branch_name() {
    local branch_name="$1"
    
    # Check if branch name is provided
    if [ -z "$branch_name" ]; then
        exit_with_error 1 "No branch name provided"
    fi
    
    # Remove any non-alphanumeric characters except dashes and underscores
    local sanitized=$(echo "$branch_name" | sed 's/[^a-zA-Z0-9_-]//g')
    
    # Check if sanitization removed everything
    if [ -z "$sanitized" ]; then
        exit_with_error 1 "Branch name contains only invalid characters: $branch_name"
    fi
    
    # Validate length (prevent extremely long names)
    if [ ${#sanitized} -gt 100 ]; then
        exit_with_error 1 "Branch name too long (max 100 characters): ${#sanitized} characters"
    fi
    
    # Validate format matches expected pattern
    if ! echo "$sanitized" | grep -q '^[a-zA-Z][a-zA-Z0-9_]*-[0-9][0-9]*-[a-zA-Z0-9_-]*$'; then
        exit_with_error 1 "Invalid branch name format: $sanitized (Expected: DevName-{issueNumber}-branch-summary)"
    fi
    
    echo "$sanitized"
}

# Secure issue number extraction
extract_issue_number() {
    local branch_name="$1"
    
    # Use parameter expansion instead of external commands where possible
    # Remove everything before first dash
    local after_first_dash="${branch_name#*-}"
    
    # Remove everything after second dash
    local issue_part="${after_first_dash%%-*}"
    
    # Validate it's purely numeric
    if ! echo "$issue_part" | grep -q '^[0-9][0-9]*$'; then
        exit_with_error 1 "Issue number is not numeric: $issue_part"
    fi
    
    # Validate reasonable range (1-999999)
    if [ "$issue_part" -lt 1 ] || [ "$issue_part" -gt 999999 ]; then
        exit_with_error 1 "Issue number out of range (1-999999): $issue_part"
    fi
    
    echo "$issue_part"
}

# Safe git operations
safe_git_operation() {
    local operation="$1"
    local result
    
    case "$operation" in
        "get_branch")
            result=$(git rev-parse --abbrev-ref HEAD 2>/dev/null)
            if [ $? -ne 0 ] || [ -z "$result" ]; then
                exit_with_error 1 "Failed to get current branch name"
            fi
            ;;
        *)
            exit_with_error 1 "Unknown git operation: $operation"
            ;;
    esac
    
    echo "$result"
}

# Get the commit message file
commit_msg_file="$1"

# Validate commit message file
validate_commit_msg_file "$commit_msg_file"

# Read the commit message
commit_msg=$(cat "$commit_msg_file")

# Skip if this is a merge commit (starts with "Merge")
if echo "$commit_msg" | grep -q "^Merge"; then
    log_info "Skipping merge commit"
    exit 0
fi

# Get current branch name using safe operation
branch_name=$(safe_git_operation "get_branch")

# Skip if we're in detached HEAD state
if [ "$branch_name" = "HEAD" ]; then
    log_info "Skipping detached HEAD state"
    exit 0
fi

# Skip if we're on main or develop branches
if [ "$branch_name" = "main" ] || [ "$branch_name" = "develop" ]; then
    log_info "Skipping protected branch: $branch_name"
    exit 0
fi

# Sanitize and validate branch name
sanitized_branch_name=$(sanitize_branch_name "$branch_name")

# Extract issue number using secure method
issue_number=$(extract_issue_number "$sanitized_branch_name")

# Create the issue prefix
issue_prefix="#$issue_number"

# Check if commit message already starts with the issue number
if echo "$commit_msg" | grep -q "^$issue_prefix "; then
    # Already has the correct prefix, no changes needed
    log_info "Commit message already has correct prefix: $issue_prefix"
    exit 0
fi

# Check if commit message starts with any issue number (wrong one)
if echo "$commit_msg" | grep -q "^#[0-9][0-9]* "; then
    # Has a different issue number, keep it as-is (developer override)
    log_info "Commit message has different issue number (developer override)"
    exit 0
fi

# Prepend the issue number to the commit message
log_info "Prepending issue number $issue_prefix to commit message"
echo "$issue_prefix $commit_msg" > "$commit_msg_file"

exit 0 