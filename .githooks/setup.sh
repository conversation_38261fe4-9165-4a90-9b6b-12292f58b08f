#!/bin/bash
#
# Git Hooks Setup Script for Unix/macOS/Git Bash
#
# This script configures Git to use the .githooks directory for all hooks
# instead of copying them to .git/hooks/
#
# Usage: ./.githooks/setup.sh
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# print_status prints an informational message in green color to stdout.
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# print_warning prints a warning message in yellow to stdout.
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# print_error prints an error message in red to stderr.
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This script must be run from within a Git repository."
    exit 1
fi

print_status "Configuring Git to use source-controlled hooks..."

# Set core.hooksPath to .githooks
if git config core.hooksPath .githooks; then
    print_status "Successfully configured core.hooksPath to .githooks"
else
    print_error "Failed to configure core.hooksPath"
    exit 1
fi

# Verify the configuration
hooks_path=$(git config core.hooksPath)
if [ "$hooks_path" = ".githooks" ]; then
    print_status "Configuration verified: Git will now use hooks from .githooks directory"
    echo
    print_status "Setup complete! Git hooks are now source-controlled and active."
    echo
    print_status "Available hooks:"
    print_status "- commit-msg: Automatically prepends issue numbers to commit messages"
    echo
    print_status "Branch naming pattern: DevName-{issueNumber}-branch-summary"
    print_status "Example: Joseph-42-add-login → commits get prefixed with '#42 '"
    echo
    print_status "To test security: run .githooks/test-security.sh"
else
    print_error "Configuration verification failed. Expected '.githooks', got '$hooks_path'"
    exit 1
fi

exit 0 