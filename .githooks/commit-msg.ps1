# Git commit-msg hook for automatic issue number prepending (PowerShell version)
# 
# This hook automatically prepends issue numbers to commit messages based on branch names.
# Branch naming pattern: DevName-{issueNumber}-branch-summary
# Example: Joseph-42-add-login → commit messages get prefixed with "#42 "
#
# The hook will:
# 1. Extract issue number from current branch name
# 2. Skip if commit message already starts with the issue number
# 3. <PERSON><PERSON> merge commits
# 4. Fail if no issue number can be extracted from branch name

param(
    [string]$CommitMsgFile
)

# Standardized error handling
$SCRIPT_NAME = "commit-msg-hook"
$LOG_PREFIX = "[$SCRIPT_NAME]"

function Write-LogError {
    param([string]$Message)
    Write-Host "$LOG_PREFIX ERROR: $Message" -ForegroundColor Red
}

function Write-LogWarning {
    param([string]$Message)
    Write-Host "$LOG_PREFIX WARNING: $Message" -ForegroundColor Yellow
}

function Write-LogInfo {
    param([string]$Message)
    Write-Host "$LOG_PREFIX INFO: $Message" -ForegroundColor Green
}

function Exit-WithError {
    param(
        [int]$ExitCode,
        [string]$Message
    )
    
    Write-LogError $Message
    Write-LogError "Hook execution failed. Commit aborted."
    exit $ExitCode
}

# Enhanced input validation for PowerShell with security fixes
function Test-CommitMsgFile {
    param([string]$FilePath)
    
    if ([string]::IsNullOrEmpty($FilePath)) {
        Exit-WithError 1 "No commit message file provided"
    }
    
    # Security: Validate file path to prevent directory traversal
    $resolvedPath = Resolve-Path -Path $FilePath -ErrorAction SilentlyContinue
    if (-not $resolvedPath) {
        Exit-WithError 1 "Commit message file does not exist: $FilePath"
    }
    
    if (-not (Test-Path $resolvedPath -PathType Leaf)) {
        Exit-WithError 1 "Path is not a file: $FilePath"
    }
    
    try {
        $fileInfo = Get-Item $resolvedPath -ErrorAction Stop
        if ($fileInfo.Length -gt 10KB) {
            Exit-WithError 1 "Commit message file too large (max 10KB): $($fileInfo.Length) bytes"
        }
    } catch {
        Exit-WithError 1 "Cannot access file information: $FilePath - $($_.Exception.Message)"
    }
}

# Enhanced branch name validation with stronger security
function Test-BranchName {
    param([string]$BranchName)
    
    if ([string]::IsNullOrEmpty($BranchName)) {
        Exit-WithError 1 "No branch name provided"
    }
    
    # Security: Check for command injection patterns BEFORE sanitization
    $dangerousPatterns = @(
        '`;', '`$', '`&', '`|', '`<', '`>', '``',
        ';', '&', '|', '<', '>', '$(',
        'rm ', 'del ', 'format ', 'shutdown ', 'reboot ',
        '`'  # Add standalone backtick detection
    )
    
    foreach ($pattern in $dangerousPatterns) {
        if ($BranchName -match [regex]::Escape($pattern)) {
            Exit-WithError 1 "Invalid branch name format: contains dangerous characters"
        }
    }
    
    # Security: More aggressive sanitization to prevent command injection
    # Remove ALL special characters that could be used for command injection
    $sanitized = $BranchName -replace '[^a-zA-Z0-9_-]', ''
    
    if ([string]::IsNullOrEmpty($sanitized)) {
        Exit-WithError 1 "Branch name contains only invalid characters: $BranchName"
    }
    
    if ($sanitized.Length -gt 100) {
        Exit-WithError 1 "Branch name too long (max 100 characters): $($sanitized.Length) characters"
    }
    
    # Enhanced format validation
    if ($sanitized -notmatch '^[a-zA-Z][a-zA-Z0-9_]*-\d+-[a-zA-Z0-9_-]*$') {
        Exit-WithError 1 "Invalid branch name format: $sanitized (Expected: DevName-{issueNumber}-branch-summary)"
    }
    
    return $sanitized
}

function Get-IssueNumber {
    param([string]$BranchName)
    
    # Use regex to extract issue number safely
    if ($BranchName -match '^[^-]+-(\d+)-') {
        $issueNumber = [int]$matches[1]
        
        # Validate reasonable range (1-999999)
        if ($issueNumber -lt 1 -or $issueNumber -gt 999999) {
            Exit-WithError 1 "Issue number out of range (1-999999): $issueNumber"
        }
        
        return $issueNumber
    } else {
        Exit-WithError 1 "Could not extract issue number from branch name: $BranchName"
    }
}

function Get-SafeBranchName {
    try {
        $branchName = git rev-parse --abbrev-ref HEAD 2>$null
        if ($LASTEXITCODE -ne 0 -or [string]::IsNullOrEmpty($branchName)) {
            Exit-WithError 1 "Failed to get current branch name"
        }
        return $branchName
    } catch {
        Exit-WithError 1 "Failed to get current branch name: $($_.Exception.Message)"
    }
}

# Validate commit message file
Test-CommitMsgFile $CommitMsgFile

# Read the commit message with enhanced error handling
try {
    $commitMsg = Get-Content -Path $CommitMsgFile -Raw -ErrorAction Stop
    if ([string]::IsNullOrEmpty($commitMsg)) {
        $commitMsg = ""
    } else {
        $commitMsg = $commitMsg.TrimEnd()
    }
} catch {
    Exit-WithError 1 "Failed to read commit message file: $($_.Exception.Message)"
}

# Skip if this is a merge commit (starts with "Merge")
if ($commitMsg -match "^Merge") {
    Write-LogInfo "Skipping merge commit"
    exit 0
}

# Get current branch name using safe method
$branchName = Get-SafeBranchName

# Skip if we're in detached HEAD state
if ($branchName -eq "HEAD") {
    Write-LogInfo "Skipping detached HEAD state"
    exit 0
}

# Skip if we're on main or develop branches
if ($branchName -eq "main" -or $branchName -eq "develop") {
    Write-LogInfo "Skipping protected branch: $branchName"
    exit 0
}

# Sanitize and validate branch name
$sanitizedBranchName = Test-BranchName $branchName

# Extract issue number using secure method
$issueNumber = Get-IssueNumber $sanitizedBranchName

# Create the issue prefix
$issuePrefix = "#$issueNumber"

# Check if commit message already starts with the issue number
if ($commitMsg -match "^$([regex]::Escape($issuePrefix)) ") {
    # Already has the correct prefix, no changes needed
    Write-LogInfo "Commit message already has correct prefix: $issuePrefix"
    exit 0
}

# Check if commit message starts with any issue number (wrong one)
if ($commitMsg -match "^#\d+ ") {
    # Has a different issue number, keep it as-is (developer override)
    Write-LogInfo "Commit message has different issue number (developer override)"
    exit 0
}

# Prepend the issue number to the commit message
Write-LogInfo "Prepending issue number $issuePrefix to commit message"
$newCommitMsg = "$issuePrefix $commitMsg"

try {
    Set-Content -Path $CommitMsgFile -Value $newCommitMsg -NoNewline -ErrorAction Stop
} catch {
    Exit-WithError 1 "Failed to write commit message file: $($_.Exception.Message)"
}

exit 0 