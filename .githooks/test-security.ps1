# Security Test Suite for Git Hooks (PowerShell version)
#
# This script tests the Git hooks against various security vulnerabilities
# and edge cases to ensure they handle malicious input safely.
#

param(
    [switch]$Verbose = $false
)

# Test counters
$global:TestsRun = 0
$global:TestsPassed = 0
$global:TestsFailed = 0
$global:FailedTests = @()

function Write-Header {
    Write-Host "================================" -ForegroundColor Blue
    Write-Host "  Git Hooks Security Test Suite" -ForegroundColor Blue
    Write-Host "================================" -ForegroundColor Blue
    Write-Host ""
}

function Write-Test {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor Yellow
}

function Write-Pass {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
    $global:TestsPassed++
}

function Write-Fail {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
    $global:TestsFailed++
    $global:FailedTests += $Message
}

function Write-Summary {
    Write-Host ""
    Write-Host "================================" -ForegroundColor Blue
    Write-Host "  Test Summary" -ForegroundColor Blue
    Write-Host "================================" -ForegroundColor Blue
    Write-Host "Total tests run: $global:TestsRun"
    Write-Host "Passed: $global:TestsPassed" -ForegroundColor Green
    Write-Host "Failed: $global:TestsFailed" -ForegroundColor Red
    
    if ($global:TestsFailed -gt 0) {
        Write-Host ""
        Write-Host "Failed tests:" -ForegroundColor Red
        foreach ($test in $global:FailedTests) {
            Write-Host "  ✗ $test" -ForegroundColor Red
        }
        Write-Host ""
        exit 1
    } else {
        Write-Host ""
        Write-Host "All tests passed!" -ForegroundColor Green
        Write-Host ""
        exit 0
    }
}

function Run-Test {
    param(
        [string]$TestName,
        [string]$BranchName,
        [string]$CommitMsg,
        [string]$ExpectedResult,  # "pass" or "fail"
        [string]$ExpectedOutput = ""  # Optional: expected output pattern
    )
    
    $global:TestsRun++
    Write-Test $TestName
    
    try {
        # Create test branch
        git checkout -b $BranchName -q 2>$null
        if ($LASTEXITCODE -ne 0) {
            # Branch might already exist, try to switch to it
            git checkout $BranchName -q 2>$null
            if ($LASTEXITCODE -ne 0) {
                # If still failing, try to delete and recreate
                git branch -D $BranchName -q 2>$null
                git checkout -b $BranchName -q 2>$null
            }
        }
        
        # Create test commit message file
        Set-Content -Path "test_commit_msg.txt" -Value $CommitMsg -NoNewline
        
        # Run the hook
        $output = & powershell.exe -ExecutionPolicy Bypass -File ".git/hooks/commit-msg.ps1" "test_commit_msg.txt" 2>&1
        $exitCode = $LASTEXITCODE
        
        # Convert output to string for pattern matching
        $outputString = $output | Out-String
        
        # Check result
        if ($ExpectedResult -eq "pass") {
            if ($exitCode -eq 0) {
                if ($ExpectedOutput -ne "" -and $outputString -notmatch $ExpectedOutput) {
                    Write-Fail "$TestName - Expected output not found: $ExpectedOutput"
                } else {
                    Write-Pass $TestName
                }
            } else {
                Write-Fail "$TestName - Expected pass but got exit code $exitCode"
                if ($Verbose) {
                    Write-Host "Output: $outputString" -ForegroundColor Gray
                }
            }
        } else {
            if ($exitCode -ne 0) {
                if ($ExpectedOutput -ne "" -and $outputString -notmatch $ExpectedOutput) {
                    Write-Fail "$TestName - Expected error output not found: $ExpectedOutput"
                    if ($Verbose) {
                        Write-Host "Output: $outputString" -ForegroundColor Gray
                    }
                } else {
                    Write-Pass $TestName
                }
            } else {
                Write-Fail "$TestName - Expected fail but got exit code $exitCode"
                if ($Verbose) {
                    Write-Host "Output: $outputString" -ForegroundColor Gray
                }
            }
        }
        
        # Clean up
        Remove-Item "test_commit_msg.txt" -ErrorAction SilentlyContinue
        git checkout master -q 2>$null
        if ($LASTEXITCODE -ne 0) {
            git checkout main -q 2>$null
        }
    } catch {
        Write-Fail "$TestName - Exception: $($_.Exception.Message)"
        git checkout master -q 2>$null
        if ($LASTEXITCODE -ne 0) {
            git checkout main -q 2>$null
        }
    }
}

# Create temporary test directory
$testDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
Push-Location $testDir

try {
    # Initialize a temporary git repository
    git init -q
    git config user.email "<EMAIL>"
    git config user.name "Test User"
    
    # Create an initial commit so we have a proper repository
    "# Test Repository" | Out-File -FilePath "README.md" -Encoding utf8
    git add README.md
    git commit -m "Initial commit" -q
    
    # Copy hooks to test directory
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    Copy-Item "$scriptDir\commit-msg.ps1" ".git\hooks\commit-msg.ps1"
    
    Write-Header
    
    # Test 1: Normal operation
    Write-Host "## Testing Normal Operation"
    Run-Test "Normal branch with valid issue number" "Joseph-42-add-login" "fix validation bug" "pass"
    
    # Test 2: Security vulnerability tests
    Write-Host ""
    Write-Host "## Testing Security Vulnerabilities"
    
    # Command injection attempts
    Run-Test "Command injection with semicolon" "DevName-123-branch; rm -rf /" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with backticks" "DevName-123-branch``malicious``" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with dollar" "DevName-123-branch`$(dangerous)" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with pipe" "DevName-123-branch|evil" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with ampersand" "DevName-123-branch&background" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with redirect" "DevName-123-branch>output" "test message" "fail" "Invalid branch name format"
    Run-Test "Command injection with input redirect" "DevName-123-branch<input" "test message" "fail" "Invalid branch name format"
    
    # Test 3: Input validation tests
    Write-Host ""
    Write-Host "## Testing Input Validation"
    
    # Branch name validation
    Run-Test "Branch name with only special chars" "!@#$%^&*()" "test message" "fail" "Invalid branch name format"
    Run-Test "Branch name too long" ("a" * 101 + "-123-branch") "test message" "fail" "Branch name too long"
    Run-Test "Invalid branch format - no dev name" "123-branch" "test message" "fail" "Invalid branch name format"
    Run-Test "Invalid branch format - no issue number" "DevName-branch" "test message" "fail" "Invalid branch name format"
    Run-Test "Invalid branch format - no dash" "DevName123branch" "test message" "fail" "Invalid branch name format"
    
    # Issue number validation
    Run-Test "Issue number zero" "DevName-0-branch" "test message" "fail" "Issue number out of range"
    Run-Test "Issue number too large" "DevName-9999999-branch" "test message" "fail" "Issue number out of range"
    Run-Test "Issue number non-numeric" "DevName-abc-branch" "test message" "fail" "Invalid branch name format"
    
    # Test 4: File operation tests
    Write-Host ""
    Write-Host "## Testing File Operations"
    
    # Test with non-existent file
    $global:TestsRun++
    Write-Test "Non-existent commit message file"
    git checkout -b "DevName-42-test" -q 2>$null
    if ($LASTEXITCODE -ne 0) {
        git checkout "DevName-42-test" -q 2>$null
    }
    $output = & powershell.exe -ExecutionPolicy Bypass -File ".git/hooks/commit-msg.ps1" "non_existent_file.txt" 2>&1
    $exitCode = $LASTEXITCODE
    $outputString = $output | Out-String
    if ($exitCode -ne 0 -and $outputString -match "Commit message file does not exist") {
        Write-Pass "Non-existent commit message file"
    } else {
        Write-Fail "Non-existent commit message file - Expected failure with specific error"
    }
    
    # Test with large file
    $global:TestsRun++
    Write-Test "Large commit message file"
    git checkout -b "DevName-43-test" -q 2>$null
    if ($LASTEXITCODE -ne 0) {
        git checkout "DevName-43-test" -q 2>$null
    }
    # Create a file larger than 10KB
    $largeContent = "x" * 11000
    Set-Content -Path "large_file.txt" -Value $largeContent
    $output = & powershell.exe -ExecutionPolicy Bypass -File ".git/hooks/commit-msg.ps1" "large_file.txt" 2>&1
    $exitCode = $LASTEXITCODE
    $outputString = $output | Out-String
    if ($exitCode -ne 0 -and $outputString -match "Commit message file too large") {
        Write-Pass "Large commit message file"
    } else {
        Write-Fail "Large commit message file - Expected failure with specific error"
    }
    Remove-Item "large_file.txt" -ErrorAction SilentlyContinue
    
    # Test 5: Edge cases
    Write-Host ""
    Write-Host "## Testing Edge Cases"
    
    Run-Test "Already prefixed message" "DevName-42-test" "#42 already prefixed" "pass" "already has correct prefix"
    Run-Test "Different issue number prefix" "DevName-42-test" "#123 different issue" "pass" "different issue number"
    Run-Test "Merge commit message" "DevName-42-test" "Merge pull request #8 from test" "pass" "Skipping merge commit"
    
    # Test 6: Protected branches
    Write-Host ""
    Write-Host "## Testing Protected Branches"
    
    Run-Test "Main branch" "main" "test message" "pass" "Skipping protected branch"
    Run-Test "Develop branch" "develop" "test message" "pass" "Skipping protected branch"
    
    # Test 7: Special characters in commit messages
    Write-Host ""
    Write-Host "## Testing Special Characters in Commit Messages"
    
    Run-Test "Commit message with quotes" "DevName-42-test" "fix `"quoted`" text" "pass"
    Run-Test "Commit message with apostrophes" "DevName-42-test" "fix user's issue" "pass"
    Run-Test "Commit message with newlines" "DevName-42-test" "fix issue`nwith newline" "pass"
    
    Write-Summary
    
} finally {
    # Clean up
    Pop-Location
    Remove-Item $testDir -Recurse -Force -ErrorAction SilentlyContinue
} 