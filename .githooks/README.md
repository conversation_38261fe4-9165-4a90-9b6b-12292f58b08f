# Git Hooks for Automatic Issue Number Prepending

This directory contains Git hooks that automatically prepend issue numbers to commit messages based on branch names

## Overview

The system automatically extracts issue numbers from branch names and prepends them to commit messages, ensuring consistent commit message formatting across the team.

**Branch naming pattern**: `DevName-{issueNumber}-branch-summary`

**Example**: 
- Branch: `Joseph-42-add-login`
- Commit message: `"fix validation bug"` → `"#42 fix validation bug"`

## How It Works

### Installation Process
**Important**: This system requires **one-time setup** per developer to configure Git to use source-controlled hooks.

1. **Developer runs setup script once**:
   - **Windows**: `.\.githooks\setup.ps1`
   - **Unix/macOS/Git Bash**: `./.githooks/setup.sh`

2. **Setup script configures Git**:
   - Runs `git config core.hooksPath .githooks`
   - Git now executes hooks directly from `.githooks` directory
   - No copying to `.git/hooks` required

3. **After setup**:
   - Every commit → `commit-msg` hook automatically processes messages
   - Hook updates are automatic when `.githooks/` files change
   - New hooks added to `.githooks/` work immediately

### Automatic Updates
- Hooks are executed directly from the source-controlled `.githooks` directory
- When `.githooks/` files are updated via git pull, changes take effect immediately
- No maintenance scripts needed
- **Zero additional setup required** after initial configuration

### Commit Message Processing
The `commit-msg` hook:
1. Extracts the issue number from the current branch name
2. Skips processing if the commit message already starts with the correct issue number
3. Skips merge commits (e.g., "Merge pull request #8 from...")
4. Skips main/develop branches
5. Fails the commit if no issue number can be extracted

## Installation Requirements

### Why Setup is Required
- **Git Security**: Git doesn't automatically execute hooks from repositories for security reasons
- **One-time Configuration**: After setup, hooks execute directly from source control
- **Cross-platform**: Works on Windows, macOS, and Linux with single configuration

### When to Re-run Setup
You need to run the setup script again in these situations:
- **Fresh clone**: When you clone the repository to a new location
- **New machine**: When setting up the repository on a different computer
- **Reset Git config**: If you've reset your Git configuration or run `git config --unset core.hooksPath`
- **Different user**: Each Git user profile needs to run setup once per repository

You do **NOT** need to re-run setup for:
- Regular `git pull` updates
- Switching branches
- Hook file changes in `.githooks/`
- Git updates or upgrades

### Installation Steps

#### Option 1: Quick Setup (Recommended)
```bash
# From repository root
.\.githooks\setup.ps1    # Windows PowerShell
# OR
./.githooks/setup.sh     # Unix/macOS/Git Bash
```

## Files

| File | Purpose | Platform |
|------|---------|----------|
| `commit-msg` | Main hook script | Unix/macOS/Git Bash |
| `commit-msg.ps1` | PowerShell version | Windows PowerShell |
| `setup.sh` | Setup script | Unix/macOS/Git Bash |
| `setup.ps1` | Setup script | Windows PowerShell |
| `test-security.sh` | Security test suite | Unix/macOS/Git Bash |
| `test-security.ps1` | Security test suite | Windows PowerShell |
| `config` | Hook configuration settings | All platforms |
| `README.md` | This documentation | All platforms |

## Branch Naming Rules

**Supported patterns**:
- `Joseph-42-add-login`
- `Sarah-123-fix-bug`
- `DevName-999-any-branch-description`

**Unsupported patterns**:
- `feature-42-add-login` (no dev name)
- `Joseph-add-login` (no issue number)
- `42-add-login` (no dev name)

## Behavior Examples

### Normal Operation
```bash
# Branch: Joseph-42-add-login
git commit -m "fix validation bug"
# Result: "#42 fix validation bug"
```

### Already Prefixed
```bash
# Branch: Joseph-42-add-login
git commit -m "#42 fix validation bug"
# Result: "#42 fix validation bug" (no change)
```

### Developer Override
```bash
# Branch: Joseph-42-add-login
git commit -m "#123 fix different issue"
# Result: "#123 fix different issue" (keeps developer's choice)
```

### Merge Commits
```bash
# Branch: Joseph-42-add-login
# Merge commit: "Merge pull request #8 from CorellianSoftwareInc/..."
# Result: No change (merge commits are skipped)
```

### Main/Develop Branches
```bash
# Branch: main or develop
git commit -m "fix bug"
# Result: "fix bug" (no prefix added)
```

## System Architecture

### Hook Relationships
```
Developer runs: setup.ps1 or setup.sh
                      ↓
        Configures: git config core.hooksPath .githooks
                      ↓
Every commit ──→ commit-msg hook ──→ Processes message
                      ↓
Git pulls ──→ Updated hooks ──→ Automatically active
```

### Source Control Benefits
- **All hooks are version controlled** in `.githooks/` directory
- **Updates are distributed automatically** via git pull
- **New hooks work immediately** for all configured developers
- **No maintenance scripts needed**

## Manual Setup

### Windows PowerShell
```powershell
cd C:\path\to\your\repo
.\.githooks\setup.ps1
```

### Unix/macOS
```bash
cd /path/to/your/repo
./.githooks/setup.sh
```

### Verification
After setup, verify configuration:
```bash
git config core.hooksPath
# Should output: .githooks
```

## Troubleshooting

### Hook Not Working
1. **Check if core.hooksPath is configured**:
   ```bash
   git config core.hooksPath
   # Should output: .githooks
   ```
2. **Re-run setup script if needed**
3. **Ensure your branch follows the naming pattern**

### Error: "Could not extract issue number"
- Check your branch name follows the pattern: `DevName-{issueNumber}-branch-summary`
- Ensure the issue number is numeric
- **Good Example**: `Joseph-42-add-login`
- **Bad Example**: `Joseph-abc-add-login`

### Error: "Invalid branch name format"
- Branch names must start with a letter
- Only alphanumeric characters, dashes, and underscores are allowed
- Must follow pattern: `DevName-{issueNumber}-branch-summary`
- Examples:
  - ✅ `Joseph-42-add-login`
  - ✅ `Sarah_123-fix-bug`
  - ❌ `123-invalid-start`
  - ❌ `Joseph@42-special-chars`

### Error: "Branch name contains dangerous characters"
- The hook detected potentially malicious characters in the branch name
- This is a security feature to prevent command injection
- Use only letters, numbers, dashes, and underscores
- **Good Example**: `Joseph-42-add-login`
- **Bad Example**: `Joseph-42-add;rm-rf` (contains dangerous semicolon)

### Error: "Branch name too long"
- Branch names are limited to 100 characters maximum
- Consider using shorter, more descriptive names
- **Example**: `Dev-42-short-desc` instead of `Developer-42-very-long-branch-name-that-exceeds-limits`

### Error: "Issue number out of range"
- Issue numbers must be between 1 and 999999
- Check your issue tracking system for the correct number
- Examples:
  - ✅ `Joseph-42-branch` (valid range)
  - ❌ `Joseph-0-branch` (too small)
  - ❌ `Joseph-9999999-branch` (too large)

### Error: "Commit message file too large"
- Commit messages are limited to 10KB maximum
- Consider breaking large commits into smaller, focused changes
- Use bullet points instead of long paragraphs

### Error: "Branch name contains only invalid characters"
- The branch name has no valid characters after sanitization
- Ensure your branch name contains at least some alphanumeric characters
- **Example**: `!@#$%` becomes empty after sanitization 

### Permission Issues (Unix/macOS)
```bash
chmod +x .git/hooks/commit-msg
chmod +x .git/hooks/post-checkout
```

### Windows PowerShell Execution Policy
If you get execution policy errors:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## Testing

### Manual Testing
To test the hook without making actual commits:

```bash
# Test the commit-msg hook directly
echo "test commit message" > test_commit_msg.txt
.git/hooks/commit-msg test_commit_msg.txt
cat test_commit_msg.txt
rm test_commit_msg.txt
```

### Security Testing
Run comprehensive security tests:

**Windows PowerShell**:
```powershell
.\.githooks\test-security.ps1
```

**Unix/macOS**:
```bash
./.githooks/test-security.sh
```

The security tests validate:
- Command injection prevention
- Input validation and sanitization
- File operation security
- Error handling robustness
- Edge cases and malicious input

## Development

### Updating Hooks
1. **Edit the hooks** in `.githooks/` directory
2. **Changes take effect immediately** (no reinstallation needed)
3. **All developers get updates** when they pull the changes

### Adding New Hooks
1. **Create the new hook** in `.githooks/` directory
2. **Make it executable**: `chmod +x .githooks/new-hook`
3. **Update this README** to document the new hook
4. **Commit and push** - all developers get the new hook automatically
5. **No additional setup required** for existing configured developers

### Configuration
Edit `.githooks/config` to modify:
- Protected branches (skip issue number prepending)
- Issue number validation ranges
- File size limits
- Security settings

## Compatibility

### Git Clients
- **Git CLI** (all platforms)
- **GitHub Desktop**
- **VS Code Git integration**
- **JetBrains IDEs**
- **SourceTree**
- **GitKraken**
- **Other GUI Git clients** (most should work)

### Platforms
- **Windows 10/11** (PowerShell 5.1+)
- **macOS** (Bash/Zsh)
- **Linux** (Bash/Zsh)
- **Git Bash on Windows**

## Security

### Security Features
The hooks have been designed with security as a primary concern:

#### Input Validation
- **Branch name sanitization**: Removes all special characters except alphanumeric, dashes, and underscores
- **Command injection prevention**: Detects and blocks dangerous character patterns
- **Format validation**: Ensures branch names match the expected pattern `DevName-{issueNumber}-branch-summary`
- **Length limits**: Branch names are limited to 100 characters maximum
- **Issue number validation**: Issue numbers must be numeric and within range (1-999999)

#### File Operation Security
- **File existence validation**: Verifies commit message files exist before processing
- **Path traversal prevention**: Uses resolved paths to prevent directory traversal attacks
- **Permission checks**: Ensures files are readable and writable
- **Size limits**: Commit message files are limited to 10KB maximum
- **Error handling**: All file operations include comprehensive error handling

#### Command Injection Prevention
- **Input sanitization**: All branch names are sanitized before processing
- **Dangerous pattern detection**: Actively scans for command injection patterns
- **Regex validation**: Strict regex patterns prevent malicious input
- **No dynamic command execution**: No user input is ever executed as shell commands

#### What the hooks DON'T do:
- Access external networks
- Read sensitive files outside the commit message
- Modify repository history
- Execute arbitrary code from commit messages
- Store or transmit any data

### Security Testing
Comprehensive security test suites are included:
- **Windows**: Run `.githooks/test-security.ps1`
- **Unix/macOS**: Run `.githooks/test-security.sh`

The test suites validate:
- Command injection prevention
- Input validation and sanitization
- File operation security
- Error handling robustness
- Edge cases and malicious input

### Reporting Security Issues
If you discover a security vulnerability in these hooks:

1. **Do not create a public issue**
2. **Contact the development team directly**
3. **Provide detailed reproduction steps**
4. **Include the output of the security test suite**

## Migration to Lefthook

### Current Status
This repository is in the process of migrating to **Lefthook** for improved hook management:

- **Current**: Manual installation required
- **Future**: Automatic installation via `pnpm install`
- **Timeline**: See `docs/future-projects/LEFTHOOK_MIGRATION_IMPLEMENTATION_PLAN.md`

### Benefits of Migration
- **Cross-platform compatibility** without manual setup
- **Workspace-aware execution** (only run relevant checks)
- **Parallel execution** for faster performance
- **Declarative configuration** in YAML format
- **Zero bash dependency** on Windows

### For Developers
- **Current system will continue to work** during migration
- **No action required** until migration is complete
- **Migration will be announced** with updated instructions 