# Relay Compliance Peer-Review Report

**Date:** 24 Jul 2025  
**Reviewer:** Cascade AI  
**Scope:** Validation of `Hot_Chocolate_Relay_Gap_Analysis_Report.md` _and_ `Relay_Implementation_Plan.md` against Hot Chocolate v15 Relay guidelines (<https://chillicream.com/docs/hotchocolate/v15/defining-a-schema/relay>) and Corellian Software coding standards.

---

## 1. Executive Summary

Overall, the **gap analysis is accurate** and the **implementation plan addresses the highest-impact items** (query field, node standardisation, DataLoader usage).  
However we identified a handful of **incorrect assumptions, missing edge-cases and latent risks** that should be resolved before implementation begins.  
With the improvements listed below the team can realistically achieve **full Relay compliance with low migration risk**.

| Area | Compliance After Plan | Key Missing / Risk |
|------|----------------------|--------------------|
| Global IDs & Node | ✅ | Complex ID serialisation edge-cases |
| Connections | ✅ | Consistent page sizes & naming in ALL queries |
| Mutation Conventions | ⚠️ | Partial error standardisation, inconsistent `clientMutationId` |
| `query` field in payloads | ⚠️ | Available only in HC >= 12; 15.1.7 ok — **BUT order of extension methods in `Program.cs` matters** |
| DataLoader Optimisation | ⚠️ | Only some resolvers updated; N+1 possible in deep graph paths |
| Subscriptions | ❌ | Not covered; package & server config absent |
| Defer/Stream | ❌ | Enabled flag present but no concrete usage |

---

## 2. Validation of Major Assumptions

| # | Assumption | Validation |
|---|------------|-----------|
| A1 | `.AddQueryFieldToMutationPayloads()` exists in HC v15.1.7 | ✅ Confirmed. The method is available since v12. |
| A2 | Adding the extension after `.AddGlobalObjectIdentification()` is sufficient | ⚠️ Must be **after** `AddGlobalObjectIdentification()` **and** *before* any `.ModifyRequestOptions()` that might transform schema; ensure order preserved. |
| A3 | Static abstract generic method `Parse<T>` in `IComplexId` compiles under .NET 9 | ✅ C# 12 static abstract members allow this; however **generic static members** require C# 12+, so upgrade project `LangVersion` if still on C# 11 preview. |
| A4 | Timeline (3-5 days) is realistic | ⚠️ Ambitious; adding DataLoaders & tests alone often >3 days. Suggest 6-8 dev-days or parallelise. |
| A5 | No DB migrations required | ✅ Plan is code-only; verified. |
| A6 | Front-end unaffected | ⚠️ GraphQL-Code-Gen may re-generate types; `query` field addition could break optimistic update typings. Coordinate with FE. |

---

## 3. Detailed Findings & Recommendations

### 3.1 Global Identifiers & Node Interface

* ✔ Node pattern solid; plan correctly adds missing `[ID]` attributes.  
* ❗ **Complex ID** parser must also register custom `INodeIdSerializer` if you intend to reference complex IDs via global IDs. Only adding type converters is insufficient — add:
  ```csharp
  .AddNodeIdSerializer<CustomNodeIdSerializer>()
  ```
  or implement `IHoneywellIdSerializer` replacement.

### 3.2 Mutation Payload `query` Field

* Correct extension method chosen.
* **Risk:** If any mutation defines a custom payload _class_ that _already_ contains a `query` property, schema build will fail with duplicate field. Run schema validation tests.
* Ensure GraphQL operation complexity limits take new nested queries into account.

### 3.3 Mutation Conventions & Error Handling

* Base class pattern good; unify under single namespace (`backend.Types.Payloads`).
* Recommend **GraphQLError** list instead of `string` for richer error codes.
* Consider adding **`extensions` map** for i18n / code parsing.

### 3.4 DataLoader Optimisation

* Only top-level resolvers updated.  
  ➜ Also convert _field resolvers inside object types_ (e.g. `TimeSheet.payStubs`) to use loaders.
* Register `EmployeeLoader` & `AgreementLoader` _before_ `.AddProjections()` to avoid interception order issue.

### 3.5 Connections & Pagination

* Constants welcome; but `DefaultPageSize = 400` may cause bloat. Suggest 50-100 for typical UIs and allow FE override.
* Explicit `ConnectionName` is optional; over-specification can clutter schema. Apply only where ambiguous.

### 3.6 Complex IDs

* Interface design correct.  
* Ensure `Equals`/`GetHashCode` implemented if using `record struct` (they already are).  
* Add **unit tests** for serialization roundtrip.

### 3.7 Subscriptions (Not in Plan)

* Gap analysis flagged absence, implementation plan omits.  
  ➜ Add `HotChocolate.Subscriptions` package and websocket middleware.  
  ➜ Define at least one sample subscription (`onTimeSheetUpdated`).

### 3.8 Defer/Stream Usage

* Server opts-in via `AllowDefer = true` but no schema examples. Provide **example connection query** with `@stream` for large payroll exports.

### 3.9 Testing Strategy

* Good coverage suggestions.  
* **Performance test** target (<500 ms for 100 nodes) may be optimistic on CI; add tolerance or run locally.

### 3.10 Documentation

* Implementation guide helpful.  
* Add section on **subscription usage** & **complex ID authoring**.

---

## 4. Risk Assessment

| Risk | Likelihood | Impact | Mitigation |
|------|------------|--------|-----------|
| Schema breaking (`query` field) | Med | High | Version schema, notify FE, run code-gen beforehand |
| DataLoader bug introduces stale cache | Low | Med | Add `CancellationToken` & unit tests |
| Static abstract generic build errors on CI | Med (older SDK) | High | Pin SDK 9-preview 5+ in global.json |
| Performance regression due to large default page size | Med | Med | Reduce default, monitor traces |
| Subscription rollout scope creep | Low | Med | Phase as optional

---

## 5. Recommended Adjustments to Implementation Plan

1. **Add Phase 0 – Infrastructure**  
   • Upgrade to `Microsoft.NET.Sdk` **9.0.100-preview.5** (or latest)  
   • Add `HotChocolate.Subscriptions` package + websocket config.
2. **Split Phase 1 into 1a (Query Field) and 1b (Error Standardisation)** to unblock FE earlier.
3. **Lower default page size** to 100; keep 4000 max.
4. **Extend DataLoader phase** to cover nested resolvers.
5. **Add CI task** that runs `dotnet hc schema validate` to catch duplicate fields.
6. **Update FE hand-off checklist** to include new `query` selection sets.

---

## 6. Conclusion

The proposed plan positions the backend well for full Relay compliance, but requires **subscription support, stricter error conventions, and expanded DataLoader coverage** to close all gaps. Addressing the recommendations above will reduce long-term maintenance cost and ensure seamless integration with Relay-powered frontend clients.

---

## 7. References

* Hot Chocolate Docs – Relay: <https://chillicream.com/docs/hotchocolate/v15/defining-a-schema/relay>
* GraphQL RFC – Global Object Identification <https://relay.dev/graphql/objectidentification.htm>
