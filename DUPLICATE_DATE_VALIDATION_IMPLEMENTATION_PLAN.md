# Duplicate Date Validation Implementation Plan

## Background

### Problem Statement
Currently, the CSV upload functionality allows users to upload rows for employees that already have timesheet detail entries for the same work date. This creates duplicate entries where an employee has multiple PayStubDetail records for the same date, which is incorrect business logic.

### Current Behavior
- Users can upload CSV rows for any employee regardless of existing timesheet details
- No validation prevents duplicate employee+date combinations
- This leads to data integrity issues and confusing UI

### Desired Behavior
- CSV upload should validate that no employee+date combination already exists in the timesheet
- If a duplicate is found, show validation warning to the user
- Allow partial uploads (valid rows proceed, invalid rows show warnings)
- Only consider it a duplicate if the existing detail has actual hours (not blank rows)

### Business Rules
1. An employee can work multiple days in a pay period (multiple CSV rows for same employee)
2. An employee cannot have multiple entries for the same work date
3. Blank timesheet detail rows (no hours) should not count as duplicates
4. Validation should happen during CSV upload, not after server processing

## Technical Architecture

### Data Flow
```
TimesheetDetail (has payStubNodes)
    ↓
TimesheetToolbar
    ↓
UploadTimeSheetWithEmployeeData
    ↓
UploadTimeSheetContent (needs existing details for validation)
    ↓
validateCsvData (new duplicate date validation)
```

### Key Data Structures

#### Existing Timesheet Details (from PayStubTable fragments)
```typescript
interface PayStubDetail {
    id: string;
    payStubId: string;
    workDate: string; // YYYY-MM-DD format
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    // ... other fields
}

interface PayStub {
    id: string;
    employeeId: string;
    details: PayStubDetail[];
    // ... other fields
}
```

#### CSV Row Data (from validation)
```typescript
interface RawCsvRow {
    [NormalizedCsvHeader.DATE]: string; // MM/DD/YYYY or YYYY-MM-DD
    [NormalizedCsvHeader.SSN]: string;
    [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: string;
    // ... other fields
}
```

## Implementation Steps

### Step 1: Pass Existing Timesheet Details to UploadTimeSheetContent

**Files to modify:**
- `ui/src/components/TimesheetDetail/TimesheetDetail.tsx`
- `ui/src/components/TimesheetDetail/TimesheetToolbar.tsx`
- `ui/src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheetWithEmployeeData.tsx`
- `ui/src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheetContent.tsx`

**Changes:**
1. In `TimesheetDetail.tsx`, extract existing timesheet details from `payStubNodes`
2. Pass this data through the component hierarchy
3. Add new prop to `UploadTimeSheetContentProps` interface

**Code changes:**

```typescript
// TimesheetDetail.tsx - Extract existing details
const existingTimesheetDetails = useMemo(() => {
    return payStubNodes.flatMap(payStub =>
        payStub.details.map(detail => ({
            employeeId: payStub.employeeId,
            workDate: detail.workDate,
            hasHours: (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0) > 0
        }))
    );
}, [payStubNodes]);

// Pass to child components
<TimeSheetGrid
    // ... existing props
    existingTimesheetDetails={existingTimesheetDetails}
/>
```

### Step 2: Update Validation Function Signature

**Files to modify:**
- `ui/src/components/TimesheetDetail/UploadTimeSheet/utils/dataValidation.ts`

**Changes:**
1. Add new parameter to `validateCsvData` function
2. Add TypeScript interface for existing details

```typescript
interface ExistingTimesheetDetail {
    employeeId: string;
    workDate: string;
    hasHours: boolean;
}

export const validateCsvData = (
    parsedData: RawCsvRow[],
    payPeriodEndDate?: string | null,
    existingTimesheetDetails?: ExistingTimesheetDetail[]
): {
    validRows: RawCsvRow[];
    rowErrors: RowError[];
    headerErrors: HeaderError[];
}
```

### Step 3: Add Duplicate Date Validation Logic

**Files to modify:**
- `ui/src/components/TimesheetDetail/UploadTimeSheet/utils/dataValidation.ts`

**Changes:**
1. Add helper function to check for duplicates
2. Add validation logic after employee identification validation
3. Use existing `DUPLICATE_ENTRY` error code

```typescript
// Helper function to check for duplicate dates
const isDuplicateDate = (
    rowSSN: string | undefined,
    rowEmployeeId: string | undefined,
    rowDate: string,
    existingDetails: ExistingTimesheetDetail[],
    employeeData: ProcessedEmployeeData[]
): boolean => {
    // Convert CSV date to YYYY-MM-DD format
    const normalizedDate = normalizeDateToISO(rowDate);

    // Find matching employee
    const matchingEmployee = employeeData.find(emp =>
        (rowSSN && emp.ssn === rowSSN) ||
        (rowEmployeeId && emp.externalEmployeeId === rowEmployeeId)
    );

    if (!matchingEmployee) return false;

    // Check for existing detail with same employee and date
    return existingDetails.some(detail =>
        detail.employeeId === matchingEmployee.id &&
        detail.workDate === normalizedDate &&
        detail.hasHours // Only count as duplicate if existing detail has hours
    );
};

// Add to validation loop
if (existingTimesheetDetails && existingTimesheetDetails.length > 0) {
    const isDuplicate = isDuplicateDate(
        row[NormalizedCsvHeader.SSN],
        row[NormalizedCsvHeader.EXTERNALEMPLOYEEID],
        row[NormalizedCsvHeader.DATE],
        existingTimesheetDetails,
        employeeData // Need to pass this from UploadTimeSheetContent
    );

    if (isDuplicate) {
        rowErrorsForThisRow.push({
            type: 'row',
            rowIndex,
            message: 'Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.',
            code: 'DUPLICATE_ENTRY'
        });
    }
}
```

### Step 4: Update Error Categorization

**Files to modify:**
- `ui/src/components/TimesheetDetail/UploadTimeSheet/utils/errorCategorization.ts`

**Changes:**
1. Add handling for `DUPLICATE_ENTRY` error code
2. Create appropriate error category and instructions

```typescript
case 'DUPLICATE_ENTRY':
    return {
        category: 'Duplicate Date Errors',
        summary: `${errorCount} duplicate date error${errorCount > 1 ? 's' : ''} in your CSV file`,
        context: 'Some employees already have timesheet entries for the dates in your CSV file.',
        details: errors.map(error => `Row ${error.rowIndex}: ${error.message}`),
        instructions: [
            'Open your CSV file and check the "Date" column',
            'Remove or update rows where employees already have entries for those dates',
            'Save the file and try uploading again'
        ]
    };
```

### Step 5: Update UploadTimeSheetContent

**Files to modify:**
- `ui/src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheetContent.tsx`

**Changes:**
1. Add new prop for existing timesheet details
2. Pass employee data to validation function
3. Update validation function call

```typescript
interface UploadTimeSheetContentProps {
    // ... existing props
    existingTimesheetDetails?: ExistingTimesheetDetail[];
}

// In handleFileUpload function
const { validRows, rowErrors, headerErrors } = validateCsvData(
    parseResult.data,
    timeSheetData.payPeriodEndDate,
    existingTimesheetDetails
);
```

## Testing Scenarios

### Test File: `test-scenario5-duplicate-dates.csv`
Create a test file with the following scenarios:

1. **Valid rows**: Employee with no existing entries for those dates
2. **Duplicate dates**: Employee with existing entries for same dates
3. **Mixed scenario**: Some valid, some duplicate dates
4. **Blank existing details**: Employee has blank entries (should not count as duplicates)

### Expected Results:
- Scenario 1: All rows upload successfully
- Scenario 2: All rows show duplicate date validation warnings
- Scenario 3: Valid rows proceed, duplicate rows show warnings
- Scenario 4: Rows should be allowed (blank existing details don't count as duplicates)

## Error Messages

### Validation Warning Dialog
```
Duplicate Date Errors
3 duplicate date errors in your CSV file:
Some employees already have timesheet entries for the dates in your CSV file.

• Row 2: Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.
• Row 4: Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.
• Row 6: Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.

To fix these warnings:
1. Open your CSV file and check the "Date" column
2. Remove or update rows where employees already have entries for those dates
3. Save the file and try uploading again
```

## Implementation Notes

### Date Format Handling
- CSV dates can be in MM/DD/YYYY or YYYY-MM-DD format
- Existing timesheet details use YYYY-MM-DD format
- Need to normalize CSV dates to YYYY-MM-DD for comparison

### Employee Matching
- Match by SSN if both CSV row and existing employee have SSN
- Match by Employee ID if both CSV row and existing employee have Employee ID
- Use the same matching logic as existing employee lookup

### Performance Considerations
- Existing timesheet details are already loaded in memory
- Validation happens client-side, no additional server calls needed
- Employee matching uses existing employee data from parent query

### Error Handling
- Use existing `DUPLICATE_ENTRY` error code for consistency
- Follow existing validation warning pattern
- Allow partial uploads (valid rows proceed, invalid rows show warnings)

## Files Summary

### Files to Modify:
1. `ui/src/components/TimesheetDetail/TimesheetDetail.tsx` - Extract existing details
2. `ui/src/components/TimesheetDetail/TimesheetToolbar.tsx` - Pass data down
3. `ui/src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheetWithEmployeeData.tsx` - Pass data down
4. `ui/src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheetContent.tsx` - Add prop and pass to validation
5. `ui/src/components/TimesheetDetail/UploadTimeSheet/utils/dataValidation.ts` - Add duplicate validation logic
6. `ui/src/components/TimesheetDetail/UploadTimeSheet/utils/errorCategorization.ts` - Add error handling

### Files to Create:
1. `test-scenario5-duplicate-dates.csv` - Test file for duplicate date scenarios

### Type Definitions to Add:
```typescript
interface ExistingTimesheetDetail {
    employeeId: string;
    workDate: string;
    hasHours: boolean;
}
```

## Success Criteria

1. ✅ CSV upload shows validation warnings for duplicate employee+date combinations
2. ✅ Only existing details with actual hours count as duplicates
3. ✅ Partial uploads work correctly (valid rows proceed, invalid rows show warnings)
4. ✅ Clear error messages guide users to fix the issues
5. ✅ No performance impact (uses existing data, no additional server calls)
6. ✅ Maintains existing validation behavior for other error types
