# Implementation Plan: Require Saving Unsaved Changes **Before** CSV Upload

> **Audience**&nbsp;— front-end engineers new to this codebase, UX designer, QA.
>
> **Goal**&nbsp;— enforce a single source-of-truth workflow where _all_ manual edits are persisted to the server **before** a bulk CSV upload starts. This removes the need to merge client-side drafts with CSV data and eliminates several race conditions.

---

## 1. Background & Problem Statement

The timesheet UI allows users to:

1. Make **manual** edits that live temporarily in the `TimesheetUIStore` (Zustand) as **drafts**.
2. Perform **bulk** uploads via CSV, which call `bulkAddPayStubsWithModify`.

Today the upload path must juggle three data sources:

| Source | Volatility | Example merge concern |
|--------|------------|-----------------------|
| Relay store (server) | Stable | Existing pay-stubs used for duplicate detection |
| Zustand drafts | Unsaved, may diverge | Draft hours may clash with CSV rows |
| CSV rows | Incoming | Must avoid creating duplicates against the union of the above |

This complexity forces
* multi-step transformations (`dataTransformationExtended.ts`),
* edge-case handling in optimistic updaters, and
* brittle duplicate-prevention logic.

The recommendation is to **ban uploads while drafts exist**. Users must either **Save** (persist to server) or **Discard** edits first. The CSV logic then interacts with one authoritative snapshot.

---

## 2. Desired UX Flow

```mermaid
graph TD
    A[User clicks "Upload"] --> B{Are there unsaved edits?}
    B -- No --> C(Open Upload Dialog)
    B -- Yes --> D[Prompt Modal]
    D --> |Save & Continue| E(Save draft via ModifyTimeSheetMutation)
    E --> C
    D --> |Discard & Continue| F(Clear drafts)
    F --> C
    D --> |Cancel| G(Return to page)
```

**Copy proposal**
```
You have unsaved changes. Please save or discard them before uploading a CSV.
```

Buttons: “Save & Continue” (primary), “Discard & Continue”, “Cancel”.

---

## 3. Architectural Impact

| Area | Before | After |
|------|--------|-------|
| `UploadTimeSheetContent.tsx` | Upload dialog accessible anytime | Wrapped by `ensureSavedState()` guard |
| `dataTransformationExtended.ts` | Merges draft + server pay-stubs | Operates **only** on server data; sections merging `draftMap` can be deleted |
| `existingPayStubMap.ts` | Accepts map of drafts | No draft logic needed |
| Zustand store selectors | Merge server + drafts in multiple hooks | Hooks can drop draft merging for CSV path |
| Optimistic updaters | Handle collision with draft IDs | Simplified; only worry about new CSV inserts |

---

## 4. Implementation Steps

### 4.1 Draft-Detection Helper

Add to `ui/src/store/timesheetUIStore/helpers.ts`:
```ts
export function hasUnsavedChanges(state: TimesheetUIStore): boolean {
  return state.payStubDrafts.size > 0 || state.detailDrafts.size > 0;
}
```

### 4.2 Modal Component

* **File**: `ui/src/components/TimesheetDetail/UploadTimeSheet/UnsavedChangesPrompt.tsx`
* Spectrum `<DialogContainer>` wrapper with three buttons.
* Props: `onSaveContinue`, `onDiscardContinue`, `onCancel`.

### 4.3 Guard Function in `UploadTimeSheetContent.tsx`

```ts
const handleUploadClick = useCallback(() => {
  const storeState = useTimesheetUIStore.getState();
  if (!hasUnsavedChanges(storeState)) {
    openUploadDialog();
    return;
  }
  setShowPrompt(true);
}, []);
```

* Replace existing `<Button onPress={openUploadDialog}>` with `handleUploadClick`.
* Implement `onSaveContinue`:
  1. Call `modifyTimeSheetMutation` (existing helper).
  2. Await completion / error.
  3. On success ➜ `openUploadDialog()`.
* Implement `onDiscardContinue`:
  1. `useTimesheetUIStore.getState().clearAllDrafts();` *(write helper if lacking)*.
  2. `openUploadDialog()`.

### 4.4 Remove Draft-Merge Logic

1. **`dataTransformationExtended.ts`**
   * Delete `transformWithExistingData` branches that read from draft maps.
   * Simplify `TransformResultWithExisting` accordingly.
2. **`existingPayStubMap.ts`**
   * Strip parameters / conditions linked to drafts.
3. **Hooks depending on merged view**
   * Search for `convertRawToProcessed` & draft merging; adjust.

### 4.5 Optimistic Updaters

* Remove fallback that checks `store.get(draftId)` etc.
* Keep ConnectionHandler insertion for new CSV edges.

### 4.6 Validation & UI Clean-up

* Ensure validation calls (row highlights) use server data only.
* Update tests.

### 4.7 Documentation

* Amend **Timesheet-Data-Flow-Architecture.md**:
  * In section *5.3 State Management*, clarify that drafts must be flushed before bulk operations.
  * Briefly document the modal flow.

### 4.8 QA & Testing Matrix

| Scenario | Steps | Expected |
|----------|-------|----------|
| No drafts | Click Upload | Upload dialog opens immediately |
| Drafts – Save | Edit hours → Upload → Save & Continue | Mutation runs, spinner shows, dialog opens after success |
| Drafts – Discard | Edit hours → Upload → Discard & Continue | Drafts cleared (rows revert), dialog opens |
| Cancel | Edit hours → Upload → Cancel | Nothing happens |
| Error on Save | Force network 500 → Save & Continue | Error toast, remain on page, prompt closes |

Unit tests: modal branch logic; integration tests with MSW.

---

## 5. Acceptance Criteria

1. Upload dialog **never** opens while drafts exist unless user chooses Save/Discard.
2. After Save & Continue the Relay store is up-to-date (verify via GraphQL inspector).
3. Duplicate CSV upload creates **no** extra Pay-Stubs.
4. ESLint + type checks pass.
5. Docs updated.

---

## 6. Estimated Effort & Ownership

| Task | Role | Estimate |
|------|------|----------|
| Modal + guard | Front-end | 4h |
| Refactor draft-merge logic | Front-end | 6-8h |
| Updater simplification | Front-end | 3h |
| QA scripts & Cypress | QA | 4h |
| Docs | Eng | 1h |
| **Total** |  | **~2d** |

---

## 7. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Large diff touches transformation & updaters | Stage commits by feature (modal ➜ refactor) with green tests each step |
| Save mutation fails, user blocked | Graceful error toast & retain drafts; allow retry |

---

## 8. Roll-out Plan

Project has not shipped to users yet → merge into `main` once QA greenlights. No data migration needed.

---

_Last updated: 2025-07-23_
