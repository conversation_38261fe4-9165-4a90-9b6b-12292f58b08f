# Duplicate Date Validation Testing Guide

## Overview
This guide explains how to test the newly implemented duplicate date validation feature for CSV uploads.

## Test Setup
1. Ensure the UI server is running (`pnpm dev` in the ui directory)
2. Navigate to a timesheet that already has some employee entries with hours
3. Use the CSV upload feature

## Test Scenarios

### Test File: test-scenario5-duplicate-dates.csv
This file contains the following test cases:

| Row | Employee (SSN) | Date       | Expected Result |
|-----|---------------|------------|-----------------|
| 1   | 134828541     | 7/12/2025  | Valid (first occurrence) |
| 2   | 134828541     | 7/13/2025  | Valid (different date) |
| 3   | 134828541     | 7/12/2025  | **DUPLICATE DATE ERROR** |
| 4   | 099847275     | 7/12/2025  | Val<PERSON> (different employee) |
| 5   | 099847275     | 7/13/2025  | Valid (different date) |
| 6   | 060628360     | 7/12/2025  | Valid (first occurrence) |
| 7   | 060628360     | 7/12/2025  | **DUPLICATE DATE ERROR** |

### Expected Validation Dialog
When uploading test-scenario5-duplicate-dates.csv to a timesheet that already has entries for these employees on these dates, you should see:

```
Duplicate Entries
2 duplicate entry errors in your CSV file
Some employees already have timesheet entries for the dates in your CSV file.

• Row 3: Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.
• Row 7: Employee already has a timesheet entry for this date. Each employee can only have one entry per work date.

To fix these warnings:
1. Open your CSV file and check the "Date" column
2. Remove rows where employees already have entries for those dates
3. Alternatively, update the dates to different work days
4. Save the file and try uploading again
```

## Additional Test Cases

### 1. Upload to Empty Timesheet
- Use the same test file on an empty timesheet
- Expected: All rows should be valid (no duplicates exist yet)

### 2. Mixed Errors Test
- Create a CSV with both duplicate employee entries AND duplicate date entries
- Expected: Error dialog should show both types of errors with combined instructions

### 3. Blank Hours Test
- If an employee has a timesheet entry with 0 hours (blank row), uploading a CSV with that employee+date should NOT trigger a duplicate error
- Only entries with actual hours count as duplicates

## Implementation Details

The duplicate date validation:
1. Extracts existing timesheet details from the current timesheet
2. Checks each CSV row against existing employee+date combinations
3. Only considers entries with hours (ST + OT + DT > 0) as duplicates
4. Shows user-friendly error messages with clear instructions

## Code Changes Summary

1. **TimesheetDetail.tsx**: Extracts existing timesheet details using useMemo
2. **Component Hierarchy**: Passes data through TimesheetDetailView → TimeSheetGrid → TimesheetToolbar → UploadTimeSheetWithEmployeeData → UploadTimeSheetContent
3. **dataValidation.ts**: Added duplicate date validation logic with helper functions
4. **errorCategorization.ts**: Enhanced DUPLICATE_ENTRY error handling to differentiate between duplicate employees and duplicate dates