# EPRLive24 Keep-Alive Service: Detailed Technical Explanation

## The Core Problem

EPRLive24 (the new React/TypeScript application) integrates with the legacy **EPRLive Web Forms application** which has a **hard-coded 2-hour session timeout**. This creates a critical user experience issue:

### Legacy EPRLive Web Forms Session Management
- **Session Duration**: Fixed 2-hour timeout in ASP.NET Web Forms
- **Session Storage**: Uses `ASPStateTempSessions` table in the database
- **Session Expiration**: Automatic logout after 2 hours of inactivity
- **No Warning System**: Users lose their work without notice

```c#
// ASPStateTempSession model shows the legacy session structure
public partial class AspstateTempSession
{
    public string SessionId { get; set; } = null!;
    public DateTime Created { get; set; }
    public DateTime Expires { get; set; }  // 2-hour expiration
    public int Timeout { get; set; }       // Session timeout value
    public bool Locked { get; set; }
    public byte[]? SessionItemShort { get; set; }
    public byte[]? SessionItemLong { get; set; }
}
```

### The User Experience Challenge

**Scenario**: A user opens EPRLive24 and spends time:
1. Reading reports and dashboards (no API calls)
2. Analyzing data (passive consumption)
3. Planning their next actions (thinking time)
4. Working on external documents while referencing EPRLive24

**Problem**: After 2 hours of this "passive" activity, when the user tries to interact with EPRLive24, their session in the legacy Web Forms application has expired, causing unexpected authentication failures.

## Keep-Alive Service Solution

### Technical Implementation

The Keep-Alive service in EPRLive24 solves this by maintaining an active connection with the legacy system:

```typescript
// keep-alive.ts configuration
class KeepAliveService {
    private interval: number = Constants.KeepAlive.defaultInterval; // 15 minutes
    private keepAliveUrl: string = Constants.KeepAlive.endpoint;
    
    private makeKeepAliveRequest = async (): Promise<void> => {
        try {
            const response = await fetch(this.keepAliveUrl, {
                method: 'GET',
                credentials: 'include', // Critical: Include session cookies
                /*
                 * Allow the browser to send cookies and receive full responses.
                 * The legacy endpoint must emit the proper CORS headers
                 * (Access-Control-Allow-Origin, ‑Credentials, etc.).
                 */
                // mode: 'no-cors'   ⟵ remove this
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                },
            });
            
            // Reset retry count on success
            this.retryCount = 0;
        } catch (error) {
            // Handle failures with retry logic
            this.retryCount++;
        }
    };
}
```

### Why Every 15 Minutes?

The service configuration uses a **15-minute interval** strategically:

```typescript
export const KEEP_ALIVE_INTERVAL = 15 * 60 * 1000; // 15 minutes
```

**Reasoning**:
- **Safety Margin**: Well within the 2-hour timeout window
- **Server Load**: Balanced to avoid excessive server requests
- **Battery Efficiency**: Reasonable for mobile/laptop users
- **Network Tolerance**: Accounts for temporary network issues

## Technical Architecture

### Dual Session Management

EPRLive24 implements a sophisticated dual-session system:

#### 1. EPRLive24 Session Management
```typescript
// SessionManagementService configuration
sessionTimeoutMinutes: 120,        // 2-hour session timeout
warningBeforeExpiryMinutes: 5,     // 5-minute warning period
```

#### 2. Legacy EPRLive Web Forms Session Extension
- Keep-alive requests maintain the `ASPStateTempSessions` records
- Session cookies are passed through with `credentials: 'include'`
- The legacy system updates `LastActivityDate` preventing timeout

### Integration Flow

```
EPRLive24 User Activity
        ↓
Activity Tracking (excludes keep-alive)
        ↓
Session Timer Management
        ↓
Keep-Alive Service (every 15 min)
        ↓
Legacy Web Forms Session Extension
        ↓
ASPStateTempSessions Table Update
```

### Database Impact

The keep-alive service interacts with the legacy session tables:

```sql
-- ASPStateTempSessions table structure
CREATE TABLE ASPStateTempSessions (
    SessionId NVARCHAR(88) PRIMARY KEY,
    Created DATETIME NOT NULL,
    Expires DATETIME NOT NULL,    -- Extended by keep-alive
    Timeout INT NOT NULL,         -- 2-hour timeout value
    Locked BIT NOT NULL,
    SessionItemShort VARBINARY(MAX),
    SessionItemLong VARBINARY(MAX)
);
```

## Modern Authentication Architecture

### Reference Token Implementation

EPRLive24 uses **reference tokens** (opaque tokens) instead of JWT tokens for enhanced security:

#### Reference Token Characteristics
- **Format**: Opaque string (not self-contained like JWT)
- **Validation**: Requires introspection with Identity Server
- **Security**: Claims remain on server, not exposed to client
- **Revocation**: Immediate revocation possible
- **Size**: Small, efficient transmission

```csharp
// Enhanced introspection handler with caching
public class IntrospectionHandler : IOpenIddictServerHandler<HandleIntrospectionRequestContext>
{
    // Token validation via introspection
    private async Task<(bool Success, Dictionary<string, object> ClaimsToCache)> 
        ProcessTokenIntrospection(HandleIntrospectionRequestContext context)
    {
        // Validate token format and basic structure
        if (string.IsNullOrWhiteSpace(context.Request.Token))
        {
            context.Reject(error: Errors.InvalidRequest, 
                          description: "The token parameter is missing or empty.");
            return (false, claimsToCache);
        }
        
        // OpenIddict performs introspection validation
        // Add custom validation and token binding
        ExtractApplicationClaims(context);
        AddTokenBindingAndMetadata(context);
        
        return await ValidateTokenClaims(context);
    }
}
```

#### Token Security Features

```csharp
// Token binding for security
private void AddTokenBindingAndMetadata(HandleIntrospectionRequestContext context)
{
    var tokenBindingId = GenerateTokenBindingId(context.Request.Token ?? string.Empty);
    
    context.Claims["token_binding_id"] = tokenBindingId;
    context.Claims["binding_method"] = "token_fingerprint";
    context.Claims["introspection_time"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
    context.Claims["handler_id"] = "EPRIdentity.IntrospectionHandler";
}
```

### Performance Optimization

#### Introspection Caching
```csharp
// Optimized caching strategy
var cacheEntryOptions = new MemoryCacheEntryOptions
{
    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15),
    SlidingExpiration = TimeSpan.FromMinutes(5),
    Priority = CacheItemPriority.Normal,
    Size = 1
};
_cache.Set(cacheKey, claimsToCache, cacheEntryOptions);
```

## Why Keep-Alive is Essential

### 1. **Seamless User Experience**
- Users can spend hours reading/analyzing without interaction
- No unexpected authentication failures
- Continuous productivity without interruption

### 2. **Legacy System Compatibility**
- Works with existing EPRLive Web Forms infrastructure
- No changes required to legacy codebase
- Maintains backward compatibility

### 3. **Business Continuity**
- Users don't lose work during long analysis sessions
- Reduces support tickets about unexpected logouts
- Improves user satisfaction and productivity

### 4. **Security Balance**
- Maintains security through eventual timeout
- Provides session warnings in EPRLive24
- Allows controlled session extension
- Reference tokens provide enhanced security over JWT

## Advanced Features

### Activity-Based Session Management

The system intelligently distinguishes between user activity and keep-alive requests:

```typescript
// Activity tracking excludes keep-alive requests
const isKeepAliveRequest = config.url?.includes('/keep-alive');
if (!isKeepAliveRequest) {
    sessionManagementService.registerActivity();
}
```

### Enhanced Error Handling

```typescript
// Retry logic for network failures
if (this.retryCount >= this.maxRetries) {
    console.error('Keep-alive max retries reached. Stopping service.');
    this.stop();
}
```

### Session State Coordination

The system coordinates between multiple session management layers:

1. **Browser Session**: EPRLive24 local session
2. **API Session**: Reference token validation via introspection
3. **Legacy Session**: Web Forms ASPStateTempSessions
4. **Identity Session**: 30-minute revalidation intervals

## Token Management Integration

### Reference Token Validation Flow

```
Browser Request → CookieToHeaderMiddleware → Backend API → Token Introspection → Cache Check
                                                                 ↓
Identity Server ← Token Validation ← Cache Miss ← Introspection Handler
                                                                 ↓
                 Token Response → Claims Extraction → Cache Store → API Response
```

### Token Security Layers

1. **Format Validation**: Ensures token is not null/empty
2. **Introspection Validation**: OpenIddict validates token with Identity Server
3. **Claim Validation**: Custom validation for email and GUID formats
4. **Business Logic**: Application-specific claim requirements
5. **Token Binding**: SHA256-based token fingerprints prevent theft

### Secure Logging

```csharp
// Safe token fingerprinting for logs
private static string GetTokenFingerprint(string token)
{
    if (string.IsNullOrWhiteSpace(token))
        return "null";

    if (token.Length <= 8)
        return $"{token}(len:{token.Length})";

    return $"{token[..8]}...(len:{token.Length})";
}
```

## Configuration and Monitoring

### Environment-Specific Settings

```typescript
// Future enhancement - configurable intervals
const keepAliveInterval = process.env.VITE_KEEP_ALIVE_INTERVAL || 900000; // 15 min
const sessionTimeout = process.env.VITE_SESSION_TIMEOUT_MINUTES || 120;   // 2 hours
```

### Monitoring and Logging

The service provides comprehensive logging:
- Keep-alive request success/failure
- Retry attempts and failures
- Service start/stop events
- Network error handling
- Token introspection performance metrics

## Performance Considerations

### Resource Optimization

- **Lightweight Requests**: Minimal payload keep-alive calls
- **Efficient Timing**: 15-minute intervals balance effectiveness and performance
- **Error Recovery**: Automatic retry with exponential backoff
- **Clean Shutdown**: Proper cleanup when user logs out
- **Token Introspection Caching**: 15-minute cache with 5-minute sliding expiration

### Network Efficiency

```typescript
headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Cache-Control': 'no-cache'  // Prevent caching of keep-alive requests
}
```

## Real-World Benefits

### Before Keep-Alive Service
- Users lost sessions after 2 hours of reading/analysis
- Frequent authentication errors during data entry
- Lost work and user frustration
- Increased support burden

### After Keep-Alive Service
- Seamless experience for long analysis sessions
- Predictable session management with warnings
- Reduced authentication errors
- Improved user productivity and satisfaction
- Enhanced security with reference tokens

## Security Considerations

### Token Security
- Keep-alive requests don't expose sensitive data
- Reference tokens are opaque and secure
- Session cookies remain secure (HttpOnly)
- Maintains existing security boundaries
- Token binding prevents theft and replay attacks

### Audit Trail
- All keep-alive activities are logged
- Failed attempts are tracked and monitored
- Service lifecycle events are recorded
- Integration with existing security monitoring
- Token introspection events are comprehensively logged

### Reference Token Advantages
- **Immediate Revocation**: Tokens can be revoked instantly
- **Server-Side Claims**: Claims never exposed to client
- **Introspection Audit**: Every validation creates audit trail
- **Enhanced Security**: Opaque format prevents tampering

---

**Summary**: The EPRLive24 Keep-Alive service is essential for bridging the gap between modern user expectations and legacy system constraints. It ensures users can work productively without unexpected interruptions while maintaining the security and integrity of the existing EPRLive Web Forms infrastructure. The modern reference token architecture provides enhanced security while the intelligent caching system ensures optimal performance.

**Current Date**: 2025-07-14  
**Last Updated by**: bekirakinci  
**Token Architecture**: Reference Tokens (Opaque) with Introspection  
**Related PRs**: #614, #622, #624