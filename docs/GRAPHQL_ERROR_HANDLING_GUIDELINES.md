# GraphQL Error Handling & Reporting Guidelines

**Applies to:** Backend (Hot Chocolate v15.1.7 / .NET 9) and Front-end (React 18 + Relay 15)  
**Last updated:** 24 Jul 2025

---

## 1  Background & Motivation

Our API currently mixes concerns between GraphQL *top-level* errors (in the `errors` array) and *business-level* validation messages returned inside mutation payloads.  This inconsistency leads to duplicated error surfaces, hard-to-debug network traces, and uneven front-end UX.

Relay mandates that **successful mutations must return a non-null payload** even when business rules fail, enabling optimistic updates and store writes.  Hot Chocolate offers rich `IError` objects, error filters, and exception mappers that we can leverage to enforce a uniform contract.

These guidelines codify an error taxonomy and implementation patterns so both the server and Relay clients speak the same language.

---

## 2  Error Taxonomy

| Layer | Definition | Transport Location | Retry advice |
|-------|-----------|--------------------|--------------|
| **System Error** | Infrastructure or unexpected exception (e.g. DB unavailable, NullReferenceException) | Top-level `errors` array, payload **null** | *Maybe* – depends on cause |
| **Auth Error** | Authentication / authorisation failure | Top-level `errors` array with `extensions.code = "UNAUTHORISED"` | Prompt login |
| **Business Error** | Domain or validation rule failure (e.g. period closed, duplicate entry) | `errors` field inside mutation/query **payload** | Usually no – user must correct input |
| **Client Error** | Network unavailable, CORS, HTTP 4xx | Not a GraphQL response – surfaced by fetch layer | Retry after connection restored |

Only **Business Errors** live inside the payload; everything else bubbles as top-level GraphQL errors.

---

## 3  Standard Payload Shape

All Relay-compliant payloads implement the following interface:

```graphql
interface RelayPayload {
  errors: [BusinessError!]!
  clientMutationId: String
}

type BusinessError {
  code: String!          # Machine-readable (e.g. PERIOD_CLOSED)
  message: String!       # Localisable human text (English by default)
  field: String          # Nullable – input field causing the error
  extensions: JSON       # Arbitrary extra metadata (range min/max, etc.)
}
```

* `errors` **always** exists (empty list on success).
* `code` **must** map to a constant in `ErrorCodes.cs`.
* `message` should be concise and user-friendly; FE may override via i18n table.

---

## 4  Backend Implementation

### 4.1  Core DTOs

```csharp
namespace backend.Types.Errors;

public readonly record struct BusinessError(
    string Code,
    string Message,
    string? Field = null,
    IReadOnlyDictionary<string, object?>? Extensions = null);

public static class ErrorCodes
{
    public const string PeriodClosed = "PERIOD_CLOSED";
    public const string DuplicateEntry = "DUPLICATE_ENTRY";
    public const string InvalidState = "INVALID_STATE";
    // … add more here
}
```

### 4.2  Base Payload

```csharp
using backend.Types.Errors;

public abstract class BaseRelayPayload : IRelayPayload
{
    protected BaseRelayPayload(IEnumerable<BusinessError>? errors = null,
                               string? clientMutationId = null)
    {
        Errors = errors?.ToList() ?? new List<BusinessError>();
        ClientMutationId = clientMutationId;
    }

    public IReadOnlyList<BusinessError> Errors { get; }
    public string? ClientMutationId { get; }

    public bool IsSuccess => Errors.Count == 0;
}
```

### 4.3  Producing Business Errors in Resolvers

```csharp
if (!IsPeriodOpen(input.Date))
{
    return new AddTimesheetPayload(
        timeSheetEdge: null,
        errors: new[]
        {
            new BusinessError(ErrorCodes.PeriodClosed,
                               "The reporting period is closed.",
                               field: "date")
        },
        clientMutationId: input.ClientMutationId);
}
```

### 4.4  Suppressing Duplicate Top-Level Errors

Configure an **Error Filter** once in `Program.cs`:

```csharp
builder.Services.AddGraphQLServer()
    .AddErrorFilter(error =>
        error.Extensions?.ContainsKey("handled") == true ? null : error);
```

In exception mappers convert known domain exceptions into `BusinessError`s and add `extensions["handled"] = true` so they are filtered out.

### 4.5  Exception-to-Error Mapping

```csharp
public class PeriodClosedExceptionMapper : IErrorFilter
{
    public IError OnError(IError error)
    {
        if (error.Exception is PeriodClosedException ex)
        {
            return ErrorBuilder.FromError(error)
                .SetMessage(ex.Message)
                .SetExtension("code", ErrorCodes.PeriodClosed)
                .SetExtension("handled", true)
                .Build();
        }
        return error;
    }
}
```

### 4.6  Subscription & Deferred Streams

* Business errors in subscription payloads follow the **same** pattern (`errors` field).  
* Streamed lists (`@stream`) should include an optional final `errors` edge item if validation fails mid-stream.

---

## 5  Frontend (Relay) Consumption

### 5.1  Typed Fragments

Generate types with `relay-compiler` – `errors` will map to `BusinessError[]`.

```graphql
mutation AddTimesheet($input: AddTimesheetInput!) {
  addTimesheet(input: $input) {
    timeSheetEdge { node { id } }
    errors { code message field }
    clientMutationId
  }
}
```

### 5.2  Utility Helpers

`src/graphql/errors/index.ts`:

```ts
export function hasBusinessErrors(payload: {errors: {code: string}[]}) {
  return payload.errors.length > 0;
}

export function getFirstErrorMessage(payload: {errors: {message: string}[]}) {
  return payload.errors[0]?.message ?? 'Unknown error';
}
```

### 5.3  Mutation Commit Pattern

```ts
commitAddTimesheet({
  variables: {input},
  onCompleted(resp) {
    const payload = resp.addTimesheet;
    if (hasBusinessErrors(payload)) {
      showToast(getFirstErrorMessage(payload));
      return;
    }
    // success path – Edge already inserted via Relay store update
  },
  onError(err) {
    showToast(err.message); // Network or system error
  },
});
```

### 5.4  Global Error Boundary

Intercept top-level GraphQL errors once (e.g. `RelayEnvironment.onError`) and route to a global error boundary or session expiration handler.

---

## 6  Testing Strategy

| Layer | Tool | Example |
|-------|------|---------|
| Unit (backend) | xUnit + Snapshots | Assert that resolver returns `errors[0].code == PERIOD_CLOSED` |
| Integration (backend) | Hot Chocolate `TestServer` | Verify `errors` array empty for valid input |
| Front-end | Jest + React Testing Library | Mock GraphQL response containing business error, assert toast shown |

---

## 7  Roll-out Checklist

1. Add `BusinessError` and `ErrorCodes` classes.  
2. Refactor all existing payloads to inherit from `BaseRelayPayload`.  
3. Implement error filters & exception mappers.  
4. Update mutations to populate `errors` list.  
5. Regenerate Relay types (`yarn relay`).  
6. Add FE utility helpers and update existing mutations.  
7. Smoke-test critical flows (timesheet submit, pay-stub create, etc.).

---

## 8  References

* GraphQL Spec §7 – Error Handling  
* Hot Chocolate Docs – Error Handling (<https://chillicream.com/docs/hotchocolate/v15/execution/errors>)  
* Relay Modern – Error handling patterns (<https://relay.dev/docs/guides/error-handling/>)
