# EPRLive24 Repo

This repo contains the backend and the frontend code, also includes Aspire project to orchestrate the deployment of the backend and frontend projects.

## Developer Setup

### Git Configuration

Please make sure to configure Git to handle line endings correctly on your machine:

#### For Windows:

```shell
git config --global core.autocrlf true
```

'true' makes sure that CRLF is used in the working directory and converts LF to CRLF when checking out code.

#### For Linux/Mac:

```shell
git config --global core.autocrlf input
```

'input' converts CRLF to LF when checking in code but does not perform any conversion when checking out.

### Git Hooks Setup (Required)

**Important**: All developers must run a one-time setup script to configure Git hooks that automatically prepend issue numbers to commit messages.

#### Quick Setup:

**Windows PowerShell:**
```powershell
.\.githooks\setup.ps1
```

**Unix/macOS/Git Bash:**
```bash
./.githooks/setup.sh
```

#### What This Does:
- Configures Git to use the repository's shared hooks
- Enables automatic issue number prepending based on branch names
- Ensures commit messages follow the pattern: `#42 your commit message`

For detailed information, troubleshooting, and security features, see [.githooks/README.md](.githooks/README.md).

**Note**: This is a one-time setup per repository clone. Once configured, hooks will automatically update when you pull changes.
 
