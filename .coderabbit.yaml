# CodeRabbit Configuration for Biome + ESLint Hybrid Integration
# This configuration enables CodeRabbit to use Biome for formatting and general linting,
# while preserving ESLint for Relay-specific rules

# Code review settings
reviews:
  # Enable automatic code formatting
  auto_format: true

  # Tools configuration
  tools:
    # Enable Biome integration
    biome:
      enabled: true
      config_file: "ui/biome.json"
      auto_fix: true
      check_on_pr: true

    # Enable ESLint for Relay rules
    eslint:
      enabled: true
      config_file: "ui/eslint.relay.config.js"
      auto_fix: true
      check_on_pr: true

  # Enable linting checks
  linting:
    enabled: true
    tools: ["biome", "eslint"]

  # Code quality checks
  quality:
    enabled: true
    complexity_threshold: 10
    maintainability_threshold: 65

  # Security checks
  security:
    enabled: true
    scan_dependencies: true

  # Performance checks
  performance:
    enabled: true
    bundle_size_check: true

  # Custom rules for your project
  rules:
    # Enforce consistent naming conventions
    naming:
      components: "PascalCase"
      functions: "camelCase"
      constants: "UPPER_SNAKE_CASE"

    # TypeScript specific rules
    typescript:
      strict: true
      no_any: true
      prefer_interfaces: true

    # React specific rules
    react:
      hooks_exhaustive_deps: true
      no_unused_props: true
      prefer_functional_components: true

# PR automation
pr:
  # Auto-fix formatting issues
  auto_fix_formatting: true

  # Add Biome check status
  status_checks:
    - "biome-check"

  # Commit message format
  commit_message_format: "fix: {description}"

# File patterns to include/exclude
files:
  include:
    - "ui/src/**/*.{ts,tsx,js,jsx}"
    - "ui/*.{json,md}"
    - "backend/**/*.{cs,csproj,json,md}"
    - "identity/**/*.{cs,csproj,json,md}"
    - "aspire/**/*.{cs,csproj,json,md}"
    - "EPREvents/**/*.{cs,csproj,json,md}"
    - "*.md"
    - "*.json"
  exclude:
    - "ui/node_modules/**"
    - "ui/dist/**"
    - "ui/build/**"
    - "ui/coverage/**"
    - "backend/bin/**"
    - "backend/obj/**"
    - "identity/bin/**"
    - "identity/obj/**"
    - "aspire/bin/**"
    - "aspire/obj/**"
    - "EPREvents/bin/**"
    - "EPREvents/obj/**"
    - "node_modules/**"
    - "dist/**"
    - "build/**"
    - "*.min.js"
    - "*.bundle.js"
    - "coverage/**"
