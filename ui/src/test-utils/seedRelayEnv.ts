// ui/src/test-utils/seedRelayEnv.ts
import { MockEnvironment, MockPayloadGenerator } from 'relay-test-utils';

interface SeedOverrides {
  id?: string;
  payStubs?: any[];
  timeSheetFields?: Record<string, any>;
  employee?: Record<string, any>;
  agreement?: Record<string, any>;
}

export function seedEnvironment(env: MockEnvironment, overrides: SeedOverrides = {}) {
  // Clear any previously queued resolvers to prevent data leakage between tests
  if (env.mock && typeof env.mock.clear === 'function') {
    env.mock.clear();
  }
  
  // Queue a resolver for when operations are made
  env.mock.queueOperationResolver(operation =>
    MockPayloadGenerator.generate(operation, {
      TimeSheet: () => ({            // core record
        id: overrides.id ?? 'timesheet-1',
        payStubs: overrides.payStubs ?? [],
        ...overrides.timeSheetFields,
      }),
      Employee: () => overrides.employee ?? { id: 'emp-1', firstName: 'John', lastName: 'Doe', active: true },
      Agreement: () => overrides.agreement ?? { id: 'agr-1', name: 'Std' },
      Classification: () => ({ id: 'class-1', name: 'Default Classification' }),
      CostCenter: () => ({ id: 'cc-1', name: 'Default Cost Center' }),
      // Handle connection types for fragment data
      TimeSheetConnection: () => ({
        edges: [{ node: { id: overrides.id ?? 'timesheet-1' }, cursor: 'cursor-1' }],
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: 'cursor-1', endCursor: 'cursor-1' }
      }),
      PayStubConnection: () => ({
        edges: overrides.payStubs?.map((ps, idx) => ({ 
          node: ps, 
          cursor: `cursor-${idx}` 
        })) ?? [],
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null }
      }),
      // add more resolvers as fragments evolve
    })
  );
}