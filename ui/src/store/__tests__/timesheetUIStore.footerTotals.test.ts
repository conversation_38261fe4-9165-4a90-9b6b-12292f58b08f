/**
 * Tests for footer totals calculation with orphan drafts
 * Tests the enhanced selectFooterTotals and calculatePayStubTotalsWithDrafts functions
 */

import { useTimesheetUIStore } from '../timesheetUIStore';
import type { FlatPayStubDetailDraft } from '@/src/types';

// Mock timesheet data
const createMockTimesheet = (payStubs: any[]) => ({
    payStubs: payStubs
});

const createMockPayStub = (id: string, details: any[] = []) => ({
    id,
    details
});

const createMockDetail = (id: string, stHours = 0, otHours = 0, dtHours = 0, bonus = 0, expenses = 0) => ({
    id,
    stHours,
    otHours,
    dtHours,
    bonus,
    expenses
});

describe('TimesheetUIStore Footer Totals', () => {
    let store: any;
    const timesheetId = 'test-timesheet-1';

    beforeEach(() => {
        // Get a fresh store instance for each test
        store = useTimesheetUIStore.getState();
        // Clear any existing state completely
        store.clearAllDrafts(); // Clear all drafts from all timesheets
        store.setActiveTimesheet(timesheetId);
    });

    describe('selectFooterTotals with server data only', () => {
        it('should calculate totals from server payStubs when no drafts exist', () => {
            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [
                    createMockDetail('detail-1', 8, 2, 0, 100, 50),
                    createMockDetail('detail-2', 6, 0, 2, 0, 25)
                ]),
                createMockPayStub('paystub-2', [createMockDetail('detail-3', 4, 4, 0, 75, 0)])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 18, // 8 + 6 + 4
                otHours: 6, // 2 + 0 + 4
                dtHours: 2, // 0 + 2 + 0
                totalHours: 26, // 18 + 6 + 2
                bonus: 175, // 100 + 0 + 75
                expenses: 75 // 50 + 25 + 0
            });
        });

        it('should handle empty timesheet data', () => {
            const timesheetData = createMockTimesheet([]);
            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 0,
                otHours: 0,
                dtHours: 0,
                totalHours: 0,
                bonus: 0,
                expenses: 0
            });
        });

        it('should handle null/undefined timesheet data', () => {
            const totals = store.selectFooterTotals(timesheetId, undefined);

            expect(totals).toEqual({
                stHours: 0,
                otHours: 0,
                dtHours: 0,
                totalHours: 0,
                bonus: 0,
                expenses: 0
            });
        });
    });

    describe('selectFooterTotals with draft overlays', () => {
        it('should apply draft changes to existing server details', () => {
            // Set up draft changes
            store.updateDetailField(timesheetId, 'detail-1', 'stHours', 12, 'paystub-1');
            store.updateDetailField(timesheetId, 'detail-2', 'bonus', 200, 'paystub-1');

            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [
                    createMockDetail('detail-1', 8, 2, 0, 100, 50),
                    createMockDetail('detail-2', 6, 0, 2, 0, 25)
                ])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 18, // 12 (draft) + 6 (server)
                otHours: 2, // 2 (server) + 0 (server)
                dtHours: 2, // 0 (server) + 2 (server)
                totalHours: 22, // 18 + 2 + 2
                bonus: 300, // 100 (detail-1 server) + 200 (detail-2 draft) + 0 (detail-2 server)
                expenses: 75 // 50 (server) + 25 (server)
            });
        });

        it('should exclude details marked for deletion', () => {
            // Mark detail for deletion
            store.updateDetailField(timesheetId, 'detail-1', '_uiDelete', true, 'paystub-1');

            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [
                    createMockDetail('detail-1', 8, 2, 0, 100, 50),
                    createMockDetail('detail-2', 6, 0, 2, 0, 25)
                ])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 6, // Only detail-2
                otHours: 0, // Only detail-2
                dtHours: 2, // Only detail-2
                totalHours: 8, // 6 + 0 + 2
                bonus: 0, // Only detail-2
                expenses: 25 // Only detail-2
            });
        });
    });

    describe('selectFooterTotals with orphan drafts', () => {
        it('should include orphan draft details in totals', () => {
            // Create orphan drafts (details that don't exist in server data)
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', 'paystub-1', 'paystub-1');

            store.updateDetailField(timesheetId, 'orphan-detail-2', 'otHours', 3, 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-2', 'payStubId', 'paystub-1', 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-2', 'bonus', 150, 'paystub-1');

            const timesheetData = createMockTimesheet([createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 2, 0, 100, 50)])]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 13, // 8 (server) + 5 (orphan)
                otHours: 5, // 2 (server) + 3 (orphan)
                dtHours: 0, // 0 (server) + 0 (orphan)
                totalHours: 18, // 13 + 5 + 0
                bonus: 250, // 100 (server) + 150 (orphan)
                expenses: 50 // 50 (server) + 0 (orphan)
            });
        });

        it('should exclude orphan drafts marked for deletion', () => {
            // Create orphan draft and mark for deletion
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', 'paystub-1', 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', '_uiDelete', true, 'paystub-1');

            const timesheetData = createMockTimesheet([createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 2, 0, 100, 50)])]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 8, // Only server detail (orphan excluded)
                otHours: 2, // Only server detail
                dtHours: 0, // Only server detail
                totalHours: 10, // 8 + 2 + 0
                bonus: 100, // Only server detail
                expenses: 50 // Only server detail
            });
        });

        it('should exclude orphan drafts marked as deleted (duplicate check)', () => {
            // Create orphan draft and mark as deleted
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', 'paystub-1', 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', '_uiDelete', true, 'paystub-1');

            const timesheetData = createMockTimesheet([createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 2, 0, 100, 50)])]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 8, // Only server detail (orphan excluded)
                otHours: 2, // Only server detail
                dtHours: 0, // Only server detail
                totalHours: 10, // 8 + 2 + 0
                bonus: 100, // Only server detail
                expenses: 50 // Only server detail
            });
        });

        it('should only include orphan drafts that belong to the correct payStub', () => {
            // Create orphan drafts for different payStubs
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, 'paystub-1');
            store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', 'paystub-1', 'paystub-1');

            store.updateDetailField(timesheetId, 'orphan-detail-2', 'stHours', 3, 'paystub-2');
            store.updateDetailField(timesheetId, 'orphan-detail-2', 'payStubId', 'paystub-2', 'paystub-2');

            const timesheetData = createMockTimesheet([createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 2, 0, 100, 50)])]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 13, // 8 (server) + 5 (orphan for paystub-1, not paystub-2)
                otHours: 2, // 2 (server)
                dtHours: 0, // 0 (server)
                totalHours: 15, // 13 + 2 + 0
                bonus: 100, // 100 (server)
                expenses: 50 // 50 (server)
            });
        });
    });

    describe('selectFooterTotals with PayStubs marked for deletion', () => {
        it('should exclude PayStubs marked for deletion from totals', () => {
            // Mark payStub for deletion
            store.markForDeletion(timesheetId, 'paystub-1');

            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 2, 0, 100, 50)]),
                createMockPayStub('paystub-2', [createMockDetail('detail-2', 6, 0, 2, 0, 25)])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 6, // Only paystub-2
                otHours: 0, // Only paystub-2
                dtHours: 2, // Only paystub-2
                totalHours: 8, // 6 + 0 + 2
                bonus: 0, // Only paystub-2
                expenses: 25 // Only paystub-2
            });
        });
    });

    describe('selectFooterTotals null handling', () => {
        it('should treat null values as 0 during aggregation', () => {
            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [
                    { id: 'detail-1', stHours: null, otHours: undefined, dtHours: 8, bonus: null, expenses: 50 },
                    { id: 'detail-2', stHours: 5, otHours: null, dtHours: null, bonus: 100, expenses: null }
                ])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 5, // 0 + 5 (null treated as 0)
                otHours: 0, // 0 + 0 (undefined and null treated as 0)
                dtHours: 8, // 8 + 0 (null treated as 0)
                totalHours: 13, // 5 + 0 + 8
                bonus: 100, // 0 + 100 (null treated as 0)
                expenses: 50 // 50 + 0 (null treated as 0)
            });
        });

        it('should handle draft values that override null server values', () => {
            // Override null server values with draft values
            store.updateDetailField(timesheetId, 'detail-1', 'stHours', 10, 'paystub-1');
            store.updateDetailField(timesheetId, 'detail-1', 'bonus', 200, 'paystub-1');

            const timesheetData = createMockTimesheet([
                createMockPayStub('paystub-1', [
                    { id: 'detail-1', stHours: null, otHours: null, dtHours: null, bonus: null, expenses: null }
                ])
            ]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 10, // Draft value
                otHours: 0, // Default (no draft)
                dtHours: 0, // Default (no draft)
                totalHours: 10, // 10 + 0 + 0
                bonus: 200, // Draft value
                expenses: 0 // Default (no draft)
            });
        });
    });

    describe('cross-timesheet isolation', () => {
        it('should not include drafts from other timesheets', () => {
            const otherTimesheetId = 'other-timesheet';

            // Create drafts in different timesheets
            store.updateDetailField(timesheetId, 'detail-1', 'stHours', 10, 'paystub-1');
            store.updateDetailField(otherTimesheetId, 'detail-2', 'stHours', 20, 'paystub-1');

            const timesheetData = createMockTimesheet([createMockPayStub('paystub-1', [createMockDetail('detail-1', 8, 0, 0, 0, 0)])]);

            const totals = store.selectFooterTotals(timesheetId, timesheetData);

            expect(totals).toEqual({
                stHours: 10, // Only draft from current timesheet
                otHours: 0,
                dtHours: 0,
                totalHours: 10,
                bonus: 0,
                expenses: 0
            });
        });
    });
});
