/**
 * Tests for simplified selectFooterTotals function
 * 
 * Verifies that the function correctly handles arrays of PayStubs
 * after removal of the Array.isArray branch
 */

import { useTimesheetUIStore } from '../timesheetUIStore';
import type { ServerPayStub } from '@/src/types/timesheet';

describe('selectFooterTotals simplified implementation', () => {
  beforeEach(() => {
    // Reset store state
    useTimesheetUIStore.setState({
      detailDrafts: new Map(),
      payStubDrafts: new Map(),
      markedForDeletion: new Set(),
      draftsVersion: 0,
    });
  });

  describe('Array handling', () => {
    it('should handle array of PayStubs directly', () => {
      const timesheetId = 'timesheet-1';
      const payStubs: ServerPayStub[] = [
        {
          id: 'paystub-1',
          employeeId: 'emp-1',
          name: '<PERSON>',
          totalHours: 40,
          details: [
            {
              id: 'detail-1',
              payStubId: 'paystub-1',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 100,
              expenses: 50,
            },
          ],
        },
        {
          id: 'paystub-2',
          employeeId: 'emp-2',
          name: '<PERSON>',
          totalHours: 45,
          details: [
            {
              id: 'detail-2',
              payStubId: 'paystub-2',
              stHours: 8,
              otHours: 2,
              dtHours: 0,
              bonus: 0,
              expenses: 25,
            },
          ],
        },
      ];

      const store = useTimesheetUIStore.getState();
      const totals = store.selectFooterTotals(timesheetId, { payStubs });

      expect(totals).toEqual({
        stHours: 16,
        otHours: 2,
        dtHours: 0,
        totalHours: 18,
        bonus: 100,
        expenses: 75,
      });
    });

    it('should handle empty array', () => {
      const timesheetId = 'timesheet-1';
      const store = useTimesheetUIStore.getState();
      const totals = store.selectFooterTotals(timesheetId, { payStubs: [] });

      expect(totals).toEqual({
        stHours: 0,
        otHours: 0,
        dtHours: 0,
        totalHours: 0,
        bonus: 0,
        expenses: 0,
      });
    });

    it('should handle null/undefined payStubs', () => {
      const timesheetId = 'timesheet-1';
      const store = useTimesheetUIStore.getState();
      
      const totalsNull = store.selectFooterTotals(timesheetId, { payStubs: null });
      const totalsUndefined = store.selectFooterTotals(timesheetId, { payStubs: undefined });
      const totalsNoData = store.selectFooterTotals(timesheetId, null);

      const expectedEmpty = {
        stHours: 0,
        otHours: 0,
        dtHours: 0,
        totalHours: 0,
        bonus: 0,
        expenses: 0,
      };

      expect(totalsNull).toEqual(expectedEmpty);
      expect(totalsUndefined).toEqual(expectedEmpty);
      expect(totalsNoData).toEqual(expectedEmpty);
    });

    it('should filter out falsy values in array', () => {
      const timesheetId = 'timesheet-1';
      const payStubs = [
        {
          id: 'paystub-1',
          employeeId: 'emp-1',
          name: 'John Doe',
          totalHours: 40,
          details: [
            {
              id: 'detail-1',
              payStubId: 'paystub-1',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 0,
              expenses: 0,
            },
          ],
        },
        null,
        undefined,
        false,
        0,
        '',
      ] as any[];

      const store = useTimesheetUIStore.getState();
      const totals = store.selectFooterTotals(timesheetId, { payStubs });

      expect(totals).toEqual({
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        totalHours: 8,
        bonus: 0,
        expenses: 0,
      });
    });
  });

  describe('Draft changes integration', () => {
    it('should include detail draft changes in totals', () => {
      const timesheetId = 'timesheet-1';
      const payStubs: ServerPayStub[] = [
        {
          id: 'paystub-1',
          employeeId: 'emp-1',
          name: 'John Doe',
          totalHours: 40,
          details: [
            {
              id: 'detail-1',
              payStubId: 'paystub-1',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 0,
              expenses: 0,
            },
          ],
        },
      ];

      // Add draft changes
      const store = useTimesheetUIStore.getState();
      store.updateDetailField(timesheetId, 'detail-1', 'stHours', 10, 'paystub-1');
      store.updateDetailField(timesheetId, 'detail-1', 'bonus', 150, 'paystub-1');

      const totals = store.selectFooterTotals(timesheetId, { payStubs });

      expect(totals).toEqual({
        stHours: 10, // Changed from 8 to 10
        otHours: 0,
        dtHours: 0,
        totalHours: 10,
        bonus: 150, // Changed from 0 to 150
        expenses: 0,
      });
    });

    it('should exclude PayStubs marked for deletion', () => {
      const timesheetId = 'timesheet-1';
      const payStubs: ServerPayStub[] = [
        {
          id: 'paystub-1',
          employeeId: 'emp-1',
          name: 'John Doe',
          totalHours: 40,
          details: [
            {
              id: 'detail-1',
              payStubId: 'paystub-1',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 100,
              expenses: 50,
            },
          ],
        },
        {
          id: 'paystub-2',
          employeeId: 'emp-2',
          name: 'Jane Smith',
          totalHours: 40,
          details: [
            {
              id: 'detail-2',
              payStubId: 'paystub-2',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 50,
              expenses: 25,
            },
          ],
        },
      ];

      // Mark paystub-2 for deletion
      const store = useTimesheetUIStore.getState();
      store.markForDeletion(timesheetId, 'paystub-2');

      const totals = store.selectFooterTotals(timesheetId, { payStubs });

      expect(totals).toEqual({
        stHours: 8, // Only paystub-1
        otHours: 0,
        dtHours: 0,
        totalHours: 8,
        bonus: 100, // Only paystub-1
        expenses: 50, // Only paystub-1
      });
    });

    it('should include orphan draft details', () => {
      const timesheetId = 'timesheet-1';
      const payStubs: ServerPayStub[] = [
        {
          id: 'paystub-1',
          employeeId: 'emp-1',
          name: 'John Doe',
          totalHours: 40,
          details: [
            {
              id: 'detail-1',
              payStubId: 'paystub-1',
              stHours: 8,
              otHours: 0,
              dtHours: 0,
              bonus: 0,
              expenses: 0,
            },
          ],
        },
      ];

      // Add orphan draft detail
      const store = useTimesheetUIStore.getState();
      store.setDetailDraft(timesheetId, 'orphan-detail-1', {
        id: 'orphan-detail-1',
        payStubId: 'paystub-1',
        stHours: 4,
        otHours: 1,
        dtHours: 0,
        bonus: 50,
        expenses: 25,
        _uiLastModified: Date.now(),
      });

      const totals = store.selectFooterTotals(timesheetId, { payStubs });

      expect(totals).toEqual({
        stHours: 12, // 8 + 4 (orphan)
        otHours: 1,  // 0 + 1 (orphan)
        dtHours: 0,
        totalHours: 13,
        bonus: 50,   // 0 + 50 (orphan)
        expenses: 25, // 0 + 25 (orphan)
      });
    });
  });
});