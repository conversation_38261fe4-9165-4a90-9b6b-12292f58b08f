/**
 * Multi-Timesheet Concurrency Tests - Phase 3 Implementation
 *
 * Tests comprehensive multi-timesheet state isolation and concurrency handling
 * as specified in the Phase 3 implementation plan.
 */

import { act, renderHook } from '@testing-library/react';
import { useTimesheetUIStore } from '../timesheetUIStore';

describe('Zustand Store Multi-Timesheet Concurrency', () => {
    beforeEach(() => {
        // Clear all state before each test
        const store = useTimesheetUIStore.getState();
        store.clearAllDrafts();
        // Note: setLoading() and setSaving() methods removed as they're obsolete
        // Clear localStorage
        localStorage.clear();
    });

    describe('State Isolation', () => {
        test('concurrent timesheet edits do not bleed state', () => {
            const store = useTimesheetUIStore.getState();

            // Simulate two browser tabs with different timesheets
            act(() => {
                store.updatePayStubDraft('ts-1', 'ps-123', {
                    hours: { standard: 40, overtime: 2, doubletime: 0, total: 42 },
                    amounts: { bonus: 100, expenses: 50 }
                });

                store.updatePayStubDraft('ts-2', 'ps-123', {
                    hours: { standard: 35, overtime: 5, doubletime: 1, total: 41 },
                    amounts: { bonus: 200, expenses: 75 }
                });
            });

            // Verify isolation - same PayStub ID but different timesheets
            const ts1Draft = store.getDraftForPayStub('ts-1', 'ps-123');
            const ts2Draft = store.getDraftForPayStub('ts-2', 'ps-123');

            expect(ts1Draft?.hours?.standard).toBe(40);
            expect(ts1Draft?.hours?.overtime).toBe(2);
            expect(ts1Draft?.amounts?.bonus).toBe(100);

            expect(ts2Draft?.hours?.standard).toBe(35);
            expect(ts2Draft?.hours?.overtime).toBe(5);
            expect(ts2Draft?.amounts?.bonus).toBe(200);

            // Ensure they're completely separate objects
            expect(ts1Draft).not.toBe(ts2Draft);
        });

        test('state cleanup works per timesheet scope', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Create drafts for multiple timesheets
                store.updatePayStubDraft('ts-1', 'ps-1', {
                    hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
                    amounts: { bonus: 0, expenses: 0 }
                });
                store.updatePayStubDraft('ts-1', 'ps-2', {
                    hours: { standard: 6, overtime: 0, doubletime: 0, total: 6 },
                    amounts: { bonus: 0, expenses: 0 }
                });
                store.updatePayStubDraft('ts-2', 'ps-1', {
                    hours: { standard: 4, overtime: 0, doubletime: 0, total: 4 },
                    amounts: { bonus: 0, expenses: 0 }
                });
                store.updatePayStubDraft('ts-2', 'ps-3', {
                    hours: { standard: 7, overtime: 0, doubletime: 0, total: 7 },
                    amounts: { bonus: 0, expenses: 0 }
                });

                // Set expansion states
                store.setExpansion('ts-1', 'ps-1', true);
                store.setExpansion('ts-2', 'ps-1', true);
            });

            // Verify initial state
            expect(store.getDraftForPayStub('ts-1', 'ps-1')).toBeDefined();
            expect(store.getDraftForPayStub('ts-1', 'ps-2')).toBeDefined();
            expect(store.getDraftForPayStub('ts-2', 'ps-1')).toBeDefined();
            expect(store.getDraftForPayStub('ts-2', 'ps-3')).toBeDefined();

            // Clear only ts-1
            act(() => {
                store.clearAllDrafts('ts-1');
            });

            // Verify ts-1 drafts are cleared but ts-2 remains
            expect(store.getDraftForPayStub('ts-1', 'ps-1')).toBeUndefined();
            expect(store.getDraftForPayStub('ts-1', 'ps-2')).toBeUndefined();
            expect(store.getDraftForPayStub('ts-2', 'ps-1')).toBeDefined();
            expect(store.getDraftForPayStub('ts-2', 'ps-3')).toBeDefined();

            // Verify expansion states are also isolated
            expect(store.getExpandedState('ts-1', 'ps-1')).toBe(false);
            expect(store.getExpandedState('ts-2', 'ps-1')).toBe(true);
        });

        test('UI state isolation between timesheets', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Set different UI states for different timesheets
                store.setExpansion('ts-1', 'ps-1', true);
                store.setExpansion('ts-1', 'ps-2', false);
                store.setEditingState('ts-1', 'ps-1', true);

                store.setExpansion('ts-2', 'ps-1', false);
                store.setExpansion('ts-2', 'ps-2', true);
                store.setEditingState('ts-2', 'ps-2', true);

                // Set errors for different timesheets
                store.setError('ts-1', 'ps-1', {
                    message: 'Error in ts-1',
                    severity: 'error',
                    timestamp: Date.now()
                });
                store.setError('ts-2', 'ps-1', {
                    message: 'Error in ts-2',
                    severity: 'error',
                    timestamp: Date.now()
                });
            });

            // Verify expansion state isolation
            expect(store.getExpandedState('ts-1', 'ps-1')).toBe(true);
            expect(store.getExpandedState('ts-1', 'ps-2')).toBe(false);
            expect(store.getExpandedState('ts-2', 'ps-1')).toBe(false);
            expect(store.getExpandedState('ts-2', 'ps-2')).toBe(true);

            // Verify editing state isolation
            expect(store.getEditingState('ts-1', 'ps-1')).toBe(true);
            expect(store.getEditingState('ts-1', 'ps-2')).toBe(false);
            expect(store.getEditingState('ts-2', 'ps-1')).toBe(false);
            expect(store.getEditingState('ts-2', 'ps-2')).toBe(true);

            // Verify error state isolation
            expect(store.getErrorForPayStub('ts-1', 'ps-1')?.message).toBe('Error in ts-1');
            expect(store.getErrorForPayStub('ts-2', 'ps-1')?.message).toBe('Error in ts-2');
            expect(store.getErrorForPayStub('ts-1', 'ps-2')).toBeNull();
        });
    });

    describe('Persistence and Concurrency', () => {
        test('localStorage persistence handles multiple timesheets', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Create complex multi-timesheet state
                store.updatePayStubDraft('timesheet-alpha', 'paystub-001', {
                    hours: { standard: 40, overtime: 5, doubletime: 0, total: 45 },
                    amounts: { bonus: 500, expenses: 100 }
                });

                store.updatePayStubDraft('timesheet-beta', 'paystub-002', {
                    hours: { standard: 35, overtime: 10, doubletime: 0, total: 45 },
                    amounts: { bonus: 750, expenses: 200 }
                });

                store.setExpansion('timesheet-alpha', 'paystub-001', true);
                store.setEditingState('timesheet-beta', 'paystub-002', true);
            });

            // Verify data is persisted in localStorage
            const persistedData = JSON.parse(localStorage.getItem('timesheet-ui-storage') || '{}');

            // Check that both timesheets are persisted with proper scoping
            expect(persistedData.state.draftChanges).toBeDefined();
            expect(persistedData.state.expandedPayStubs).toBeDefined();
            expect(persistedData.state.editingPayStubs).toBeDefined();

            // Simulate page reload by preserving localStorage and clearing/rehydrating state
            const storedData = localStorage.getItem('timesheet-ui-storage');
            localStorage.removeItem('timesheet-ui-storage');

            act(() => {
                store.clearAllDrafts(); // Clear in-memory state
            });

            // Restore localStorage and rehydrate
            if (storedData) {
                localStorage.setItem('timesheet-ui-storage', storedData);
            }

            act(() => {
                useTimesheetUIStore.persist.rehydrate(); // Trigger rehydration
            });

            // Verify state is restored from localStorage
            expect(store.getDraftForPayStub('timesheet-alpha', 'paystub-001')).toBeDefined();
            expect(store.getDraftForPayStub('timesheet-beta', 'paystub-002')).toBeDefined();
        });

        test('persistence payload size remains under localStorage quota', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Create large dataset (simulate 100 timesheets with 10 paystubs each)
                for (let tsIndex = 0; tsIndex < 100; tsIndex++) {
                    const timesheetId = `timesheet-${tsIndex}`;

                    for (let psIndex = 0; psIndex < 10; psIndex++) {
                        const payStubId = `paystub-${psIndex}`;

                        const standardHours = Math.random() * 40;
                        const overtimeHours = Math.random() * 10;
                        const doubletimeHours = Math.random() * 5;
                        const totalHours = standardHours + overtimeHours + doubletimeHours;

                        store.updatePayStubDraft(timesheetId, payStubId, {
                            hours: {
                                standard: standardHours,
                                overtime: overtimeHours,
                                doubletime: doubletimeHours,
                                total: totalHours
                            },
                            amounts: {
                                bonus: Math.random() * 1000,
                                expenses: Math.random() * 500
                            }
                        });

                        // Add some UI state
                        if (Math.random() > 0.5) {
                            store.setExpansion(timesheetId, payStubId, true);
                        }
                    }
                }
            });

            // Check localStorage payload size
            const persistedData = localStorage.getItem('timesheet-ui-storage');
            const sizeInMB = new Blob([persistedData || '']).size / (1024 * 1024);

            console.log(`Persistence payload size: ${sizeInMB.toFixed(2)} MB`);

            // Should remain well under 4MB localStorage quota (browser limit)
            expect(sizeInMB).toBeLessThan(4);

            // Verify data integrity
            expect(store.hasDraftChanges('timesheet-0')).toBe(true);
            expect(store.hasDraftChanges('timesheet-99')).toBe(true);
        });

        test('concurrent modifications maintain data consistency', () => {
            const store = useTimesheetUIStore.getState();

            // Simulate rapid concurrent modifications
            act(() => {
                // Batch of rapid updates that might cause race conditions
                for (let i = 0; i < 50; i++) {
                    store.updatePayStubDraft('ts-concurrent', `ps-${i}`, {
                        hours: { standard: i, overtime: 0, doubletime: 0, total: i },
                        amounts: { bonus: 0, expenses: 0 }
                    });

                    store.setExpansion('ts-concurrent', `ps-${i}`, i % 2 === 0);

                    if (i % 10 === 0) {
                        store.setError('ts-concurrent', `ps-${i}`, {
                            message: `Error ${i}`,
                            severity: 'error',
                            timestamp: Date.now()
                        });
                    }
                }
            });

            // Verify all modifications were applied correctly
            for (let i = 0; i < 50; i++) {
                const draft = store.getDraftForPayStub('ts-concurrent', `ps-${i}`);
                expect(draft?.hours?.standard).toBe(i);

                const isExpanded = store.getExpandedState('ts-concurrent', `ps-${i}`);
                expect(isExpanded).toBe(i % 2 === 0);

                if (i % 10 === 0) {
                    const error = store.getErrorForPayStub('ts-concurrent', `ps-${i}`);
                    expect(error?.message).toBe(`Error ${i}`);
                }
            }
        });
    });

    describe('Performance Under Load', () => {
        test('selector performance with large multi-timesheet datasets', () => {
            const store = useTimesheetUIStore.getState();

            // Create large dataset
            act(() => {
                for (let ts = 0; ts < 20; ts++) {
                    for (let ps = 0; ps < 50; ps++) {
                        store.updatePayStubDraft(`ts-${ts}`, `ps-${ps}`, {
                            hours: { standard: 8, overtime: 2, doubletime: 0, total: 10 },
                            amounts: { bonus: 100, expenses: 50 }
                        });
                    }
                }
            });

            // Measure selector performance
            const startTime = performance.now();

            // Perform multiple selector calls
            for (let i = 0; i < 1000; i++) {
                const tsId = `ts-${i % 20}`;
                const psId = `ps-${i % 50}`;

                store.getDraftForPayStub(tsId, psId);
                store.hasDraftChanges(tsId);
                store.getExpandedState(tsId, psId);
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            console.log(`1000 selector operations took ${duration.toFixed(2)}ms`);

            // Should complete within reasonable time (< 100ms)
            expect(duration).toBeLessThan(100);
        });

        test('memory usage remains stable with large datasets', () => {
            const store = useTimesheetUIStore.getState();

            // Measure initial memory if available
            const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

            act(() => {
                // Create and destroy large datasets to test for memory leaks
                for (let cycle = 0; cycle < 10; cycle++) {
                    // Create large dataset
                    for (let ts = 0; ts < 10; ts++) {
                        for (let ps = 0; ps < 20; ps++) {
                            store.updatePayStubDraft(`cycle-${cycle}-ts-${ts}`, `ps-${ps}`, {
                                hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
                                amounts: { bonus: 100, expenses: 0 }
                            });
                        }
                    }

                    // Clear the dataset
                    for (let ts = 0; ts < 10; ts++) {
                        store.clearAllDrafts(`cycle-${cycle}-ts-${ts}`);
                    }
                }
            });

            // Force garbage collection if available (Node.js with --expose-gc)
            if ((global as any).gc) {
                (global as any).gc();
            }

            const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;

            if (initialMemory && finalMemory) {
                const memoryGrowth = finalMemory - initialMemory;
                console.log(`Memory growth: ${(memoryGrowth / 1024 / 1024).toFixed(2)} MB`);

                // Memory growth should be minimal (< 10MB)
                expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
            }
        });
    });

    describe('Error Handling and Recovery', () => {
        test('error state isolation between timesheets', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Set errors for different timesheets
                store.setError('ts-1', 'ps-1', {
                    message: 'Network error',
                    severity: 'error',
                    timestamp: Date.now()
                });
                store.setError('ts-1', 'ps-2', {
                    message: 'Validation error',
                    severity: 'error',
                    timestamp: Date.now()
                });
                store.setError('ts-2', 'ps-1', {
                    message: 'Permission error',
                    severity: 'error',
                    timestamp: Date.now()
                });
            });

            // Verify error isolation
            expect(store.getErrorForPayStub('ts-1', 'ps-1')?.message).toBe('Network error');
            expect(store.getErrorForPayStub('ts-1', 'ps-2')?.message).toBe('Validation error');
            expect(store.getErrorForPayStub('ts-2', 'ps-1')?.message).toBe('Permission error');
            expect(store.getErrorForPayStub('ts-2', 'ps-2')).toBeNull();

            // Clear error for specific timesheet
            act(() => {
                store.setError('ts-1', 'ps-1', null);
            });

            // Verify selective error clearing
            expect(store.getErrorForPayStub('ts-1', 'ps-1')).toBeNull();
            expect(store.getErrorForPayStub('ts-1', 'ps-2')?.message).toBe('Validation error');
            expect(store.getErrorForPayStub('ts-2', 'ps-1')?.message).toBe('Permission error');
        });

        test('global state consistency during errors', () => {
            const store = useTimesheetUIStore.getState();

            act(() => {
                // Set up state
                store.updatePayStubDraft('ts-1', 'ps-1', {
                    hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
                    amounts: { bonus: 0, expenses: 0 }
                });
                // Note: setLoading() and setSaving() methods removed as they're obsolete

                // Simulate error during save
                store.setError('ts-1', 'ps-1', {
                    message: 'Save failed',
                    severity: 'error',
                    timestamp: Date.now()
                });
            });

            // Verify state consistency
            expect(store.getDraftForPayStub('ts-1', 'ps-1')).toBeDefined(); // Draft should remain
            // Note: isSaving and isLoading checks removed since methods were removed
            expect(store.getErrorForPayStub('ts-1', 'ps-1')?.message).toBe('Save failed');
        });
    });
});
