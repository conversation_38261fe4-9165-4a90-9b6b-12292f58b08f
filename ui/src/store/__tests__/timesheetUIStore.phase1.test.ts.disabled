/**
 * Unit tests for Phase 1 Zustand migration functionality
 * 
 * Tests the new state slices and actions added to replace TimesheetUIContext
 */

import { renderHook, act } from '@testing-library/react';
import { useTimesheetUIStore } from '../timesheetUIStore';
import type { TimesheetError } from '../timesheetUIStore';

// Mock the relay environment for mutation testing
const mockEnvironment = {} as any;

describe('TimesheetUIStore - Phase 1 Functionality', () => {
    beforeEach(() => {
        // Clear the store state before each test
        useTimesheetUIStore.setState({
            activeTimesheetId: null,
            draftChanges: new Map(),
            expandedPayStubs: new Set(),
            editingPayStubs: new Set(),
            errorsByPayStubId: new Map(),
            validationErrorsByPayStubId: new Map(),
            isSaving: false,
            savingPayStubIds: new Set(),
            lastSaved: null,
            globalError: null,
            showingEmployeeSelector: false,
            selectedEmployeeForAdd: null,
            selectedEmployees: new Map(),
            dateRanges: new Map(),
            viewModes: new Map(),
            columnVisibility: new Map(),
            employeeSelectorVisible: new Map(),
            selectedPayStubs: new Map(),
            markedForDeletion: new Set()
        });
    });

    describe('Validation Error Management', () => {
        it('should set and get validation errors for a PayStub', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';
            const errors = ['Error 1', 'Error 2'];

            act(() => {
                result.current.setValidationErrors(timesheetId, payStubId, errors);
            });

            expect(result.current.getValidationErrors(timesheetId, payStubId)).toEqual(errors);
        });

        it('should clear validation errors for a PayStub', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';
            const errors = ['Error 1', 'Error 2'];

            act(() => {
                result.current.setValidationErrors(timesheetId, payStubId, errors);
                result.current.clearValidationErrors(timesheetId, payStubId);
            });

            expect(result.current.getValidationErrors(timesheetId, payStubId)).toEqual([]);
        });

        it('should scope validation errors by timesheet and PayStub ID', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId1 = 'timesheet-1';
            const timesheetId2 = 'timesheet-2';
            const payStubId = 'paystub-1';
            const errors1 = ['Error 1'];
            const errors2 = ['Error 2'];

            act(() => {
                result.current.setValidationErrors(timesheetId1, payStubId, errors1);
                result.current.setValidationErrors(timesheetId2, payStubId, errors2);
            });

            expect(result.current.getValidationErrors(timesheetId1, payStubId)).toEqual(errors1);
            expect(result.current.getValidationErrors(timesheetId2, payStubId)).toEqual(errors2);
        });
    });

    describe('Global Error Management', () => {
        it('should set and clear global errors', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const error = 'Global error message';

            act(() => {
                result.current.setGlobalError(error);
            });

            expect(result.current.globalError).toBe(error);

            act(() => {
                result.current.setGlobalError(null);
            });

            expect(result.current.globalError).toBeNull();
        });
    });

    describe('Employee Selector State', () => {
        it('should show and hide employee selector', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';

            expect(result.current.showingEmployeeSelector).toBe(false);
            expect(result.current.selectedEmployeeForAdd).toBeNull();

            act(() => {
                result.current.showEmployeeSelector(timesheetId);
            });

            expect(result.current.showingEmployeeSelector).toBe(true);
            expect(result.current.selectedEmployeeForAdd).toBe(timesheetId);

            act(() => {
                result.current.hideEmployeeSelector();
            });

            expect(result.current.showingEmployeeSelector).toBe(false);
            expect(result.current.selectedEmployeeForAdd).toBeNull();
        });
    });

    describe('Individual PayStub Saving State', () => {
        it('should track saving state for individual PayStubs', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId1 = 'paystub-1';
            const payStubId2 = 'paystub-2';

            expect(result.current.isSavingPayStub(timesheetId, payStubId1)).toBe(false);
            expect(result.current.isSavingPayStub(timesheetId, payStubId2)).toBe(false);

            // Simulate marking PayStubs as saving
            act(() => {
                const key1 = result.current.createScopedKey(timesheetId, payStubId1);
                const key2 = result.current.createScopedKey(timesheetId, payStubId2);
                useTimesheetUIStore.setState((state) => {
                    const newSaving = new Set(state.savingPayStubIds);
                    newSaving.add(key1);
                    return { savingPayStubIds: newSaving };
                });
            });

            expect(result.current.isSavingPayStub(timesheetId, payStubId1)).toBe(true);
            expect(result.current.isSavingPayStub(timesheetId, payStubId2)).toBe(false);
        });
    });

    describe('Enhanced Error Management', () => {
        it('should use clearError to clear specific errors', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';
            const error: TimesheetError = {
                message: 'Test error',
                severity: 'error',
                timestamp: Date.now()
            };

            act(() => {
                result.current.setError(timesheetId, payStubId, error);
            });

            expect(result.current.getErrorForPayStub(timesheetId, payStubId)).not.toBeNull();

            act(() => {
                result.current.clearError(timesheetId, payStubId);
            });

            expect(result.current.getErrorForPayStub(timesheetId, payStubId)).toBeNull();
        });
    });

    describe('Enhanced Editing State Management', () => {
        it('should start and stop editing for specific PayStubs', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';

            expect(result.current.getEditingState(timesheetId, payStubId)).toBe(false);

            act(() => {
                result.current.startEditingPayStub(timesheetId, payStubId);
            });

            expect(result.current.getEditingState(timesheetId, payStubId)).toBe(true);

            act(() => {
                result.current.stopEditingPayStub(timesheetId);
            });

            expect(result.current.getEditingState(timesheetId, payStubId)).toBe(false);
        });

        it('should stop editing for all PayStubs in a timesheet', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId1 = 'paystub-1';
            const payStubId2 = 'paystub-2';

            act(() => {
                result.current.startEditingPayStub(timesheetId, payStubId1);
                result.current.startEditingPayStub(timesheetId, payStubId2);
            });

            expect(result.current.getEditingState(timesheetId, payStubId1)).toBe(true);
            expect(result.current.getEditingState(timesheetId, payStubId2)).toBe(true);

            act(() => {
                result.current.stopEditingPayStub(timesheetId);
            });

            expect(result.current.getEditingState(timesheetId, payStubId1)).toBe(false);
            expect(result.current.getEditingState(timesheetId, payStubId2)).toBe(false);
        });
    });

    describe('Employee Selection Flow', () => {
        it('should handle the complete employee selection flow', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const employeeId = 'employee-1';

            // Start with selector hidden
            expect(result.current.showingEmployeeSelector).toBe(false);

            // Show selector
            act(() => {
                result.current.showEmployeeSelector(timesheetId);
            });

            expect(result.current.showingEmployeeSelector).toBe(true);
            expect(result.current.selectedEmployeeForAdd).toBe(timesheetId);

            // Select employee - this should hide selector and add empty PayStub
            act(() => {
                result.current.selectEmployeeForNewPayStub(timesheetId, employeeId);
            });

            expect(result.current.showingEmployeeSelector).toBe(false);
            expect(result.current.selectedEmployeeForAdd).toBeNull();
        });
    });

    describe('Store Statistics', () => {
        it('should track store statistics correctly with new state', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';

            // Add some state
            act(() => {
                result.current.updatePayStubDraft(timesheetId, payStubId, { 
                    hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 } 
                });
                result.current.setValidationErrors(timesheetId, payStubId, ['Error']);
                result.current.startEditingPayStub(timesheetId, payStubId);
            });

            const stats = result.current.getStoreStats();

            expect(stats.totalDrafts).toBe(1);
            expect(stats.totalEditing).toBe(1);
            expect(stats.timesheetScopes).toContain(timesheetId);
        });
    });

    describe('Scoped Key Generation', () => {
        it('should create consistent scoped keys', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const timesheetId = 'timesheet-1';
            const payStubId = 'paystub-1';

            const key1 = result.current.createScopedKey(timesheetId, payStubId);
            const key2 = result.current.createScopedKey(timesheetId, payStubId);

            expect(key1).toBe(key2);
            expect(key1).toBe(`${timesheetId}:${payStubId}`);
        });

        it('should create unique keys for different IDs', () => {
            const { result } = renderHook(() => useTimesheetUIStore());
            const key1 = result.current.createScopedKey('timesheet-1', 'paystub-1');
            const key2 = result.current.createScopedKey('timesheet-1', 'paystub-2');
            const key3 = result.current.createScopedKey('timesheet-2', 'paystub-1');

            expect(key1).not.toBe(key2);
            expect(key1).not.toBe(key3);
            expect(key2).not.toBe(key3);
        });
    });
});