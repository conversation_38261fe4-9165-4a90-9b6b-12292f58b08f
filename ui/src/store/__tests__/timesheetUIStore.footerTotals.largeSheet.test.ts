/**
 * Stress tests for footer totals calculation with large datasets
 * Tests performance and correctness with 1000+ PayStubs and random draft combinations
 */

import { useTimesheetUIStore } from '../timesheetUIStore';
import type { FlatPayStubDetailDraft } from '@/src/types';

// Helper to generate random numbers
const randomBetween = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;
const randomHours = () => Math.random() * 12; // 0-12 hours
const randomMoney = () => Math.random() * 500; // 0-500 dollars

// Helper to create mock data at scale
const createLargeTimesheet = (numPayStubs: number, detailsPerPayStub: number) => {
  const payStubs = [];
  
  for (let p = 0; p < numPayStubs; p++) {
    const details = [];
    
    for (let d = 0; d < detailsPerPayStub; d++) {
      details.push({
        id: `detail-${p}-${d}`,
        payStubId: `paystub-${p}`,
        stHours: randomHours(),
        otHours: randomHours(),
        dtHours: randomHours(),
        bonus: randomMoney(),
        expenses: randomMoney(),
        workDate: '2023-01-01'
      });
    }
    
    payStubs.push({
      id: `paystub-${p}`,
      details
    });
  }
  
  return { payStubs };
};

// Helper to add random orphan drafts
const addRandomOrphanDrafts = (
  store: ReturnType<typeof useTimesheetUIStore.getState>, 
  timesheetId: string, 
  numOrphans: number,
  payStubIds: string[]
) => {
  for (let i = 0; i < numOrphans; i++) {
    const orphanId = `orphan-${i}`;
    const payStubId = payStubIds[randomBetween(0, payStubIds.length - 1)];
    
    // Random chance of being deleted
    const isDeleted = Math.random() < 0.1; // 10% chance
    
    store.updateDetailField(timesheetId, orphanId, 'stHours', randomHours(), payStubId);
    store.updateDetailField(timesheetId, orphanId, 'payStubId', payStubId, payStubId);
    
    if (Math.random() < 0.5) {
      store.updateDetailField(timesheetId, orphanId, 'otHours', randomHours(), payStubId);
    }
    
    if (Math.random() < 0.3) {
      store.updateDetailField(timesheetId, orphanId, 'dtHours', randomHours(), payStubId);
    }
    
    if (Math.random() < 0.4) {
      store.updateDetailField(timesheetId, orphanId, 'bonus', randomMoney(), payStubId);
    }
    
    if (Math.random() < 0.3) {
      store.updateDetailField(timesheetId, orphanId, 'expenses', randomMoney(), payStubId);
    }
    
    if (isDeleted) {
      store.updateDetailField(timesheetId, orphanId, '_uiDelete', true, payStubId);
    }
  }
};

// Helper to add random draft overlays to existing details
const addRandomDraftOverlays = (
  store: ReturnType<typeof useTimesheetUIStore.getState>,
  timesheetId: string,
  timesheetData: any,
  overlayPercentage: number
) => {
  for (const payStub of timesheetData.payStubs) {
    for (const detail of payStub.details) {
      if (Math.random() < overlayPercentage) {
        // Random chance of various types of changes
        if (Math.random() < 0.4) {
          store.updateDetailField(timesheetId, detail.id, 'stHours', randomHours(), payStub.id);
        }
        
        if (Math.random() < 0.2) {
          store.updateDetailField(timesheetId, detail.id, 'otHours', randomHours(), payStub.id);
        }
        
        if (Math.random() < 0.1) {
          store.updateDetailField(timesheetId, detail.id, 'dtHours', randomHours(), payStub.id);
        }
        
        if (Math.random() < 0.3) {
          store.updateDetailField(timesheetId, detail.id, 'bonus', randomMoney(), payStub.id);
        }
        
        if (Math.random() < 0.2) {
          store.updateDetailField(timesheetId, detail.id, 'expenses', randomMoney(), payStub.id);
        }
        
        // Random chance of deletion
        if (Math.random() < 0.05) {
          store.updateDetailField(timesheetId, detail.id, '_uiDelete', true, payStub.id);
        }
      }
    }
  }
};

describe('TimesheetUIStore Large Sheet Stress Tests', () => {
  let store: ReturnType<typeof useTimesheetUIStore.getState>;
  const timesheetId = 'stress-test-timesheet';

  beforeEach(() => {
    store = useTimesheetUIStore.getState();
    store.clearAllDrafts();
    store.setActiveTimesheet(timesheetId);
  });

  describe('Performance benchmarks', () => {
    it('should handle 1000 PayStubs with mixed drafts within performance budget', () => {
      const numPayStubs = 1000;
      const detailsPerPayStub = 3; // 3000 total details
      const numOrphans = 500;      // 500 orphan drafts
      
      console.log(`\n🧪 Stress Test: ${numPayStubs} PayStubs, ${numPayStubs * detailsPerPayStub} details, ${numOrphans} orphan drafts`);
      
      // Create large timesheet
      const startSetup = performance.now();
      const timesheetData = createLargeTimesheet(numPayStubs, detailsPerPayStub);
      const payStubIds = timesheetData.payStubs.map(ps => ps.id);
      
      // Add random orphan drafts
      addRandomOrphanDrafts(store, timesheetId, numOrphans, payStubIds);
      
      // Add random draft overlays (20% of existing details)
      addRandomDraftOverlays(store, timesheetId, timesheetData, 0.2);
      
      const endSetup = performance.now();
      console.log(`⏱️  Setup time: ${(endSetup - startSetup).toFixed(2)}ms`);
      
      // Measure footer totals calculation performance
      const startCalc = performance.now();
      const totals = store.selectFooterTotals(timesheetId, timesheetData);
      const endCalc = performance.now();
      
      const calcTime = endCalc - startCalc;
      console.log(`⏱️  Calculation time: ${calcTime.toFixed(2)}ms`);
      console.log(`📊 Totals: ST=${totals.stHours.toFixed(1)}, OT=${totals.otHours.toFixed(1)}, DT=${totals.dtHours.toFixed(1)}, Total=${totals.totalHours.toFixed(1)}`);
      
      // Performance assertion: should complete within 150ms for 1000 PayStubs
      expect(calcTime).toBeLessThan(150);
      
      // Correctness assertions
      expect(totals.stHours).toBeGreaterThanOrEqual(0);
      expect(totals.otHours).toBeGreaterThanOrEqual(0);
      expect(totals.dtHours).toBeGreaterThanOrEqual(0);
      expect(Math.abs(totals.totalHours - (totals.stHours + totals.otHours + totals.dtHours))).toBeLessThan(0.001);
      expect(totals.bonus).toBeGreaterThanOrEqual(0);
      expect(totals.expenses).toBeGreaterThanOrEqual(0);
      
      // All values should be finite numbers
      expect(Number.isFinite(totals.stHours)).toBe(true);
      expect(Number.isFinite(totals.otHours)).toBe(true);
      expect(Number.isFinite(totals.dtHours)).toBe(true);
      expect(Number.isFinite(totals.totalHours)).toBe(true);
      expect(Number.isFinite(totals.bonus)).toBe(true);
      expect(Number.isFinite(totals.expenses)).toBe(true);
    });

    it('should handle 500 PayStubs with heavy orphan draft usage', () => {
      const numPayStubs = 500;
      const detailsPerPayStub = 2; // 1000 total details
      const numOrphans = 1000;     // More orphans than server details
      
      console.log(`\n🧪 Heavy Orphan Test: ${numPayStubs} PayStubs, ${numOrphans} orphan drafts`);
      
      const timesheetData = createLargeTimesheet(numPayStubs, detailsPerPayStub);
      const payStubIds = timesheetData.payStubs.map(ps => ps.id);
      
      // Add many orphan drafts
      const startOrphans = performance.now();
      addRandomOrphanDrafts(store, timesheetId, numOrphans, payStubIds);
      const endOrphans = performance.now();
      console.log(`⏱️  Orphan creation time: ${(endOrphans - startOrphans).toFixed(2)}ms`);
      
      // Measure calculation with heavy orphan load
      const startCalc = performance.now();
      const totals = store.selectFooterTotals(timesheetId, timesheetData);
      const endCalc = performance.now();
      
      const calcTime = endCalc - startCalc;
      console.log(`⏱️  Heavy orphan calculation time: ${calcTime.toFixed(2)}ms`);
      
      // Should still be within performance budget
      expect(calcTime).toBeLessThan(100);
      
      // Totals should be valid
      expect(Math.abs(totals.totalHours - (totals.stHours + totals.otHours + totals.dtHours))).toBeLessThan(0.001);
    });

    it('should maintain performance with repeated calculations', () => {
      const numPayStubs = 200;
      const detailsPerPayStub = 4;
      const numOrphans = 100;
      const numIterations = 100;
      
      console.log(`\n🧪 Repeated Calculation Test: ${numIterations} iterations`);
      
      const timesheetData = createLargeTimesheet(numPayStubs, detailsPerPayStub);
      const payStubIds = timesheetData.payStubs.map(ps => ps.id);
      addRandomOrphanDrafts(store, timesheetId, numOrphans, payStubIds);
      
      const times: number[] = [];
      
      // Perform multiple calculations to test consistency
      for (let i = 0; i < numIterations; i++) {
        const start = performance.now();
        store.selectFooterTotals(timesheetId, timesheetData);
        const end = performance.now();
        times.push(end - start);
      }
      
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      console.log(`⏱️  Average time: ${avgTime.toFixed(2)}ms, Min: ${minTime.toFixed(2)}ms, Max: ${maxTime.toFixed(2)}ms`);
      
      // Average should be well within budget
      expect(avgTime).toBeLessThan(20);
      
      // Maximum should not exceed budget significantly
      expect(maxTime).toBeLessThan(50);
    });
  });

  describe('Correctness with large datasets', () => {
    it('should correctly aggregate thousands of details with mixed scenarios', () => {
      const numPayStubs = 100;
      const detailsPerPayStub = 5;
      const numOrphans = 200;
      
      const timesheetData = createLargeTimesheet(numPayStubs, detailsPerPayStub);
      const payStubIds = timesheetData.payStubs.map(ps => ps.id);
      
      // Calculate expected totals from server data
      let expectedStHours = 0;
      let expectedOtHours = 0;
      let expectedDtHours = 0;
      let expectedBonus = 0;
      let expectedExpenses = 0;
      
      for (const payStub of timesheetData.payStubs) {
        for (const detail of payStub.details) {
          expectedStHours += detail.stHours;
          expectedOtHours += detail.otHours;
          expectedDtHours += detail.dtHours;
          expectedBonus += detail.bonus;
          expectedExpenses += detail.expenses;
        }
      }
      
      // Add controlled orphan drafts with known values
      const orphanStHours = 5;
      const orphanOtHours = 2;
      const orphanBonus = 100;
      
      for (let i = 0; i < 10; i++) {
        const orphanId = `controlled-orphan-${i}`;
        const payStubId = payStubIds[0]; // All to first PayStub for simplicity
        
        store.updateDetailField(timesheetId, orphanId, 'stHours', orphanStHours, payStubId);
        store.updateDetailField(timesheetId, orphanId, 'otHours', orphanOtHours, payStubId);
        store.updateDetailField(timesheetId, orphanId, 'bonus', orphanBonus, payStubId);
        store.updateDetailField(timesheetId, orphanId, 'payStubId', payStubId, payStubId);
        
        expectedStHours += orphanStHours;
        expectedOtHours += orphanOtHours;
        expectedBonus += orphanBonus;
      }
      
      const totals = store.selectFooterTotals(timesheetId, timesheetData);
      
      // Allow for small floating point differences
      expect(Math.abs(totals.stHours - expectedStHours)).toBeLessThan(0.01);
      expect(Math.abs(totals.otHours - expectedOtHours)).toBeLessThan(0.01);
      expect(Math.abs(totals.dtHours - expectedDtHours)).toBeLessThan(0.01);
      expect(Math.abs(totals.bonus - expectedBonus)).toBeLessThan(0.01);
      expect(Math.abs(totals.expenses - expectedExpenses)).toBeLessThan(0.01);
      expect(Math.abs(totals.totalHours - (expectedStHours + expectedOtHours + expectedDtHours))).toBeLessThan(0.01);
    });

    it('should handle edge cases in large datasets', () => {
      const numPayStubs = 100;
      
      // Create timesheet with various edge cases
      const payStubs = [];
      for (let i = 0; i < numPayStubs; i++) {
        const details = [];
        
        // Add details with various edge case values
        details.push({
          id: `detail-${i}-null`,
          payStubId: `paystub-${i}`,
          stHours: null,
          otHours: undefined,
          dtHours: 0,
          bonus: null,
          expenses: undefined
        });
        
        details.push({
          id: `detail-${i}-negative`,
          payStubId: `paystub-${i}`,
          stHours: -1, // Should be handled gracefully
          otHours: 2,
          dtHours: 0,
          bonus: 0,
          expenses: 0
        });
        
        details.push({
          id: `detail-${i}-string`,
          payStubId: `paystub-${i}`,
          stHours: 'invalid' as any, // Should be handled gracefully
          otHours: 1,
          dtHours: 0,
          bonus: 50,
          expenses: 25
        });
        
        payStubs.push({
          id: `paystub-${i}`,
          details
        });
      }
      
      const timesheetData = { payStubs };
      
      // Should not throw errors
      expect(() => {
        const totals = store.selectFooterTotals(timesheetId, timesheetData);
        
        // All values should be finite numbers despite invalid input
        expect(Number.isFinite(totals.stHours)).toBe(true);
        expect(Number.isFinite(totals.otHours)).toBe(true);
        expect(Number.isFinite(totals.dtHours)).toBe(true);
        expect(Number.isFinite(totals.totalHours)).toBe(true);
        expect(Number.isFinite(totals.bonus)).toBe(true);
        expect(Number.isFinite(totals.expenses)).toBe(true);
      }).not.toThrow();
    });
  });

  describe('Memory usage and cleanup', () => {
    it('should not leak memory with repeated large operations', () => {
      const numIterations = 10;
      const initialMemory = process.memoryUsage().heapUsed;
      
      for (let iteration = 0; iteration < numIterations; iteration++) {
        // Create and destroy large datasets
        const timesheetData = createLargeTimesheet(500, 3);
        const payStubIds = timesheetData.payStubs.map(ps => ps.id);
        
        addRandomOrphanDrafts(store, `${timesheetId}-${iteration}`, 200, payStubIds);
        store.selectFooterTotals(`${timesheetId}-${iteration}`, timesheetData);
        
        // Clear everything for this iteration
        store.clearAllDrafts(`${timesheetId}-${iteration}`);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      console.log(`\n💾 Memory usage: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase`);
      
      // Memory increase should be reasonable (less than 50MB for stress test)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Cross-timesheet isolation at scale', () => {
    it('should maintain isolation with multiple large timesheets', () => {
      const numTimesheets = 10;
      const numPayStubsPerTimesheet = 100;
      const numOrphansPerTimesheet = 50;
      
      const timesheetDatas: any[] = [];
      
      // Create multiple large timesheets
      for (let t = 0; t < numTimesheets; t++) {
        const currentTimesheetId = `${timesheetId}-${t}`;
        const timesheetData = createLargeTimesheet(numPayStubsPerTimesheet, 2);
        const payStubIds = timesheetData.payStubs.map(ps => ps.id);
        
        addRandomOrphanDrafts(store, currentTimesheetId, numOrphansPerTimesheet, payStubIds);
        timesheetDatas.push({ id: currentTimesheetId, data: timesheetData });
      }
      
      // Calculate totals for each timesheet and verify isolation
      const allTotals: any[] = [];
      
      for (const { id, data } of timesheetDatas) {
        const totals = store.selectFooterTotals(id, data);
        allTotals.push(totals);
        
        // Each timesheet should have valid totals (allow for small floating point differences)
        expect(Math.abs(totals.totalHours - (totals.stHours + totals.otHours + totals.dtHours))).toBeLessThan(0.001);
        expect(totals.totalHours).toBeGreaterThan(0);
      }
      
      // Totals should vary between timesheets (proving isolation)
      const uniqueTotals = new Set(allTotals.map(t => t.totalHours));
      expect(uniqueTotals.size).toBeGreaterThan(1);
      
      console.log(`\n🔒 Isolation Test: ${numTimesheets} timesheets with unique totals: ${uniqueTotals.size}`);
    });
  });
});