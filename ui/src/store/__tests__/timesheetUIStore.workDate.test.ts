/**
 * Test for workDate preservation in the draft system
 */

import { useTimesheetUIStore } from '../timesheetUIStore';

describe('TimesheetUIStore - workDate preservation', () => {
    beforeEach(() => {
        // Reset store state before each test
        useTimesheetUIStore.setState({
            detailDrafts: new Map(),
            payStubDrafts: new Map(),
            expandedPayStubs: new Set(),
            editingPayStubs: new Set(),
            errorsByPayStubId: new Map(),
            validationErrorsByPayStubId: new Map(),
            markedForDeletion: new Set(),
            isSaving: false,
            savingPayStubIds: new Set(),
            lastSaved: null,
            activeTimesheetId: null,
            draftsVersion: 0
        });
    });

    it('should preserve workDate when creating first draft from placeholder detail', () => {
        const store = useTimesheetUIStore.getState();
        const timesheetId = 'timesheet-123';
        const detailId = 'placeholder-paystub-456-2024-01-15';
        const payStubId = 'paystub-456';
        
        // Simulate original placeholder detail with workDate
        const originalDetail = {
            workDate: '2024-01-15'
        };

        // User edits stHours for the first time
        store.updateDetailField(timesheetId, detailId, 'stHours', 8, payStubId, originalDetail);

        // Check that the draft was created with workDate preserved
        const draft = store.getDetailDraft(timesheetId, detailId);
        
        expect(draft).toBeDefined();
        expect(draft!.stHours).toBe(8);
        expect(draft!.workDate).toBe('2024-01-15'); // workDate should be preserved
        expect(draft!.payStubId).toBe(payStubId);
        expect(draft!.id).toBe(detailId);
    });

    it('should not overwrite existing workDate in subsequent edits', () => {
        const store = useTimesheetUIStore.getState();
        const timesheetId = 'timesheet-123';
        const detailId = 'placeholder-paystub-456-2024-01-15';
        const payStubId = 'paystub-456';
        
        // First edit with original detail
        const originalDetail = {
            workDate: '2024-01-15'
        };
        store.updateDetailField(timesheetId, detailId, 'stHours', 8, payStubId, originalDetail);

        // Second edit without original detail (different workDate)
        store.updateDetailField(timesheetId, detailId, 'otHours', 2, payStubId, { workDate: '2024-01-16' });

        // Check that the original workDate is preserved
        const draft = store.getDetailDraft(timesheetId, detailId);
        
        expect(draft).toBeDefined();
        expect(draft!.stHours).toBe(8);
        expect(draft!.otHours).toBe(2);
        expect(draft!.workDate).toBe('2024-01-15'); // Original workDate should remain
    });

    it('should handle editing existing details without overwriting workDate', () => {
        const store = useTimesheetUIStore.getState();
        const timesheetId = 'timesheet-123';
        const detailId = 'detail-existing-789';
        const payStubId = 'paystub-456';
        
        // Simulate existing detail that already has a draft with workDate
        store.setDetailDraft(timesheetId, detailId, {
            id: detailId,
            payStubId: payStubId,
            workDate: '2024-01-10',
            stHours: 6,
            _uiLastModified: Date.now()
        });

        // User edits another field
        store.updateDetailField(timesheetId, detailId, 'jobCode', 'PROJ-001', payStubId, { workDate: '2024-01-11' });

        // Check that the existing workDate is not changed
        const draft = store.getDetailDraft(timesheetId, detailId);
        
        expect(draft).toBeDefined();
        expect(draft!.stHours).toBe(6);
        expect(draft!.jobCode).toBe('PROJ-001');
        expect(draft!.workDate).toBe('2024-01-10'); // Existing workDate should remain unchanged
    });

    it('should set workDate when original detail provides it and draft is empty', () => {
        const store = useTimesheetUIStore.getState();
        const timesheetId = 'timesheet-123';
        const detailId = 'new-detail-999';
        const payStubId = 'paystub-456';
        
        // No existing draft, but original detail has workDate
        const originalDetail = {
            workDate: '2024-01-20'
        };

        // User edits a field for the first time
        store.updateDetailField(timesheetId, detailId, 'bonus', 100, payStubId, originalDetail);

        // Check that workDate was set from original detail
        const draft = store.getDetailDraft(timesheetId, detailId);
        
        expect(draft).toBeDefined();
        expect(draft!.bonus).toBe(100);
        expect(draft!.workDate).toBe('2024-01-20');
    });

    it('should work without originalDetail parameter (backward compatibility)', () => {
        const store = useTimesheetUIStore.getState();
        const timesheetId = 'timesheet-123';
        const detailId = 'detail-compat-test';
        const payStubId = 'paystub-456';

        // Call without originalDetail parameter
        store.updateDetailField(timesheetId, detailId, 'expenses', 50, payStubId);

        // Check that the draft was created without workDate
        const draft = store.getDetailDraft(timesheetId, detailId);
        
        expect(draft).toBeDefined();
        expect(draft!.expenses).toBe(50);
        expect(draft!.workDate).toBeUndefined(); // No workDate should be set
        expect(draft!.payStubId).toBe(payStubId);
    });
});