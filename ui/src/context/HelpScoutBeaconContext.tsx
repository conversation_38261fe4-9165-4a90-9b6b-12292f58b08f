import React, { createContext, useContext, useState, useRef, ReactNode, useCallback, useEffect } from 'react';

// Configuration for HelpScout Beacon
export const BEACON_CONFIG = {
    display: {
        style: 'iconAndText' as const,
        position: 'right' as const,
        zIndex: 10000,
        text: 'Help'
    },
    appearance: {
        color: '#0066CC'
    }
};

interface BeaconState {
    currentGuid: string | null;
    isInitialized: boolean;
}

interface BeaconContextValue extends BeaconState {
    initializeBeacon: (guid: string) => Promise<void>;
    destroyBeacon: () => void;
}

const HelpScoutBeaconContext = createContext<BeaconContextValue | undefined>(undefined);

interface HelpScoutBeaconProviderProps {
    children: ReactNode;
}

export const HelpScoutBeaconProvider: React.FC<HelpScoutBeaconProviderProps> = ({ children }) => {
    const [state, setState] = useState<BeaconState>({
        currentGuid: null,
        isInitialized: false
    });
    
    const initializationPromiseRef = useRef<Promise<void> | null>(null);
    const isInitializingRef = useRef(false);
    
    // Refs for state checks in callbacks to avoid stale closures
    const currentGuidRef = useRef<string | null>(null);
    const isInitializedRef = useRef(false);

    // Update refs when state changes
    useEffect(() => {
        currentGuidRef.current = state.currentGuid;
        isInitializedRef.current = state.isInitialized;
    }, [state.currentGuid, state.isInitialized]);

    // Load Beacon script if not already loaded
    useEffect(() => {
        if (!document.getElementById('beacon-script')) {
            const script = document.createElement('script');
            script.id = 'beacon-script';
            script.type = 'text/javascript';
            script.async = true;
            script.src = 'https://beacon-v2.helpscout.net';
            
            script.onerror = () => {
                console.error('[HelpScout Beacon] Failed to load Beacon script');
            };
            
            document.head.appendChild(script);
        }
    }, []);

    const waitForBeacon = useCallback((): Promise<void> => {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds max wait
            
            const checkBeacon = () => {
                if (typeof window.Beacon === 'function') {
                    resolve();
                } else if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(checkBeacon, 100);
                } else {
                    console.error('[HelpScout Beacon] Beacon failed to load after 5 seconds');
                    resolve(); // Resolve anyway to prevent hanging
                }
            };
            
            checkBeacon();
        });
    }, []);

    const initializeBeacon = useCallback(async (guid: string): Promise<void> => {
        // If already initialized with same GUID, do nothing
        if (currentGuidRef.current === guid && isInitializedRef.current) {
            return;
        }

        // Prevent concurrent initializations
        if (isInitializingRef.current && initializationPromiseRef.current) {
            await initializationPromiseRef.current;
            return;
        }

        isInitializingRef.current = true;

        initializationPromiseRef.current = (async () => {
            try {
                // Wait for Beacon to be available
                await waitForBeacon();

                if (!window.Beacon) {
                    throw new Error('Beacon is not available');
                }

                // Destroy existing beacon if switching GUIDs
                if (currentGuidRef.current && currentGuidRef.current !== guid) {
                    try {
                        window.Beacon('destroy');
                        // Update state to reflect destruction
                        setState({
                            currentGuid: null,
                            isInitialized: false
                        });
                        // Small delay to ensure cleanup
                        await new Promise(resolve => setTimeout(resolve, 100));
                    } catch (e) {
                        // Ignore destroy errors
                        console.warn('[HelpScout Beacon] Error destroying previous beacon:', e);
                    }
                }

                // Initialize new beacon
                window.Beacon('init', guid);
                
                // Configure beacon
                window.Beacon('config', {
                    ...BEACON_CONFIG.appearance,
                    display: BEACON_CONFIG.display
                });
                
                // Don't open beacon automatically - let user click to open

                // Update state
                setState({
                    currentGuid: guid,
                    isInitialized: true
                });

                console.log('[HelpScout Beacon] Successfully initialized with GUID:', guid);

            } catch (error) {
                console.error('[HelpScout Beacon] Initialization error:', error);
                setState(prev => ({ ...prev, isInitialized: false }));
            } finally {
                isInitializingRef.current = false;
            }
        })();

        await initializationPromiseRef.current;
    }, [waitForBeacon]); // Only waitForBeacon dependency

    const destroyBeacon = useCallback(() => {
        try {
            if (window.Beacon) {
                window.Beacon('destroy');
                // Update state after destroying
                setState({
                    currentGuid: null,
                    isInitialized: false
                });
            } else {
                setState({
                    currentGuid: null,
                    isInitialized: false
                });
            }
            isInitializingRef.current = false;
            initializationPromiseRef.current = null;
        } catch (e) {
            console.error('[HelpScout Beacon] Error destroying beacon:', e);
        }
    }, []);

    const value: BeaconContextValue = {
        ...state,
        initializeBeacon,
        destroyBeacon
    };

    return (
        <HelpScoutBeaconContext.Provider value={value}>
            {children}
        </HelpScoutBeaconContext.Provider>
    );
};

export const useHelpScoutBeaconContext = (): BeaconContextValue => {
    const context = useContext(HelpScoutBeaconContext);
    
    if (!context) {
        throw new Error('useHelpScoutBeaconContext must be used within HelpScoutBeaconProvider');
    }
    
    return context;
}; 