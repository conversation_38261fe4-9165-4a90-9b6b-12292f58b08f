/**
 * Nominal Type System for GraphQL Global IDs
 *
 * This module provides type-safe handling of Relay Global IDs by using
 * TypeScript's nominal typing to distinguish between different entity types
 * and prevent accidental mixing of ID types.
 *
 * Phase 2.1 Implementation: Strong Type Safety Foundation
 */

/**
 * Base nominal type for all Global IDs
 * This prevents accidental string operations on Global IDs
 */
export type GlobalID<T extends string = string> = string & {
    readonly __globalId: true;
    readonly __entityType: T;
};

/**
 * Specific Global ID types for each entity
 */
export type PayStubGlobalID = GlobalID<'PayStub'>;
export type PayStubDetailGlobalID = GlobalID<'PayStubDetail'>;
export type EmployeeGlobalID = GlobalID<'Employee'>;
export type TimeSheetGlobalID = GlobalID<'TimeSheet'>;
export type ClientTempGlobalID = GlobalID<'ClientTemp'>;

/**
 * Union type for all valid Global ID types
 */
export type AnyGlobalID = PayStubGlobalID | PayStubDetailGlobalID | EmployeeGlobalID | TimeSheetGlobalID | ClientTempGlobalID;

/**
 * Type guard functions for runtime type checking
 */
export const GlobalIDGuards = {
    /**
     * Checks if a string is a valid Global ID format
     */
    isGlobalIDFormat(value: string): value is GlobalID {
        try {
            // Basic validation: Global IDs are base64 encoded and contain ':'
            const decoded = typeof atob === 'function' ? atob(value) : Buffer.from(value, 'base64').toString('binary');
            return decoded.includes(':');
        } catch {
            return false;
        }
    },

    /**
     * Checks if a string is a client temporary ID
     */
    isClientTempID(value: string): value is ClientTempGlobalID {
        return value.startsWith('client:');
    },

    /**
     * Checks if a value is any type of Global ID
     */
    isAnyGlobalID: (value: unknown): value is AnyGlobalID => {
        return typeof value === 'string' && (GlobalIDGuards.isGlobalIDFormat(value) || GlobalIDGuards.isClientTempID(value));
    }
};

/**
 * Type assertion functions for safe casting
 */
export function assertGlobalID<T extends string>(value: string, expectedType: T): asserts value is GlobalID<T> {
    if (!GlobalIDGuards.isGlobalIDFormat(value) && !GlobalIDGuards.isClientTempID(value)) {
        throw new Error(`Invalid Global ID format: "${value}". Expected base64-encoded Global ID or client temporary ID.`);
    }
    // Note: expectedType is used for type assertion but not runtime validation
    void expectedType; // Suppress unused parameter warning
}

export function assertPayStubID(value: unknown): asserts value is PayStubGlobalID {
    if (typeof value !== 'string') {
        throw new Error(`PayStub ID must be a string, got: ${typeof value}`);
    }
    assertGlobalID(value, 'PayStub');
}

export function assertPayStubDetailID(value: unknown): asserts value is PayStubDetailGlobalID {
    if (typeof value !== 'string') {
        throw new Error(`PayStubDetail ID must be a string, got: ${typeof value}`);
    }
    assertGlobalID(value, 'PayStubDetail');
}

export function assertEmployeeID(value: unknown): asserts value is EmployeeGlobalID {
    if (typeof value !== 'string') {
        throw new Error(`Employee ID must be a string, got: ${typeof value}`);
    }
    assertGlobalID(value, 'Employee');
}

export function assertTimeSheetID(value: unknown): asserts value is TimeSheetGlobalID {
    if (typeof value !== 'string') {
        throw new Error(`TimeSheet ID must be a string, got: ${typeof value}`);
    }
    assertGlobalID(value, 'TimeSheet');
}

export const GlobalIDAssertions = {
    assertGlobalID,
    assertPayStubID,
    assertPayStubDetailID,
    assertEmployeeID,
    assertTimeSheetID
};

/**
 * Safe conversion functions that validate before casting
 */
export const GlobalIDConverters = {
    /**
     * Safely converts a string to a typed Global ID with validation
     */
    toGlobalID<T extends string>(value: string, expectedType: T): GlobalID<T> {
        assertGlobalID(value, expectedType);
        return value;
    },

    /**
     * Safely converts unknown value to PayStub Global ID
     */
    toPayStubID(value: unknown): PayStubGlobalID {
        assertPayStubID(value);
        return value;
    },

    /**
     * Safely converts unknown value to PayStubDetail Global ID
     */
    toPayStubDetailID(value: unknown): PayStubDetailGlobalID {
        assertPayStubDetailID(value);
        return value;
    },

    /**
     * Safely converts unknown value to Employee Global ID
     */
    toEmployeeID(value: unknown): EmployeeGlobalID {
        assertEmployeeID(value);
        return value;
    },

    /**
     * Safely converts unknown value to TimeSheet Global ID
     */
    toTimeSheetID(value: unknown): TimeSheetGlobalID {
        assertTimeSheetID(value);
        return value;
    },

    /**
     * Safely converts string to Client Temp Global ID
     */
    toClientTempID(value: string): ClientTempGlobalID {
        if (!GlobalIDGuards.isClientTempID(value)) {
            throw new Error(`Invalid client temp ID format: "${value}". Expected "client:" prefix.`);
        }
        return value;
    }
};

/**
 * Utility functions for working with Global IDs
 */
export const GlobalIDUtils = {
    /**
     * Extracts the entity type from a Global ID (if possible)
     */
    getEntityType(globalId: AnyGlobalID): string | null {
        if (GlobalIDGuards.isClientTempID(globalId)) {
            return 'ClientTemp';
        }

        try {
            const decoded = atob(globalId);
            const parts = decoded.split(':');
            return parts[0] || null;
        } catch {
            return null;
        }
    },

    /**
     * Extracts the numeric ID from a Global ID (if possible)
     */
    getNumericId(globalId: AnyGlobalID): number | null {
        if (GlobalIDGuards.isClientTempID(globalId)) {
            return null; // Client temp IDs don't have numeric IDs
        }

        try {
            const decoded = atob(globalId);
            const parts = decoded.split(':');
            const numericId = parseInt(parts[1], 10);
            return isNaN(numericId) ? null : numericId;
        } catch {
            return null;
        }
    },

    /**
     * Checks if two Global IDs refer to the same entity
     */
    areEqual(id1: AnyGlobalID, id2: AnyGlobalID): boolean {
        return id1 === id2;
    },

    /**
     * Sorts an array of Global IDs by their numeric component (server IDs first, then client IDs)
     */
    sortGlobalIds(ids: AnyGlobalID[]): AnyGlobalID[] {
        return [...ids].sort((a, b) => {
            const aIsClient = GlobalIDGuards.isClientTempID(a);
            const bIsClient = GlobalIDGuards.isClientTempID(b);

            // Server IDs come first
            if (aIsClient && !bIsClient) return 1;
            if (!aIsClient && bIsClient) return -1;

            // If both are the same type, sort by string comparison
            return a.localeCompare(b);
        });
    }
};
