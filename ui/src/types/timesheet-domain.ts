/**
 * Domain Model Types for Timesheet Components
 *
 * Phase 1: Foundation - Domain Model Types
 *
 * These types represent the UI-friendly domain models that components work with.
 * They provide a clean interface layer above the GraphQL wire format,
 * making components easier to test, reuse, and reason about.
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ UI Components (Domain Models)           │ ← THIS FILE
 * ├─────────────────────────────────────────┤
 * │ Conversion Layer (Mappers)              │ ← Transform between layers
 * ├─────────────────────────────────────────┤
 * │ GraphQL Operations (Wire Format)        │ ← Exact server contract
 * └─────────────────────────────────────────┘
 */

// =============================================================================
// CORE DOMAIN MODELS
// =============================================================================

/**
 * Domain model for the overall timesheet structure
 * This provides a clean, UI-friendly interface for timesheet data
 */
export interface TimesheetDomainModel {
    id: string;
    numericId?: number;
    name: string;
    status: string;
    type?: string;
    payPeriodEndDate: string; // ISO 8601 date string
    employerGuid: string;

    // PayStub collection
    payStubs: PayStubDomainModel[];

    // Display settings
    settings: {
        showDTHoursColumn: boolean;
        showCostCenterColumn: boolean;
        showBonusColumn: boolean;
        showExpensesColumn: boolean;
        showEarningsCodesColumn: boolean;
        readOnly?: boolean;
    };

    // Metadata
    meta: {
        canEdit: boolean;
        lastModified?: Date;
        modifiedByUserId?: string;
        creationDate?: Date;
        hoursWorked?: number;
        totalCount?: number; // For pagination
    };
}

/**
 * Domain model for individual PayStub (employee timesheet entry)
 * Represents one employee's time entries for a pay period
 */
export interface PayStubDomainModel {
    id: string;
    employeeId: string; // Standardized as string for consistency
    employeeName: string;
    name?: string; // PayStub name/description

    // Aggregated hours (computed from details)
    hours: {
        standard: number;
        overtime: number;
        doubletime: number;
        total: number; // Computed: standard + overtime + doubletime
    };

    // Aggregated monetary amounts (computed from details)
    amounts: {
        bonus: number;
        expenses: number;
    };

    // Detail entries for this PayStub
    details: PayStubDetailDomainModel[];

    // Employee information (denormalized for convenience)
    employee: {
        id: string;
        firstName?: string;
        lastName?: string;
        fullName: string; // Computed: "LastName, FirstName"
        externalEmployeeId?: string;
        active?: boolean;
    };

    // UI state (not persisted to server)
    ui: {
        expanded: boolean;
        isEditing: boolean;
        hasErrors: boolean;
        isSelected: boolean;
    };
}

/**
 * Domain model for individual work day entries within a PayStub
 * Represents a single day's work for an employee
 */
export interface PayStubDetailDomainModel {
    id: string;
    payStubId: string;
    reportLineItemId?: number;

    // Date and naming
    workDate: string; // ISO 8601 date string (YYYY-MM-DD)
    dayName: string; // Computed: "Monday", "Tuesday", etc.
    name?: string; // Custom name/description for the entry

    // Hours worked
    hours: {
        standard: number;
        overtime: number;
        doubletime: number;
        total: number; // Computed: standard + overtime + doubletime
    };

    // Job/classification information
    job: {
        jobCode?: string;
        costCenter?: string;
        hourlyRate?: number;
    };

    // Agreement and classification IDs
    agreements: {
        agreementId?: number;
        classificationId?: number;
        subClassificationId?: number;
    };

    // Monetary amounts
    amounts: {
        bonus: number;
        expenses: number;
    };

    // Earnings classification
    earnings: {
        earningsCode?: string;
        earningsCodeText?: string; // Human-readable earnings code description
    };

    // Employee reference (for convenience in processing)
    employeeId: string;

    // UI state (not persisted to server)
    ui: {
        isEditing: boolean;
        hasErrors: boolean;
        isSelected: boolean;
        validationErrors?: string[];
    };
}

// =============================================================================
// SUPPORTING DOMAIN MODELS
// =============================================================================

/**
 * Domain model for employee information
 * Simplified employee representation for timesheet context
 */
export interface EmployeeDomainModel {
    id: string;
    employeeId: string; // External/business employee ID

    // Personal information
    name: {
        first?: string;
        last?: string;
        full: string; // Computed display name
        middle?: string;
        suffix?: string;
    };

    // Employment information
    employment: {
        active: boolean;
        externalEmployeeId?: string;
        dateOfHire?: string; // ISO 8601 date string
        dateOfTermination?: string; // ISO 8601 date string
    };

    // Security/privacy
    ssn?: string; // Masked or redacted as appropriate

    // Default settings for new timesheet entries
    defaults?: {
        agreementId?: number;
        classificationId?: number;
        subClassificationId?: number;
        hourlyRate?: number;
        costCenter?: string;
    };
}

/**
 * Domain model for work agreements
 */
export interface AgreementDomainModel {
    id: string;
    name: string;
    description?: string;

    // For backward compatibility with existing UI
    value: string; // Maps to id
    text: string; // Maps to name
}

/**
 * Domain model for job classifications
 */
export interface ClassificationDomainModel {
    id: string;
    name: string;
    description?: string;
    agreementId?: string; // Parent agreement

    // For backward compatibility with existing UI
    value: string; // Maps to id
    text: string; // Maps to name
}

/**
 * Domain model for sub-classifications
 */
export interface SubClassificationDomainModel {
    id: string;
    name: string;
    description?: string;
    classificationId?: string; // Parent classification

    // For backward compatibility with existing UI
    value: string; // Maps to id
    text: string; // Maps to name
}

/**
 * Domain model for earnings codes
 */
export interface EarningsCodeDomainModel {
    code: string; // The short code (e.g., 'VAC', 'SICK')
    name: string; // Human-readable name
    description?: string;

    // For backward compatibility with existing UI
    value: string; // Maps to code
    text: string; // Maps to name
}

// =============================================================================
// COLLECTION TYPES
// =============================================================================

/**
 * Domain model for a collection of timesheets with pagination
 */
export interface TimesheetCollectionDomainModel {
    timesheets: TimesheetDomainModel[];
    pagination: {
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        totalCount: number;
        currentPage: number;
        pageSize: number;
    };
    filters?: {
        status?: string[];
        employerGuid?: string;
        dateRange?: {
            start: string;
            end: string;
        };
    };
}

/**
 * Domain model for bulk operations
 */
export interface BulkOperationDomainModel {
    operation: 'add' | 'modify' | 'delete';
    targets: {
        timesheetIds?: string[];
        payStubIds?: string[];
        detailIds?: string[];
    };
    data?: Record<string, unknown>; // Operation-specific data
    progress: {
        total: number;
        completed: number;
        failed: number;
        status: 'pending' | 'running' | 'completed' | 'failed';
    };
}

// =============================================================================
// VALIDATION TYPES
// =============================================================================

/**
 * Domain model for validation results
 */
export interface ValidationResultDomainModel {
    isValid: boolean;
    errors: ValidationErrorDomainModel[];
    warnings: ValidationWarningDomainModel[];
}

export interface ValidationErrorDomainModel {
    field: string;
    message: string;
    code: string;
    severity: 'error' | 'warning' | 'info';
    context?: {
        timesheetId?: string;
        payStubId?: string;
        detailId?: string;
    };
}

export interface ValidationWarningDomainModel {
    field: string;
    message: string;
    code: string;
    context?: {
        timesheetId?: string;
        payStubId?: string;
        detailId?: string;
    };
}

// =============================================================================
// UI STATE MODELS
// =============================================================================

/**
 * Domain model for timesheet UI state
 * Represents non-persisted UI state for the timesheet interface
 */
export interface TimesheetUIDomainModel {
    // Selection state
    selection: {
        selectedTimesheetId?: string;
        selectedPayStubIds: Set<string>;
        selectedDetailIds: Set<string>;
    };

    // View state
    view: {
        expandedPayStubIds: Set<string>;
        hiddenColumns: Set<string>;
        sortBy?: {
            field: string;
            direction: 'asc' | 'desc';
        };
        filterBy?: {
            [key: string]: unknown;
        };
    };

    // Edit state
    edit: {
        mode: 'view' | 'edit' | 'add';
        editingPayStubId?: string;
        editingDetailId?: string;
        hasUnsavedChanges: boolean;
        drafts: {
            payStubs: Map<string, Partial<PayStubDomainModel>>;
            details: Map<string, Partial<PayStubDetailDomainModel>>;
        };
    };

    // Loading and error state
    status: {
        isLoading: boolean;
        isSaving: boolean;
        error?: string;
        lastSaved?: Date;
    };
}

// =============================================================================
// TYPE UTILITIES
// =============================================================================

/**
 * Utility types for common operations
 */
export type PayStubDraft = Partial<PayStubDomainModel>;
export type PayStubDetailDraft = Partial<PayStubDetailDomainModel>;
export type TimesheetDraft = Partial<TimesheetDomainModel>;

/**
 * Required field types for creation operations
 */
export type PayStubCreateModel = Required<Pick<PayStubDomainModel, 'employeeId' | 'employeeName'>> & Partial<PayStubDomainModel>;

export type PayStubDetailCreateModel = Required<Pick<PayStubDetailDomainModel, 'workDate' | 'payStubId' | 'employeeId'>> &
    Partial<PayStubDetailDomainModel>;

export type TimesheetCreateModel = Required<Pick<TimesheetDomainModel, 'name' | 'employerGuid'>> & Partial<TimesheetDomainModel>;

/**
 * Update-only types (exclude computed/readonly fields)
 */
export type PayStubUpdateModel = Omit<Partial<PayStubDomainModel>, 'id' | 'employee' | 'hours' | 'amounts'>;
export type PayStubDetailUpdateModel = Omit<Partial<PayStubDetailDomainModel>, 'id' | 'dayName' | 'hours.total'>;
export type TimesheetUpdateModel = Omit<Partial<TimesheetDomainModel>, 'id' | 'meta'>;

/**
 * PayStub changes type for draft operations
 * Extends the existing PayStubDraft with missing totalHours property
 */
export interface PayStubChanges {
    stHours?: number;
    otHours?: number;
    dtHours?: number;
    // totalHours is computed automatically from stHours + otHours + dtHours
    bonus?: number;
    expenses?: number;
    employeeId?: string;
    employeeName?: string;
    // Other changes that might be tracked
    [key: string]: unknown;
}

/**
 * Type guard functions for PayStub changes
 */
export function isValidChangeKey(key: string): boolean {
    const validKeys: string[] = ['stHours', 'otHours', 'dtHours', 'bonus', 'expenses', 'employeeId', 'employeeName'];
    return validKeys.includes(key);
}

// =============================================================================
// TYPE GUARDS
// =============================================================================

/**
 * Type guards for runtime validation of domain models
 */
export function isTimesheetDomainModel(obj: unknown): obj is TimesheetDomainModel {
    return obj !== null && typeof obj === 'object' && 'id' in obj && 'payStubs' in obj && 'settings' in obj && 'meta' in obj;
}

export function isPayStubDomainModel(obj: unknown): obj is PayStubDomainModel {
    return obj !== null && typeof obj === 'object' && 'id' in obj && 'employeeId' in obj && 'hours' in obj && 'details' in obj;
}

export function isPayStubDetailDomainModel(obj: unknown): obj is PayStubDetailDomainModel {
    return obj !== null && typeof obj === 'object' && 'id' in obj && 'workDate' in obj && 'hours' in obj && 'payStubId' in obj;
}

// =============================================================================
// MUTATION INPUT TYPES
// =============================================================================

/**
 * Input type for adding new PayStubs
 * Used when creating new PayStubs in a timesheet
 */
export interface AddPayStubInput {
    employeeId: string;          // Relay ID
    name?: string | null;
    details?: AddPayStubDetailInput[];
    // Header totals
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    expanded?: boolean | null;
    inEdit?: boolean | null;
    employeeName?: string | null;
}

/**
 * Input type for adding new PayStub details
 */
export interface AddPayStubDetailInput {
    name?: string | null;
    workDate: string;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    jobCode?: string | null;
    earningsCode?: string | null;
    agreementId?: number | null;
    classificationId?: number | null;
    subClassificationId?: number | null;
    costCenter?: string | null;
    hourlyRate?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    reportLineItemId?: number | null;
}

/**
 * Input type for modifying existing PayStubs
 * ID is required for modification operations
 */
export interface ModifyPayStubInput extends Omit<AddPayStubInput, 'employeeId'> {
    id: string;                  // Relay ID (required)
    employeeId: string;          // Still include for completeness
}

/**
 * Input type for modifying existing PayStub details
 */
export interface ModifyPayStubDetailInput extends AddPayStubDetailInput {
    id: string;                  // Relay ID (required)
    payStubId?: string | null;
}

// =============================================================================
// CONSTANTS
// =============================================================================

/**
 * Constants for domain model field validation and defaults
 */
export const DOMAIN_CONSTANTS = {
    TIMESHEET_STATUS: {
        NEW: 'New',
        SAVED: 'Saved',
        SUBMITTED: 'Submitted'
    } as const,

    HOURS: {
        MIN: 0,
        MAX: 24,
        PRECISION: 2
    } as const,

    AMOUNTS: {
        MIN: 0,
        MAX: 999999.99,
        PRECISION: 2
    } as const,

    DATE_FORMAT: 'YYYY-MM-DD' as const,

    VALIDATION_CODES: {
        REQUIRED_FIELD: 'REQUIRED_FIELD',
        INVALID_FORMAT: 'INVALID_FORMAT',
        OUT_OF_RANGE: 'OUT_OF_RANGE',
        BUSINESS_RULE: 'BUSINESS_RULE'
    } as const
} as const;
