/**
 * Phase 5: Validation & Testing - Runtime Validation
 * 
 * Comprehensive validation functions for GraphQL timesheet types,
 * providing runtime type safety without losing edge case coverage.
 * 
 * This module implements the validation layer described in Phase 5 of the
 * GraphQL Type Safety Implementation Plan.
 */

import type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput,
} from '../types/graphql-timesheet';

import type {
    TimesheetDomainModel,
    PayStubDomainModel,
    PayStubDetailDomainModel,
} from '../types/timesheet-domain';

import { RelayIdService } from '../services/RelayIdService';
import { MAX_ARRAY_SIZE } from '../constants/validationConstants';

// =============================================================================
// RUNTIME VALIDATION FUNCTIONS FOR GRAPHQL TYPES
// =============================================================================

/**
 * Validates that an unknown input conforms to ModifyTimeSheetInput structure
 * Provides comprehensive runtime validation for mutation inputs
 */
export function validateModifyTimeSheetInput(
    input: unknown
): input is ModifyTimeSheetInput {
    if (!input || typeof input !== 'object') return false;

    const candidate = input as Record<string, unknown>;

    // Validate required fields
    if (typeof candidate.id !== 'string' || candidate.id.trim() === '') return false;

    // ID must be either a positive numeric string (legacy) or a valid Relay Global ID
    const idStr = candidate.id.trim();
    if (!RelayIdService.isPositiveNumericString(idStr) && !RelayIdService.isGlobalId(idStr)) {
        return false;
    }

    if (candidate.employerGuid === undefined || candidate.employerGuid === null) return false;

    // Validate optional string fields
    const stringFields = ['name', 'status', 'type'] as const;
    for (const field of stringFields) {
        if (candidate[field] !== undefined && typeof candidate[field] !== 'string') {
            return false;
        }
    }

    // Validate optional boolean fields
    const booleanFields = [
        'readOnly',
        'showDTHoursColumn',
        'showCostCenterColumn',
        'showBonusColumn',
        'showExpensesColumn',
        'showEarningsCodesColumn'
    ] as const;
    for (const field of booleanFields) {
        if (candidate[field] !== undefined && typeof candidate[field] !== 'boolean') {
            return false;
        }
    }

    // Validate optional date field
    if (candidate.modificationDate !== undefined) {
        if (typeof candidate.modificationDate !== 'string') return false;
        // Basic ISO date string validation
        if (!isValidISODate(candidate.modificationDate)) return false;
    }

    // Validate payStubs array if present (legacy pattern)
    if (candidate.payStubs !== undefined) {
        if (!Array.isArray(candidate.payStubs)) return false;
        if (!candidate.payStubs.every(validateModifyPayStubInput)) return false;
    }

    // Validate separate arrays pattern (new implementation)
    if (candidate.addPayStubs !== undefined) {
        if (!Array.isArray(candidate.addPayStubs)) return false;
        if (!candidate.addPayStubs.every(validateModifyPayStubInput)) return false;
    }

    if (candidate.modifyPayStubs !== undefined) {
        if (!Array.isArray(candidate.modifyPayStubs)) return false;
        if (!candidate.modifyPayStubs.every(validateModifyPayStubInput)) return false;
    }

    if (candidate.deletePayStubIds !== undefined) {
        if (!Array.isArray(candidate.deletePayStubIds)) return false;
        if (!candidate.deletePayStubIds.every(id => typeof id === 'string' && id.trim() !== '')) return false;
    }

    return true;
}

/**
 * Validates that an unknown input conforms to ModifyPayStubInput structure
 */
export function validateModifyPayStubInput(
    input: unknown
): input is ModifyPayStubInput {
    if (!input || typeof input !== 'object') return false;

    const candidate = input as Record<string, unknown>;

    // Validate required fields
    if (typeof candidate.employeeId !== 'string') return false;
    
    // Validate employeeId is a valid Global ID
    if (!RelayIdService.isGlobalId(candidate.employeeId)) return false;

    // Validate optional string fields
    const stringFields = ['id', 'employeeName', 'name'] as const;
    for (const field of stringFields) {
        if (candidate[field] !== undefined && typeof candidate[field] !== 'string') {
            return false;
        }
    }

    // Validate optional number fields (hours and amounts)
    const numberFields = ['stHours', 'otHours', 'dtHours', 'totalHours', 'bonus', 'expenses'] as const;
    for (const field of numberFields) {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'number') return false;
            // Hours cannot be negative
            if (['stHours', 'otHours', 'dtHours', 'totalHours'].includes(field) && 
                (candidate[field]) < 0) {
                return false;
            }
        }
    }

    // Validate optional boolean fields
    const booleanFields = ['expanded', 'inEdit', 'delete'] as const;
    for (const field of booleanFields) {
        if (candidate[field] !== undefined && typeof candidate[field] !== 'boolean') {
            return false;
        }
    }

    // Validate optional numeric string fields
    const numericStringFields = ['payStubId'] as const;
    for (const field of numericStringFields) {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'string') return false;
        }
    }

    // Validate details array if present
    if (candidate.details !== undefined) {
        if (!Array.isArray(candidate.details)) return false;
        return candidate.details.every(validateModifyPayStubDetailInput);
    }

    return true;
}

/**
 * Validates that an unknown input conforms to ModifyPayStubDetailInput structure
 */
export function validateModifyPayStubDetailInput(
    input: unknown
): input is ModifyPayStubDetailInput {
    if (!input || typeof input !== 'object') return false;

    const candidate = input as Record<string, unknown>;

    // Validate optional string fields
    const stringFields = [
        'id', 'payStubId', 'reportLineItemId', 'workDate', 'name',
        'jobCode', 'costCenter', 'earningsCode'
    ] as const;
    for (const field of stringFields) {
        if (candidate[field] !== undefined && candidate[field] !== null && 
            typeof candidate[field] !== 'string') {
            return false;
        }
    }

    // Validate workDate format if present
    if (candidate.workDate !== undefined && candidate.workDate !== null) {
        if (typeof candidate.workDate !== 'string') return false;
        if (!isValidWorkDate(candidate.workDate)) return false;
    }

    // Validate optional number fields
    const numberFields = [
        'stHours', 'otHours', 'dtHours', 'totalHours', 
        'agreementId', 'classificationId', 'subClassificationId',
        'hourlyRate', 'bonus', 'expenses'
    ] as const;
    for (const field of numberFields) {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'number') return false;
            // Hours cannot be negative
            if (['stHours', 'otHours', 'dtHours', 'totalHours'].includes(field) && 
                (candidate[field]) < 0) {
                return false;
            }
        }
    }

    // Validate optional boolean fields
    if (candidate.delete !== undefined && typeof candidate.delete !== 'boolean') {
        return false;
    }

    // Validate optional numeric string fields
    const numericStringFields = ['payStubDetailId'] as const;
    for (const field of numericStringFields) {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'string') return false;
        }
    }

    return true;
}

// =============================================================================
// DOMAIN MODEL VALIDATION FUNCTIONS
// =============================================================================

/**
 * Validates that a domain model has all required data for GraphQL conversion
 */
export function validateTimesheetDomainModel(
    model: unknown
): model is TimesheetDomainModel {
    if (!model || typeof model !== 'object') return false;

    const candidate = model as Record<string, unknown>;

    // Validate required fields
    if (typeof candidate.id !== 'string') return false;
    if (candidate.employerGuid === undefined || candidate.employerGuid === null) return false;

    // Validate nested objects
    if (!candidate.settings || typeof candidate.settings !== 'object') return false;
    if (!candidate.meta || typeof candidate.meta !== 'object') return false;

    // Validate payStubs array
    if (!Array.isArray(candidate.payStubs)) return false;
    if (!candidate.payStubs.every(validatePayStubDomainModel)) return false;

    return true;
}

/**
 * Validates PayStub domain model structure
 */
export function validatePayStubDomainModel(
    model: unknown
): model is PayStubDomainModel {
    if (!model || typeof model !== 'object') return false;

    const candidate = model as Record<string, unknown>;

    // Validate required fields
    if (typeof candidate.id !== 'string') return false;
    if (typeof candidate.employeeId !== 'string') return false;

    // Validate nested objects
    if (!candidate.hours || typeof candidate.hours !== 'object') return false;
    if (!candidate.amounts || typeof candidate.amounts !== 'object') return false;
    if (!candidate.employee || typeof candidate.employee !== 'object') return false;
    if (!candidate.ui || typeof candidate.ui !== 'object') return false;

    // Validate details array
    if (!Array.isArray(candidate.details)) return false;
    if (!candidate.details.every(validatePayStubDetailDomainModel)) return false;

    return true;
}

/**
 * Validates PayStubDetail domain model structure
 */
export function validatePayStubDetailDomainModel(
    model: unknown
): model is PayStubDetailDomainModel {
    if (!model || typeof model !== 'object') return false;

    const candidate = model as Record<string, unknown>;

    // Validate required fields
    if (typeof candidate.id !== 'string') return false;
    if (typeof candidate.workDate !== 'string') return false;
    if (typeof candidate.employeeId !== 'string') return false;

    // Validate nested objects
    const requiredObjects = ['hours', 'job', 'agreements', 'amounts', 'earnings', 'ui'];
    for (const obj of requiredObjects) {
        if (!candidate[obj] || typeof candidate[obj] !== 'object') return false;
    }

    return true;
}

// =============================================================================
// COMPREHENSIVE VALIDATION WITH ERROR DETAILS
// =============================================================================

/**
 * Comprehensive validation result type
 */
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationWarning[];
}

export interface ValidationError {
    field: string;
    message: string;
    value?: unknown;
    code: string;
}

export interface ValidationWarning {
    field: string;
    message: string;
    value?: unknown;
    code: string;
}

/**
 * Performs comprehensive validation of GraphQL timesheet input with detailed error reporting
 */
export function validateTimesheetInputComprehensive(
    input: unknown
): ValidationResult {
    const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
    };

    if (!input || typeof input !== 'object') {
        result.isValid = false;
        result.errors.push({
            field: 'root',
            message: 'Input must be a non-null object',
            value: input,
            code: 'INVALID_TYPE'
        });
        return result;
    }

    const candidate = input as Record<string, unknown>;

    // Validate ID field
    if (typeof candidate.id !== 'string' || candidate.id.trim() === '') {
        result.isValid = false;
        result.errors.push({
            field: 'id',
            message: 'ID must be a non-empty string',
            value: candidate.id,
            code: 'INVALID_ID_TYPE'
        });
    }

    // Validate employerGuid
    if (candidate.employerGuid === undefined || candidate.employerGuid === null) {
        result.isValid = false;
        result.errors.push({
            field: 'employerGuid',
            message: 'EmployerGuid is required',
            value: candidate.employerGuid,
            code: 'MISSING_EMPLOYER_GUID'
        });
    }

    // Validate name field
    if (candidate.name !== undefined && typeof candidate.name !== 'string') {
        result.isValid = false;
        result.errors.push({
            field: 'name',
            message: 'Name must be a string',
            value: candidate.name,
            code: 'INVALID_NAME_TYPE'
        });
    } else if (candidate.name === '') {
        result.warnings.push({
            field: 'name',
            message: 'Name is empty',
            value: candidate.name,
            code: 'EMPTY_NAME'
        });
    }

    // Validate payStubs array (legacy pattern)
    if (candidate.payStubs !== undefined) {
        if (!Array.isArray(candidate.payStubs)) {
            result.isValid = false;
            result.errors.push({
                field: 'payStubs',
                message: 'PayStubs must be an array',
                value: candidate.payStubs,
                code: 'INVALID_PAYSTUBS_TYPE'
            });
        } else {
            candidate.payStubs.forEach((payStub, index) => {
                const payStubResult = validatePayStubInputComprehensive(payStub);
                if (!payStubResult.isValid) {
                    result.isValid = false;
                    payStubResult.errors.forEach(error => {
                        result.errors.push({
                            ...error,
                            field: `payStubs[${index}].${error.field}`
                        });
                    });
                }
                payStubResult.warnings.forEach(warning => {
                    result.warnings.push({
                        ...warning,
                        field: `payStubs[${index}].${warning.field}`
                    });
                });
            });
        }
    }

    // NEW: Validate separate arrays pattern (addPayStubs, modifyPayStubs, deletePayStubIds)

    // Validate addPayStubs array
    if (candidate.addPayStubs !== undefined) {
        if (!Array.isArray(candidate.addPayStubs)) {
            result.isValid = false;
            result.errors.push({
                field: 'addPayStubs',
                message: 'addPayStubs must be an array',
                value: candidate.addPayStubs,
                code: 'INVALID_ARRAY_TYPE'
            });
        } else {
            // Array size validation
            if (candidate.addPayStubs.length > MAX_ARRAY_SIZE) {
                result.isValid = false;
                result.errors.push({
                    field: 'addPayStubs',
                    message: `Cannot add more than ${MAX_ARRAY_SIZE} PayStubs in a single operation`,
                    value: candidate.addPayStubs.length,
                    code: 'ARRAY_SIZE_EXCEEDED'
                });
            }
            
            // Validate each item
            candidate.addPayStubs.forEach((payStub, index) => {
                const payStubResult = validatePayStubInputComprehensive(payStub);
                if (!payStubResult.isValid) {
                    result.isValid = false;
                    payStubResult.errors.forEach(error => {
                        result.errors.push({
                            ...error,
                            field: `addPayStubs[${index}].${error.field}`
                        });
                    });
                }
                payStubResult.warnings.forEach(warning => {
                    result.warnings.push({
                        ...warning,
                        field: `addPayStubs[${index}].${warning.field}`
                    });
                });
            });
        }
    }

    // Validate modifyPayStubs array
    if (candidate.modifyPayStubs !== undefined) {
        if (!Array.isArray(candidate.modifyPayStubs)) {
            result.isValid = false;
            result.errors.push({
                field: 'modifyPayStubs',
                message: 'modifyPayStubs must be an array',
                value: candidate.modifyPayStubs,
                code: 'INVALID_ARRAY_TYPE'
            });
        } else {
            // Array size validation
            if (candidate.modifyPayStubs.length > MAX_ARRAY_SIZE) {
                result.isValid = false;
                result.errors.push({
                    field: 'modifyPayStubs',
                    message: `Cannot modify more than ${MAX_ARRAY_SIZE} PayStubs in a single operation`,
                    value: candidate.modifyPayStubs.length,
                    code: 'ARRAY_SIZE_EXCEEDED'
                });
            }
            
            // Validate each item
            candidate.modifyPayStubs.forEach((payStub, index) => {
                const payStubResult = validatePayStubInputComprehensive(payStub);
                if (!payStubResult.isValid) {
                    result.isValid = false;
                    payStubResult.errors.forEach(error => {
                        result.errors.push({
                            ...error,
                            field: `modifyPayStubs[${index}].${error.field}`
                        });
                    });
                }
                payStubResult.warnings.forEach(warning => {
                    result.warnings.push({
                        ...warning,
                        field: `modifyPayStubs[${index}].${warning.field}`
                    });
                });
            });
        }
    }

    // Validate deletePayStubIds array
    if (candidate.deletePayStubIds !== undefined) {
        if (!Array.isArray(candidate.deletePayStubIds)) {
            result.isValid = false;
            result.errors.push({
                field: 'deletePayStubIds',
                message: 'deletePayStubIds must be an array',
                value: candidate.deletePayStubIds,
                code: 'INVALID_ARRAY_TYPE'
            });
        } else {
            // Array size validation
            if (candidate.deletePayStubIds.length > MAX_ARRAY_SIZE) {
                result.isValid = false;
                result.errors.push({
                    field: 'deletePayStubIds',
                    message: `Cannot delete more than ${MAX_ARRAY_SIZE} PayStubs in a single operation`,
                    value: candidate.deletePayStubIds.length,
                    code: 'ARRAY_SIZE_EXCEEDED'
                });
            }
            
            // Validate each ID is a string
            candidate.deletePayStubIds.forEach((id, index) => {
                if (typeof id !== 'string' || id.trim() === '') {
                    result.isValid = false;
                    result.errors.push({
                        field: `deletePayStubIds[${index}]`,
                        message: 'deletePayStubIds must contain non-empty strings',
                        value: id,
                        code: 'INVALID_DELETE_ID'
                    });
                }
            });
        }
    }

    return result;
}

/**
 * Comprehensive validation for PayStub input
 */
export function validatePayStubInputComprehensive(
    input: unknown
): ValidationResult {
    const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
    };

    if (!input || typeof input !== 'object') {
        result.isValid = false;
        result.errors.push({
            field: 'root',
            message: 'PayStub input must be a non-null object',
            value: input,
            code: 'INVALID_TYPE'
        });
        return result;
    }

    const candidate = input as Record<string, unknown>;

    // Validate employeeId  
    if (typeof candidate.employeeId !== 'string') {
        result.isValid = false;
        result.errors.push({
            field: 'employeeId',
            message: 'EmployeeId must be a Global ID string',
            value: candidate.employeeId,
            code: 'INVALID_EMPLOYEE_ID_TYPE'
        });
    } else if (!RelayIdService.isGlobalId(candidate.employeeId)) {
        result.isValid = false;
        result.errors.push({
            field: 'employeeId',
            message: 'EmployeeId must be a valid Global ID',
            value: candidate.employeeId,
            code: 'INVALID_EMPLOYEE_ID_VALUE'
        });
    }

    // Validate hours fields
    const hoursFields = ['stHours', 'otHours', 'dtHours', 'totalHours'] as const;
    hoursFields.forEach(field => {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'number') {
                result.isValid = false;
                result.errors.push({
                    field,
                    message: `${field} must be a number`,
                    value: candidate[field],
                    code: 'INVALID_HOURS_TYPE'
                });
            } else if ((candidate[field]) < 0) {
                result.isValid = false;
                result.errors.push({
                    field,
                    message: `${field} cannot be negative`,
                    value: candidate[field],
                    code: 'NEGATIVE_HOURS'
                });
            } else if ((candidate[field]) > 24) {
                result.warnings.push({
                    field,
                    message: `${field} exceeds 24 hours, please verify`,
                    value: candidate[field],
                    code: 'EXCESSIVE_HOURS'
                });
            }
        }
    });

    // Validate details array
    if (candidate.details !== undefined) {
        if (!Array.isArray(candidate.details)) {
            result.isValid = false;
            result.errors.push({
                field: 'details',
                message: 'Details must be an array',
                value: candidate.details,
                code: 'INVALID_DETAILS_TYPE'
            });
        } else {
            candidate.details.forEach((detail, index) => {
                const detailResult = validatePayStubDetailInputComprehensive(detail);
                if (!detailResult.isValid) {
                    result.isValid = false;
                    detailResult.errors.forEach(error => {
                        result.errors.push({
                            ...error,
                            field: `details[${index}].${error.field}`
                        });
                    });
                }
                detailResult.warnings.forEach(warning => {
                    result.warnings.push({
                        ...warning,
                        field: `details[${index}].${warning.field}`
                    });
                });
            });
        }
    }

    return result;
}

/**
 * Comprehensive validation for PayStubDetail input
 */
export function validatePayStubDetailInputComprehensive(
    input: unknown
): ValidationResult {
    const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
    };

    if (!input || typeof input !== 'object') {
        result.isValid = false;
        result.errors.push({
            field: 'root',
            message: 'PayStubDetail input must be a non-null object',
            value: input,
            code: 'INVALID_TYPE'
        });
        return result;
    }

    const candidate = input as Record<string, unknown>;

    // Validate workDate if present
    if (candidate.workDate !== undefined && candidate.workDate !== null) {
        if (typeof candidate.workDate !== 'string') {
            result.isValid = false;
            result.errors.push({
                field: 'workDate',
                message: 'WorkDate must be a string',
                value: candidate.workDate,
                code: 'INVALID_WORKDATE_TYPE'
            });
        } else if (!isValidWorkDate(candidate.workDate)) {
            result.isValid = false;
            result.errors.push({
                field: 'workDate',
                message: 'WorkDate must be in YYYY-MM-DD format',
                value: candidate.workDate,
                code: 'INVALID_WORKDATE_FORMAT'
            });
        } else {
            const date = new Date(candidate.workDate);
            const now = new Date();
            if (date > now) {
                result.warnings.push({
                    field: 'workDate',
                    message: 'WorkDate is in the future',
                    value: candidate.workDate,
                    code: 'FUTURE_WORKDATE'
                });
            }
        }
    }

    // Validate hours fields
    const hoursFields = ['stHours', 'otHours', 'dtHours', 'totalHours'] as const;
    hoursFields.forEach(field => {
        if (candidate[field] !== undefined && candidate[field] !== null) {
            if (typeof candidate[field] !== 'number') {
                result.isValid = false;
                result.errors.push({
                    field,
                    message: `${field} must be a number`,
                    value: candidate[field],
                    code: 'INVALID_HOURS_TYPE'
                });
            } else if ((candidate[field]) < 0) {
                result.isValid = false;
                result.errors.push({
                    field,
                    message: `${field} cannot be negative`,
                    value: candidate[field],
                    code: 'NEGATIVE_HOURS'
                });
            } else if ((candidate[field]) > 24) {
                result.warnings.push({
                    field,
                    message: `${field} exceeds 24 hours for a single day, please verify`,
                    value: candidate[field],
                    code: 'EXCESSIVE_DAILY_HOURS'
                });
            }
        }
    });

    return result;
}

// =============================================================================
// UTILITY VALIDATION FUNCTIONS  
// =============================================================================

/**
 * Validates ISO date string format
 */
function isValidISODate(dateString: string): boolean {
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    return isoDateRegex.test(dateString) && !isNaN(Date.parse(dateString));
}

/**
 * Validates work date format (YYYY-MM-DD)
 */
function isValidWorkDate(dateString: string): boolean {
    const workDateRegex = /^\d{4}-\d{2}-\d{2}$/;
    return workDateRegex.test(dateString) && !isNaN(Date.parse(dateString));
}

/**
 * Validation utility for safe GraphQL input conversion
 */
export function safeValidateForGraphQL<T>(
    input: unknown,
    validator: (input: unknown) => input is T,
    errorMessage: string
): { success: true; data: T } | { success: false; error: string } {
    try {
        if (validator(input)) {
            return { success: true, data: input };
        } else {
            return { success: false, error: errorMessage };
        }
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Validation error'
        };
    }
}

/**
 * Business logic validation for timesheet data
 */
export function validateTimesheetBusinessRules(
    input: ModifyTimeSheetInput
): ValidationResult {
    const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
    };

    // Check for duplicate employee IDs across all PayStub arrays
    const allPayStubs = [
        ...(input.addPayStubs ?? []),
        ...(input.modifyPayStubs ?? [])
    ];
    
    if (allPayStubs.length > 0) {
        const employeeIds = allPayStubs
            .map((payStub: { employeeId?: string }) => payStub.employeeId)
            .filter((id: string | undefined) => id !== undefined);
        
        const duplicateIds = employeeIds.filter((id: string | undefined, index: number) => 
            id !== undefined && employeeIds.indexOf(id) !== index
        );
        
        if (duplicateIds.length > 0) {
            result.warnings.push({
                field: 'payStubs',
                message: `Duplicate employee IDs found: ${duplicateIds.join(', ')}`,
                value: duplicateIds,
                code: 'DUPLICATE_EMPLOYEES'
            });
        }
    }

    return result;
}