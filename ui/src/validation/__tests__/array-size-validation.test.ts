/**
 * Array Size Validation Tests
 * 
 * Tests for the 200-item array size limits implemented in the
 * validateTimesheetInputComprehensive function for the separate
 * arrays pattern (addPayStubs, modifyPayStubs, deletePayStubIds).
 */

import { validateTimesheetInputComprehensive } from '../timesheet-validation';
import { RelayIdService } from '../../services/RelayIdService';

// =============================================================================
// TEST DATA FACTORIES
// =============================================================================

function createMockAddPayStubInput() {
    return {
        employeeId: RelayIdService.toGlobalId('Employee', 123),
        name: 'Test PayStub',
        employeeName: '<PERSON>',
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        bonus: 0,
        expenses: 0,
        details: []
    };
}

function createMockModifyPayStubInput() {
    return {
        id: RelayIdService.toGlobalId('PayStub', 456),
        employeeId: RelayIdService.toGlobalId('Employee', 123),
        name: 'Test PayStub',
        employeeName: 'John Doe',
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        bonus: 0,
        expenses: 0,
        details: []
    };
}

function createValidTimesheetInput() {
    return {
        id: '1',
        employerGuid: 'test-guid-123'
    };
}

// =============================================================================
// ARRAY SIZE VALIDATION TESTS
// =============================================================================

describe('Array Size Validation Tests', () => {
    describe('addPayStubs Array Size Limits', () => {
        it('should accept arrays within size limits (200 items)', () => {
            const validArray = Array(200).fill(createMockAddPayStubInput());
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: validArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
            expect(result.errors.some(e => e.code === 'ARRAY_SIZE_EXCEEDED')).toBe(false);
        });

        it('should reject oversized addPayStubs array (201 items)', () => {
            const oversizedArray = Array(201).fill(createMockAddPayStubInput());
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: oversizedArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const sizeError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'addPayStubs');
            expect(sizeError).toBeDefined();
            expect(sizeError?.message).toContain('Cannot add more than 200 PayStubs');
            expect(sizeError?.value).toBe(201);
        });

        it('should accept empty addPayStubs array', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: []
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });

        it('should accept undefined addPayStubs', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: undefined
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });

        it('should reject non-array addPayStubs', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: 'not-an-array'
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const typeError = result.errors.find(e => e.code === 'INVALID_ARRAY_TYPE' && e.field === 'addPayStubs');
            expect(typeError).toBeDefined();
        });
    });

    describe('modifyPayStubs Array Size Limits', () => {
        it('should accept arrays within size limits (200 items)', () => {
            const validArray = Array(200).fill(createMockModifyPayStubInput());
            const input = {
                ...createValidTimesheetInput(),
                modifyPayStubs: validArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
            expect(result.errors.some(e => e.code === 'ARRAY_SIZE_EXCEEDED')).toBe(false);
        });

        it('should reject oversized modifyPayStubs array (201 items)', () => {
            const oversizedArray = Array(201).fill(createMockModifyPayStubInput());
            const input = {
                ...createValidTimesheetInput(),
                modifyPayStubs: oversizedArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const sizeError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'modifyPayStubs');
            expect(sizeError).toBeDefined();
            expect(sizeError?.message).toContain('Cannot modify more than 200 PayStubs');
            expect(sizeError?.value).toBe(201);
        });

        it('should accept empty modifyPayStubs array', () => {
            const input = {
                ...createValidTimesheetInput(),
                modifyPayStubs: []
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });

        it('should reject non-array modifyPayStubs', () => {
            const input = {
                ...createValidTimesheetInput(),
                modifyPayStubs: { not: 'an-array' }
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const typeError = result.errors.find(e => e.code === 'INVALID_ARRAY_TYPE' && e.field === 'modifyPayStubs');
            expect(typeError).toBeDefined();
        });
    });

    describe('deletePayStubIds Array Size Limits', () => {
        it('should accept arrays within size limits (200 items)', () => {
            const validArray = Array(200).fill(RelayIdService.toGlobalId('PayStub', 123));
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: validArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
            expect(result.errors.some(e => e.code === 'ARRAY_SIZE_EXCEEDED')).toBe(false);
        });

        it('should reject oversized deletePayStubIds array (201 items)', () => {
            const oversizedArray = Array(201).fill(RelayIdService.toGlobalId('PayStub', 123));
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: oversizedArray
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const sizeError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'deletePayStubIds');
            expect(sizeError).toBeDefined();
            expect(sizeError?.message).toContain('Cannot delete more than 200 PayStubs');
            expect(sizeError?.value).toBe(201);
        });

        it('should accept empty deletePayStubIds array', () => {
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: []
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });

        it('should reject non-array deletePayStubIds', () => {
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: 'single-id'
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const typeError = result.errors.find(e => e.code === 'INVALID_ARRAY_TYPE' && e.field === 'deletePayStubIds');
            expect(typeError).toBeDefined();
        });

        it('should reject arrays with empty string IDs', () => {
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: ['valid-id', '', 'another-valid-id']
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const invalidIdError = result.errors.find(e => e.code === 'INVALID_DELETE_ID');
            expect(invalidIdError).toBeDefined();
            expect(invalidIdError?.field).toBe('deletePayStubIds[1]');
        });

        it('should reject arrays with non-string IDs', () => {
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: ['valid-id', 123, 'another-valid-id']
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            const invalidIdError = result.errors.find(e => e.code === 'INVALID_DELETE_ID');
            expect(invalidIdError).toBeDefined();
            expect(invalidIdError?.field).toBe('deletePayStubIds[1]');
        });
    });
});

// =============================================================================
// MULTIPLE ARRAYS VALIDATION TESTS
// =============================================================================

describe('Multiple Arrays Validation Tests', () => {
    it('should validate all arrays simultaneously', () => {
        const input = {
            ...createValidTimesheetInput(),
            addPayStubs: Array(150).fill(createMockAddPayStubInput()),
            modifyPayStubs: Array(150).fill(createMockModifyPayStubInput()),
            deletePayStubIds: Array(150).fill(RelayIdService.toGlobalId('PayStub', 123))
        };
        
        const result = validateTimesheetInputComprehensive(input);
        
        expect(result.isValid).toBe(true);
        expect(result.errors.some(e => e.code === 'ARRAY_SIZE_EXCEEDED')).toBe(false);
    });

    it('should report multiple array size violations', () => {
        const input = {
            ...createValidTimesheetInput(),
            addPayStubs: Array(201).fill(createMockAddPayStubInput()),
            modifyPayStubs: Array(202).fill(createMockModifyPayStubInput()),
            deletePayStubIds: Array(203).fill(RelayIdService.toGlobalId('PayStub', 123))
        };
        
        const result = validateTimesheetInputComprehensive(input);
        
        expect(result.isValid).toBe(false);
        
        const addError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'addPayStubs');
        const modifyError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'modifyPayStubs');
        const deleteError = result.errors.find(e => e.code === 'ARRAY_SIZE_EXCEEDED' && e.field === 'deletePayStubIds');
        
        expect(addError).toBeDefined();
        expect(modifyError).toBeDefined();
        expect(deleteError).toBeDefined();
        
        expect(addError?.value).toBe(201);
        expect(modifyError?.value).toBe(202);
        expect(deleteError?.value).toBe(203);
    });

    it('should accept mix of defined and undefined arrays', () => {
        const input = {
            ...createValidTimesheetInput(),
            addPayStubs: Array(100).fill(createMockAddPayStubInput()),
            modifyPayStubs: undefined,
            deletePayStubIds: Array(50).fill(RelayIdService.toGlobalId('PayStub', 123))
        };
        
        const result = validateTimesheetInputComprehensive(input);
        
        expect(result.isValid).toBe(true);
    });
});

// =============================================================================
// EDGE CASE TESTS
// =============================================================================

describe('Edge Case Tests', () => {
    describe('Boundary Values', () => {
        it('should accept exactly 200 items in each array', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: Array(200).fill(createMockAddPayStubInput()),
                modifyPayStubs: Array(200).fill(createMockModifyPayStubInput()),
                deletePayStubIds: Array(200).fill(RelayIdService.toGlobalId('PayStub', 123))
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });

        it('should reject exactly 201 items in each array', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: Array(201).fill(createMockAddPayStubInput()),
                modifyPayStubs: Array(201).fill(createMockModifyPayStubInput()),
                deletePayStubIds: Array(201).fill(RelayIdService.toGlobalId('PayStub', 123))
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            expect(result.errors.filter(e => e.code === 'ARRAY_SIZE_EXCEEDED')).toHaveLength(3);
        });

        it('should handle single item arrays', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: [createMockAddPayStubInput()],
                modifyPayStubs: [createMockModifyPayStubInput()],
                deletePayStubIds: [RelayIdService.toGlobalId('PayStub', 123)]
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(true);
        });
    });

    describe('Invalid Data Types', () => {
        it('should handle null arrays', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: null,
                modifyPayStubs: null,
                deletePayStubIds: null
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            // null arrays are treated as invalid (not arrays)
            expect(result.isValid).toBe(false);
            expect(result.errors.some(e => e.code === 'INVALID_ARRAY_TYPE')).toBe(true);
        });

        it('should reject string instead of array', () => {
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: 'not-an-array'
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            expect(result.errors.some(e => e.code === 'INVALID_ARRAY_TYPE')).toBe(true);
        });

        it('should reject number instead of array', () => {
            const input = {
                ...createValidTimesheetInput(),
                modifyPayStubs: 123
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            expect(result.errors.some(e => e.code === 'INVALID_ARRAY_TYPE')).toBe(true);
        });

        it('should reject object instead of array', () => {
            const input = {
                ...createValidTimesheetInput(),
                deletePayStubIds: { id: 'test' }
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            expect(result.isValid).toBe(false);
            expect(result.errors.some(e => e.code === 'INVALID_ARRAY_TYPE')).toBe(true);
        });
    });

    describe('Performance Considerations', () => {
        it('should handle maximum size arrays efficiently', () => {
            const startTime = Date.now();
            
            const input = {
                ...createValidTimesheetInput(),
                addPayStubs: Array(200).fill(createMockAddPayStubInput()),
                modifyPayStubs: Array(200).fill(createMockModifyPayStubInput()),
                deletePayStubIds: Array(200).fill(RelayIdService.toGlobalId('PayStub', 123))
            };
            
            const result = validateTimesheetInputComprehensive(input);
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            expect(result.isValid).toBe(true);
            // Validation should complete within reasonable time (adjust threshold as needed)
            expect(duration).toBeLessThan(1000); // 1 second
        });
    });
});