/**
 * PayStub Mappers - Separate Arrays Implementation
 *
 * This file provides mappers for building AddPayStubInput and ModifyPayStubInput
 * objects from draft PayStub data, supporting the new separate arrays pattern
 * for the ModifyTimeSheet mutation.
 */

import { RelayIdService } from '../services/RelayIdService';
import type { AddPayStubInput, ModifyPayStubInput, AddPayStubDetailInput, ModifyPayStubDetailInput } from '../types/graphql-timesheet';
import type { ModifiablePayStubDetail } from '../types/timesheet-detail';

/**
 * Type guard to check if a detail should be kept (not marked for deletion).
 * Returns true if the detail should be included in the mutation.
 */
function isKeptDetail<T extends ModifiablePayStubDetail>(detail: T): detail is T & { delete?: never; _uiDelete?: never } {
    const deletable = detail as Partial<{ delete?: boolean; _uiDelete?: boolean }>;
    return !deletable.delete && !deletable._uiDelete;
}

// Type alias for draft PayStub - represents PayStub data that may be new or existing
export interface DraftPayStub {
    id?: string;
    employeeId: string;
    name?: string | null;
    employeeName?: string | null;
    details?: ReadonlyArray<ModifiablePayStubDetail>;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    expanded?: boolean | null;
    inEdit?: boolean | null;
    isNew?: boolean;
    markedForDelete?: boolean;
    delete?: boolean;
}

/**
 * Builds an AddPayStubInput from a draft PayStub
 * Used for new PayStubs that don't have an ID yet
 */
export function buildAddPayStubInput(stub: DraftPayStub): AddPayStubInput {
    return {
        employeeId: RelayIdService.isGlobalId(stub.employeeId)
            ? stub.employeeId
            : RelayIdService.toGlobalId('Employee', parseInt(stub.employeeId)),
        name: stub.name ?? null,
        employeeName: stub.employeeName ?? null,
        details: stub.details ? stub.details.filter(isKeptDetail).map(buildAddPayStubDetailInput) : [],
        stHours: stub.stHours ?? null,
        otHours: stub.otHours ?? null,
        dtHours: stub.dtHours ?? null,
        bonus: stub.bonus ?? null,
        expenses: stub.expenses ?? null,
        expanded: stub.expanded ?? null,
        inEdit: stub.inEdit ?? null
    };
}

/**
 * Builds a ModifyPayStubInput from a draft PayStub
 * Used for existing PayStubs that have an ID
 */
export function buildModifyPayStubInput(stub: DraftPayStub): ModifyPayStubInput {
    if (!stub.id) {
        throw new Error('Cannot build ModifyPayStubInput without an ID');
    }

    // Validate and convert PayStub ID to proper Relay Global ID format
    let payStubId: string;
    if (RelayIdService.isGlobalId(stub.id)) {
        // Already a valid Relay Global ID
        payStubId = stub.id;
    } else {
        // Convert raw ID (GUID or numeric) to Relay Global ID
        payStubId = RelayIdService.toGlobalId('PayStub', stub.id);
    }

    return {
        id: payStubId,
        employeeId: RelayIdService.isGlobalId(stub.employeeId)
            ? stub.employeeId
            : RelayIdService.toGlobalId('Employee', parseInt(stub.employeeId)),
        name: stub.name ?? null,
        employeeName: stub.employeeName ?? null,
        details: stub.details ? stub.details.map(buildModifyPayStubDetailInput).filter((d): d is ModifyPayStubDetailInput => d !== null) : [],
        stHours: stub.stHours ?? null,
        otHours: stub.otHours ?? null,
        dtHours: stub.dtHours ?? null,
        bonus: stub.bonus ?? null,
        expenses: stub.expenses ?? null,
        expanded: stub.expanded ?? null,
        inEdit: stub.inEdit ?? null
    };
}

/**
 * Builds an AddPayStubDetailInput from a detail
 * Used for new details in either new or existing PayStubs
 */
function buildAddPayStubDetailInput(detail: ModifiablePayStubDetail & { workDate: string }): AddPayStubDetailInput {
    // Runtime safeguard – workDate is required by GraphQL schema
    if (!detail.workDate || typeof detail.workDate !== 'string') {
        throw new Error('Attempted to build PayStubDetailInput without a valid workDate');
    }

    return {
        name: detail.name ?? null,
        workDate: detail.workDate,
        stHours: parseNumber(detail.stHours),
        otHours: parseNumber(detail.otHours),
        dtHours: parseNumber(detail.dtHours),
        jobCode: detail.jobCode ?? null,
        earningsCode: detail.earningsCode ?? null,
        agreementId: parseIntId(detail.agreementId),
        classificationId: parseIntId(detail.classificationId),
        subClassificationId: parseIntId(detail.subClassificationId),
        costCenter: detail.costCenter ?? null,
        hourlyRate: parseNumber(detail.hourlyRate),
        bonus: parseNumber(detail.bonus),
        expenses: parseNumber(detail.expenses),
        reportLineItemId: parseIntId(detail.reportLineItemId)
    };
}

/**
 * Builds a ModifyPayStubDetailInput from a detail
 * Used for existing details that have an ID
 * Returns null if the detail cannot be mapped (e.g., deletion without ID)
 */
export function buildModifyPayStubDetailInput(detail: ModifiablePayStubDetail): ModifyPayStubDetailInput | null {
    // --- Deletion passthrough ---------------------------------------------
    // If upstream logic has already indicated that this detail should be
    // removed (detail.delete === true) we must forward that flag untouched.
    // Doing any further mapping would turn the payload back into a full
    // modify request and the backend would resurrect the row.  Instead we
    // emit the minimal structure expected by the GraphQL schema.
    const markedForDeletion = (detail as Partial<{ delete?: boolean }>).delete === true;

    if (markedForDeletion) {
        // Check if this is a client-side/temporary ID that doesn't represent a saved entity
        if (detail.id?.startsWith('temp-') || detail.id?.startsWith('client-')) {
            // Unsaved details marked for deletion should be filtered out entirely
            return null;
        }
        
        // Ensure the ID is a valid Relay Global ID – convert numeric/raw ids
        // if necessary so the backend recognises the row.
        let idToSend: string;
        if (detail.id && RelayIdService.isGlobalId(detail.id)) {
            idToSend = detail.id;
        } else if (detail.id) {
            idToSend = RelayIdService.toGlobalId('PayStubDetail', detail.id);
        } else {
            // If no ID is present, return null - caller will filter out nulls
            // (unsaved rows marked for deletion are already filtered out)
            return null;
        }

        return { id: idToSend, delete: true };
    }

    const baseInput = buildAddPayStubDetailInput(detail);

    // For modify operations, only include ID if it's a valid Relay ID
    let detailId: string | undefined;
    if (detail.id) {
        if (RelayIdService.isGlobalId(detail.id)) {
            const parsed = RelayIdService.fromGlobalId(detail.id);
            if (/^\d+$/.test(parsed.id)) {
                detailId = detail.id;
            }
        } else if (/^\d+$/.test(String(detail.id))) {
            detailId = RelayIdService.toGlobalId('PayStubDetail', detail.id);
        }
    }

    if (!detailId) {
        // If no valid ID, treat as new detail (return AddPayStubDetailInput)
        return baseInput as ModifyPayStubDetailInput;
    }

    return {
        ...baseInput,
        id: detailId,
        payStubId: detail.payStubId ?? null
    };
}

/**
 * Helper function to safely parse numeric values
 */
function parseNumber(val: unknown): number | null {
    if (val === null || val === undefined || val === '') return null;
    const numVal = parseFloat(String(val));
    if (isNaN(numVal) || !isFinite(numVal)) return null;
    if (Math.abs(numVal) > Number.MAX_SAFE_INTEGER) return null;
    return numVal;
}

/**
 * Helper function to safely parse integer ID values
 */
function parseIntId(val: unknown): number | null {
    if (val === null || val === undefined || val === '') return null;
    const intVal = parseInt(String(val), 10);
    if (isNaN(intVal) || !Number.isInteger(intVal)) return null;
    if (Math.abs(intVal) > Number.MAX_SAFE_INTEGER) return null;
    return intVal;
}

/**
 * Determines if a PayStub should be treated as new based on its ID
 * @param stub - The PayStub to check
 * @param isNewTimesheet - Whether the parent timesheet is new
 * @returns true if the PayStub should be added, false if it should be modified
 */
export function isNewPayStub(stub: DraftPayStub, isNewTimesheet: boolean): boolean {
    if (isNewTimesheet) {
        return true; // All PayStubs in a new timesheet are new
    }

    if (stub.isNew !== undefined) {
        return stub.isNew; // Use explicit flag if available
    }

    if (!stub.id || stub.id.startsWith('temp-') || stub.id.startsWith('client:')) {
        return true;
    }

    return false; // any other Relay Global-ID (numeric or GUID) → existing
}
