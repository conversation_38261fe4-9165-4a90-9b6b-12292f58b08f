/**
 * HelpScout Beacon configuration constants
 * These GUIDs map to specific beacon configurations in the HelpScout dashboard
 */
export const HELPSCOUT_BEACONS = {
    /**
     * Beacon ID for the Employer Roster page
     * Shows help content specific to managing employer information and rosters
     */
    EMPLOYER_ROSTER: '150f892a-c41e-474f-8f32-bb27bc15a1ad',
    
    /**
     * Beacon ID for the Timesheet Roster page
     * Shows help content specific to managing multiple timesheets
     */
    TIMESHEET_ROSTER: '1b34a0a2-ebac-4fe9-90b3-ce994bb75bab',
    
    /**
     * Beacon ID for the Timesheet Detail page
     * Shows help content specific to individual timesheet details and editing
     */
    TIMESHEET_DETAIL: 'dec1b03f-d455-4eaf-8e9c-c1085c047de3',
    
    /**
     * Beacon ID for the Delinquency by Payment page
     * Shows help content specific to delinquency reports and payment tracking
     */
    DELINQUENCY_BY_PAYMENT: 'fb1226ff-aa5b-4f0f-9fe7-14d3582e2d7e'
} as const;

// Type for HelpScout beacon IDs to ensure type safety
export type HelpScoutBeaconId = typeof HELPSCOUT_BEACONS[keyof typeof HELPSCOUT_BEACONS];