/**
 * Date Formatting Constants
 * 
 * These constants ensure consistent date formatting between client and server,
 * regardless of browser culture or timezone differences.
 * 
 * ## Timezone Handling Strategy:
 * 
 * **DateTime Fields (modificationDate, creationDate, etc.):**
 * - Use UTC timezone via .toISOString() method
 * - Ensures consistent server-client communication
 * - Backend handles timezone conversion with DateTimeOffset types
 * - Prevents browser culture/timezone from affecting data integrity
 * 
 * **LocalDate Fields (payPeriodEndDate, workDate, etc.):**
 * - Use YYYY-MM-DD format without time component
 * - No timezone consideration needed for date-only fields
 * - Matches GraphQL LocalDate scalar expectations
 * 
 * ## Server Globalization Alignment:
 * - Formats match backend/Utils/FieldDefinitions.cs patterns
 * - Prevents client browser culture differences from causing issues
 * - Ensures same formatted values between server and client (UI)
 * 
 * @see backend/Utils/FieldDefinitions.cs for server-side date formats
 * @see backend/Data/Models/TimeSheet.cs for DateTimeOffset usage
 */

/**
 * Standard format for LocalDate scalar type (YYYY-MM-DD)
 * Used for: payPeriodEndDate, workDate, and other date-only fields
 * Matches server expectation for LocalDate GraphQL scalar
 */
export const LOCAL_DATE_FORMAT = 'yyyy-MM-dd' as const;

/**
 * Display format for dates in user interfaces (MM/dd/yyyy)
 * Matches server-side display format from FieldDefinitions.cs
 */
export const DISPLAY_DATE_FORMAT = 'MM/dd/yyyy' as const;

/**
 * Date range constants for filters
 */
export const DATE_RANGES = {
    ALL_TIME_START: '1970-01-01',
    MIN_YEAR: 1900,
    MAX_YEAR: 2100
} as const;

/**
 * Type definitions for better type safety
 */
export type LocalDateString = `${number}-${number}-${number}`; // YYYY-MM-DD
export type DateFormatType = 'LocalDate' | 'DateTime';

/**
 * Helper function to format dates consistently
 * 
 * @param date - The Date object to format
 * @returns Formatted date string matching server expectations
 */
export const formatDateForServer = (date: Date): string => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        throw new Error('formatDateForServer requires a valid Date object');
    }
    return date.toISOString();
}; 