interface SessionConfig {
    sessionTimeoutMinutes: number;
    warningBeforeExpiryMinutes: number;
    keepAliveUrlPattern: RegExp;
}

type OpenAuthDialogFunction = (message: string, redirectUrl: string) => void;

export class SessionManagementService {
    private static instance: SessionManagementService;
    private config: SessionConfig;
    private sessionTimer: NodeJS.Timeout | null = null;
    private lastActivityTime: Date = new Date();
    private isRunning: boolean = false;
    private isWarningShown: boolean = false;
    private openAuthDialogCallback: OpenAuthDialogFunction | null = null;

    private constructor() {
        this.config = {
            sessionTimeoutMinutes: 120, // 2 hours
            warningBeforeExpiryMinutes: 5, // Show warning 5 minutes before expiry
            keepAliveUrlPattern: /\/keepalive\.aspx(?:\?.*)?$/i
        };
    }

    /**
     * Get the singleton instance of SessionManagementService
     */
    public static getInstance(): SessionManagementService {
        if (!SessionManagementService.instance) {
            SessionManagementService.instance = new SessionManagementService();
        }
        return SessionManagementService.instance;
    }

    /**
     * Initialize the service with the auth dialog opener function
     * @param openAuthDialog Function to open the auth dialog
     */
    public initialize(openAuthDialog: OpenAuthDialogFunction): void {
        this.openAuthDialogCallback = openAuthDialog;
    }

    /**
     * Start the session management service
     */
    public start(): void {
        if (this.isRunning) {
            return;
        }

        this.isRunning = true;
        this.lastActivityTime = new Date();
        this.resetSessionTimer();
    }

    /**
     * Stop the session management service
     */
    public stop(): void {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;
        this.clearSessionTimer();
        this.isWarningShown = false;
    }

    /**
     * Register an API call activity (should be called after each API call)
     * @param url The URL of the API call
     * @param method The HTTP method used
     */
    public registerActivity(url: string, method: string = 'GET'): void {
        if (!this.isRunning) {
            return;
        }

        // Skip keep-alive requests
        if (this.isKeepAliveRequest(url)) {
            return;
        }

        // Update last activity time
        this.lastActivityTime = new Date();

        // Reset the session timer
        this.resetSessionTimer();

        // Reset warning state since user is active
        this.isWarningShown = false;
    }

    /**
     * Check if a request is a keep-alive request
     */
    private isKeepAliveRequest(url: string): boolean {
        return this.config.keepAliveUrlPattern.test(url);
    }

    /**
     * Reset the session timer
     */
    private resetSessionTimer(): void {
        this.clearSessionTimer();

        // Calculate time until warning should be shown
        const timeUntilWarning = (this.config.sessionTimeoutMinutes - this.config.warningBeforeExpiryMinutes) * 60 * 1000;

        this.sessionTimer = setTimeout(() => {
            this.showSessionExpiringWarning();
        }, timeUntilWarning);
    }

    /**
     * Clear the current session timer
     */
    private clearSessionTimer(): void {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
            this.sessionTimer = null;
        }
    }

    /**
     * Show the session expiring warning dialog
     */
    private showSessionExpiringWarning(): void {
        if (!this.isRunning || this.isWarningShown) {
            return;
        }

        if (!this.openAuthDialogCallback) {
            console.warn('SessionManagementService: openAuthDialog callback not initialized. Call initialize() first.');
            return;
        }

        this.isWarningShown = true;

        // Show the auth dialog with session expiring message
        const redirectUrl = import.meta.env.VITE_OLD_APP_URL + '/Login.aspx?NewAppRedirect=' + encodeURIComponent(window.location.href);
        this.openAuthDialogCallback('', redirectUrl);
    }

    /**
     * Extend the session (called when user interacts with the warning dialog)
     */
    public extendSession(): void {
        if (!this.isRunning) {
            return;
        }

        // Update last activity time
        this.lastActivityTime = new Date();

        // Reset the session timer
        this.resetSessionTimer();

        // Reset warning state
        this.isWarningShown = false;
    }

    /**
     * Get the current session status
     */
    public getSessionStatus(): {
        isRunning: boolean;
        lastActivityTime: Date;
        timeUntilWarning: number; // in minutes
        isWarningShown: boolean;
    } {
        const now = new Date();
        const timeSinceLastActivity = now.getTime() - this.lastActivityTime.getTime();
        const timeUntilWarning =
            Math.max(0, (this.config.sessionTimeoutMinutes - this.config.warningBeforeExpiryMinutes) * 60 * 1000 - timeSinceLastActivity) /
            60 /
            1000;

        return {
            isRunning: this.isRunning,
            lastActivityTime: this.lastActivityTime,
            timeUntilWarning: Math.round(timeUntilWarning),
            isWarningShown: this.isWarningShown
        };
    }

    /**
     * Update session configuration
     */
    public updateConfig(config: Partial<SessionConfig>): void {
        this.config = { ...this.config, ...config };

        // If service is running, restart with new config
        if (this.isRunning) {
            this.resetSessionTimer();
        }
    }
}

// Export singleton instance
export const sessionService = SessionManagementService.getInstance();
