# Timesheet CSV Upload User Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [CSV File Format](#csv-file-format)
4. [Upload Process](#upload-process)
5. [Handling Existing Data](#handling-existing-data)
6. [Error Messages and Solutions](#error-messages-and-solutions)
7. [Best Practices](#best-practices)
8. [Frequently Asked Questions](#frequently-asked-questions)

## Introduction

The Timesheet CSV Upload feature allows you to quickly import employee hours and related data from a spreadsheet into the timesheet system. This feature is designed to save time when entering data for multiple employees or multiple days at once.

### Key Benefits
- **Bulk Data Entry**: Upload hours for multiple employees and dates in one operation
- **Time Savings**: Eliminate manual entry for large amounts of data
- **Data Validation**: Automatic checking ensures data accuracy before import
- **Flexible Updates**: Add new entries or update existing timesheet data
- **Error Prevention**: Clear error messages help you fix issues before data is imported

### What You Can Upload
- Employee work hours (Standard, Overtime, Double-time)
- Direct pay amounts (bonuses)
- Expense reimbursements
- Job codes and cost centers
- Work classifications and agreements

## Getting Started

### Prerequisites
Before uploading a CSV file, ensure you have:
1. Access to the timesheet system with appropriate permissions
2. A CSV file with employee data in the correct format
3. The timesheet for the correct pay period open

### Accessing the Upload Feature
1. Navigate to the Timesheet Detail page
2. Look for the **Upload** button (usually located in the toolbar)
3. Click the Upload button to open the upload dialog

## CSV File Format

Your CSV file must follow a specific format for successful upload. The system accepts CSV files with the following columns:

### Required Columns
At least one of these employee identifiers must be present:
- **Employee ID** - The employee's ID number in the system
- **SSN** - The employee's Social Security Number

And this date column is always required:
- **Date** - The work date (format: MM/DD/YYYY or YYYY-MM-DD)

### Optional Columns
- **Agreement** - The work agreement name
- **Classification** - Job classification
- **Sub Classification** - Sub-classification if applicable
- **Day** - Day of the week (automatically calculated if not provided)
- **Hourly Rate** - Employee's hourly rate
- **Job Code** - Specific job or project code
- **Earnings Code** - Type of earnings
- **Cost Center** - Department or cost center code
- **ST Hours** - Standard/regular hours worked
- **OT Hours** - Overtime hours worked
- **DT Hours** - Double-time hours worked
- **Direct Pay** - Bonus or additional pay amount
- **Expenses** - Reimbursable expenses

### Sample CSV Format
```csv
Employee ID,SSN,Date,ST Hours,OT Hours,Job Code,Cost Center
12345,,07/15/2024,8,0,PROJ001,DEPT100
12346,,07/15/2024,8,2,PROJ002,DEPT200
```

### Important Format Notes
- Column headers are case-insensitive
- Dates must be within the current pay period
- Numeric values should not include currency symbols
- Empty cells are treated as zero for hours and amounts

## Upload Process

### Step 1: Select Your File
1. Click the **Upload** button on the timesheet page
2. Click **Choose File** or drag and drop your CSV file
3. The system will display the file name once selected

### Step 2: File Validation
The system automatically validates your file:
- Checks for required columns
- Verifies date formats
- Ensures dates are within the pay period
- Validates employee information

### Step 3: Data Processing
After validation, the system will:
1. Match employees in the CSV with system records
2. Check for any existing data that might be updated
3. Identify any conflicts or issues

### Step 4: Review and Confirm
- If all data is valid, you'll see a success message
- The system will show how many records will be imported
- Click **Confirm** to proceed with the upload

## Handling Existing Data

One of the most powerful features of the CSV upload is its ability to intelligently handle existing timesheet data.

### Automatic Updates vs. New Entries
The system automatically determines whether to:
- **Create new entries** for employees not yet in the timesheet
- **Update existing entries** for employees already in the timesheet

### Conflict Resolution
When your CSV contains data for dates that already have hours recorded, you'll see a **Conflicts Found** dialog.

#### Understanding Conflicts
A conflict occurs when:
- An employee already has hours recorded for a specific date
- Your CSV file contains different hours for that same date

#### Conflict Resolution Options
When conflicts are detected, you have three choices:

1. **Overwrite All**
   - Replaces existing data with CSV data
   - Use when you're certain the CSV has the correct information
   - All conflicting entries will be updated

2. **Skip Conflicts**
   - Keeps existing data unchanged
   - Only imports new dates without conflicts
   - Use when you want to preserve existing entries

3. **Cancel Upload**
   - Stops the entire upload process
   - No data is changed
   - Use when you need to review your CSV file

#### Conflict Display Example
The conflict dialog shows:
```
John Doe (7/14/2024)
Existing: ST: 8.00, OT: 0.00
New: ST: 10.00, OT: 2.00
```

This clearly shows what data exists and what would replace it.

## Error Messages and Solutions

### Common Validation Errors

#### "Employee not found"
**Cause**: The Employee ID or SSN in your CSV doesn't match any employee in the system.
**Solution**:
- Verify the employee ID or SSN is correct
- Check for extra spaces or formatting issues
- Ensure the employee is active in the system

#### "Date is outside the pay period"
**Cause**: The date in your CSV is not within the current timesheet's pay period.
**Solution**:
- Check the pay period dates at the top of the timesheet
- Ensure all dates in your CSV fall within this range
- Create a separate timesheet for different pay periods

#### "Missing required headers"
**Cause**: Your CSV file is missing required columns.
**Solution**:
- Ensure your CSV has either "Employee ID" or "SSN" column
- Verify the "Date" column is present
- Check that column headers match expected names

#### "Duplicate entry for employee"
**Cause**: Your CSV has multiple rows for the same employee and date.
**Solution**:
- Review your CSV for duplicate entries
- Combine hours for the same employee/date into one row
- Remove any accidental duplicates

#### "Agreement not found"
**Cause**: The agreement name in your CSV doesn't match system records.
**Solution**:
- Verify the exact agreement name in the system
- Check for spelling errors or extra spaces
- Leave blank if unknown (system may apply defaults)

### File Format Errors

#### "File size exceeds maximum"
**Cause**: Your CSV file is larger than 10MB.
**Solution**:
- Split large files into smaller batches
- Remove any unnecessary columns or data
- Ensure you're uploading a CSV, not an Excel file

#### "Invalid file type"
**Cause**: The file is not a valid CSV format.
**Solution**:
- Save your spreadsheet as CSV (not Excel format)
- Use "Save As" and select "CSV" as the file type
- Ensure the file extension is .csv

## Best Practices

### Before Uploading
1. **Verify Pay Period**: Ensure you're in the correct timesheet period
1. **Check Employee IDs**: Confirm all employee identifiers are accurate
1. **Review Dates**: Verify all dates are in the correct format and period

### Preparing Your CSV
1. **Use Templates**: Start with a known-good CSV format
2. **Validate in Spreadsheet**: Check for obvious errors before uploading
3. **Remove Extra Data**: Delete unnecessary columns or rows
4. **Format Consistently**: Use the same date format throughout

### During Upload
1. **Review Warnings**: Pay attention to all validation messages
2. **Understand Conflicts**: Carefully review conflict information before choosing
3. **Start Small**: Test with a few employees before large uploads
4. **Monitor Progress**: Watch for the success confirmation

### After Upload
1. **Verify Results**: Check that all expected data appears in the timesheet
2. **Review Totals**: Ensure hour totals match your expectations
3. **Check Calculations**: Verify overtime and double-time are correct
4. **Save Your Work**: Don't forget to save the timesheet after upload

## Frequently Asked Questions

### Q: Can I upload data for employees not yet in the timesheet?
**A**: Yes! The system will automatically create new entries for employees not currently in the timesheet, as long as they exist in the employee database.

### Q: What happens if I upload the same file twice?
**A**: The system will detect that the data already exists and show you a conflict dialog. You can choose to overwrite, skip, or cancel.

### Q: Can I upload partial data (like only ST hours)?
**A**: Yes, you can include only the columns you need. Omitted columns will be treated as empty/zero.

### Q: How do I fix errors after upload?
**A**: You can either:
- Upload a corrected CSV file (choosing "Overwrite All" for conflicts)
- Manually edit individual entries in the timesheet

### Q: Is there a limit to how many employees I can upload?
**A**: The system can handle up to 10,000 rows per upload. For larger datasets, split into multiple files.

### Q: Can I upload data for multiple pay periods?
**A**: No, each upload must be for a single pay period. Upload to different timesheets for different periods.

### Q: What date formats are accepted?
**A**: The system accepts:
- MM/DD/YYYY (e.g., 07/15/2024)
- YYYY-MM-DD (e.g., 2024-07-15)
- M/D/YYYY (e.g., 7/15/2024)

### Q: Can I include employees with no hours?
**A**: Rows with zero hours in all columns are automatically skipped. Include at least some hours or other data for the row to be processed.

### Q: How do I handle different pay rates for the same employee?
**A**: Include the appropriate job code or classification for each row. The system can handle different rates based on these classifications.

### Q: What if my CSV has extra columns?
**A**: Extra columns are safely ignored. Only recognized columns are processed.

## Troubleshooting Tips

### Upload Button is Disabled
- Ensure you have permission to edit the timesheet
- Check that the timesheet is not locked or submitted
- Verify you're in edit mode, not view-only mode

### File Won't Upload
- Ensure file size is under 10MB
- Verify it's saved as .csv format
- Check for special characters in the file name
- Try using a different browser

### Data Doesn't Appear After Upload
- Check for validation errors that prevented import
- Ensure you clicked "Confirm" after reviewing
- Refresh the page to see updated data
- Verify you're viewing the correct pay period

### Getting Help
If you encounter issues not covered in this guide:
1. Note any error messages exactly as shown
2. Save your CSV file for reference
3. Contact your system administrator or support team
4. Provide the timesheet ID and upload timestamp

---

*Remember: The CSV upload feature is designed to make your work easier. Take time to understand the process, and always verify your data before and after uploading.*
