# Timesheet Data Flow Architecture

This document describes the current data flow architecture for the React-based timesheet management application built with **Relay** (Facebook's GraphQL client) and **Zustand** for state management.

---

## 1. System Overview

```
 React 19      Relay Modern      Zustand
 ──────────▶  GraphQL API  ──────────▶  Entity / Domain Services  ──────────▶  SQL
   (UI)         (Queries +            (Hot Chocolate,                (EF Core)
                Mutations,             .NET 9)
                Store cache)
```

* **Frontend**: React 19 + TypeScript, Relay Modern for GraphQL, and Zustand for local/UI state.
* **Backend**: ASP.NET Core 9 with Hot Chocolate v13. All node IDs are Relay Global IDs; Hot Chocolate's `[ID]` attribute transparently converts them to CLR scalars.
* **Persistence**: SQL Server via EF Core.

---

## 2. Front-End Architecture

### 2.1 Data Fetching

1. **Route component** executes a `useLazyLoadQuery` that pulls the root `TimeSheet` record and joins all fragments required by the page.
2. Child components consume data via `useFragment` or `usePaginationFragment`.
3. Key fragments are marked `@refetchable` so they can be fetched directly when pages are deep-linked.

### 2.2 Local State & Drafts

| Concern                 | Where it lives | Notes |
| ----------------------- | -------------- | ----- |
| Server data             | Relay store    | Normalized, cache-first.
| Editable drafts         | `TimesheetUIStore` (Zustand) | Stored as **flat draft objects** (PayStub / Detail) **in-memory**; no localStorage persistence. |
| UI chrome               | `TimesheetUIStore` | Ephemeral; not persisted.

Zustand selective selectors keep re-renders minimal. Draft + server data are merged by utility hooks such as `useMergedPayStub` and `useFlatPayStub` in performance-critical paths.

**All paystubs are created immediately on the server via `AddEmptyPayStubMutation`. No temporary/draft-only paystubs exist.**

### 2.3 Mutations & Optimistic Updates

* **Add employee** – `AddEmptyPayStubMutation` creates paystubs **immediately on the server** with optimistic UI updates.
* **Edit hours / details** – `ModifyTimeSheetMutation` bundles all PayStub drafts.
* **Remove stub / detail** – delete inputs with `id: ID!`.
* Every mutation creates an optimistic record via `createOptimisticPayStubInStore`, writing **string IDs** that match the server schema.

---

## 3. Back-End Architecture

### 3.1 GraphQL Schema (Hot Chocolate)

Example input & type:
```csharp
public record AddPayStubInput(
    [ID(nameof(PayStub))] Guid? id,
    [ID(nameof(Employee))] int employeeId,
    string? employeeName);
```

```graphql
type PayStub {
  id: ID!
  employeeId: ID!
  totalHours: Float
  …
}
```

Hot Chocolate decodes `RW1wbG95ZWU6MTIz` ➜ `123` automatically; resolvers and services work with native ints/Guids.

### 3.2 Domain / Data Layer

* Command handlers perform validation and apply business rules.
* Repositories use EF Core; IDs remain native ints/Guids.

---

## 4. End-to-End ID Lifecycle

| Layer              | Representation            |
| ------------------ | ------------------------- |
| React component    | Base64 Global ID string   |
| Relay variables    | `ID`                      |
| Hot Chocolate arg  | `[ID] int | Guid | string`|
| Domain/entity      | int / Guid                |
| Database           | int / uniqueidentifier    |

There is **no manual parsing** of the base64 payload anywhere in the codebase.

---

## 5. Data Flow Patterns

### 5.1 Query Data Flow

The query data flow demonstrates how fragment references and domain models flow through the component hierarchy using the **dual-prop pattern**:

```mermaid
graph TD
    subgraph "Fragment Chain"
        Query[GraphQL Query] --> FragRef1[Fragment Reference $key]
        FragRef1 --> UseFragment1[useFragment Hook]
        UseFragment1 --> DomainModel[Domain Model Data]
    end

    subgraph "Dual-Prop Pattern"
        UseFragment1 --> PayStubRow["PayStubRow Component"]
        FragRef1 --> PayStubRow
        PayStubRow --> |"Domain Model"| UILogic[UI Business Logic]
        PayStubRow --> |"Fragment Ref"| ChildFragment[Child Fragment Component]
    end
```

**Implementation Example:**

```typescript
// PayStubRowWrapper receives fragment reference
const payStubData = useFragment(PayStubTable_payStubFragment, payStubRef);

// Passes BOTH domain model AND fragment reference
<PayStubRow
    payStub={payStubData}             // ✅ Domain model for UI logic
    payStubFragmentRef={payStubRef}   // ✅ Fragment ref for nested components
/>
```

### 5.2 Mutation Data Flow

The mutation flow implements **optimistic updates** with immediate server creation and domain model draft state:

1. **User Interaction**: UI components trigger draft updates via Zustand store actions
2. **Draft Storage**: Changes stored as **flat draft objects** inside `TimesheetUIStore` (in-memory)
3. **Optimistic UI**: Components immediately reflect changes using merged draft + server data
4. **PayStub Creation**: `AddEmptyPayStubMutation` creates paystubs immediately on server
5. **Save Operation**: Store converts domain drafts to GraphQL input types
6. **Store Update**: Relay updates cached data, Zustand clears drafts

### 5.3 State Management Architecture

#### Auto-Save Lifecycle (Non-Destructive Edits)

* All edits first land in **Zustand** draft maps (optimistic UI).
* A debounced task (<strong>1.5&nbsp;s</strong> after last change) converts drafts to a `ModifyTimeSheetInput` and calls `commitModifyTimeSheetMutation`.
* Auto-save **skips** if:
  * Any pay-stub/detail is **marked for deletion**.
  * Validation errors are present.
  * A previous save is still in flight.
* On success the draft maps are cleared; Relay store becomes the single source of truth.

Destructive drafts require an explicit **Save deletions** action from the user. This preserves a confirmation step for irreversible operations.

The following diagram augments the previous mermaid graph:

```mermaid
graph TB
    subgraph "Server State (Relay)"
        GraphQLAPI[GraphQL API] --> RelayStore[Relay Store Cache]
        RelayStore --> TimesheetData[Timesheet Data]
        RelayStore --> PayStubData[PayStub Data]
    end

    subgraph "Local State (Zustand)"
        ZustandStore[Zustand TimesheetUIStore]
        ZustandStore --> DraftChanges[Draft Changes<br/>In-Memory]
        ZustandStore --> UIState[UI State<br/>Not Persisted]
        ZustandStore --> ErrorState[Error State]
    end

    subgraph "Component Layer"
        Components[React Components]
        Components --> |"Read Server Data"| RelayStore
        Components --> |"Read/Write UI State"| ZustandStore
        Components --> |"Optimistic Updates"| MergedData[Merged View<br/>Server + Drafts]
    end
```

---

### 5.4 Pay-Stub List Refetch Pattern (CSV Upload)

The Pay-Stub list fragment (`UploadTimeSheetFragments_timeSheetConsolidated`) is now marked `@refetchable` and consumed via `useRefetchableFragment`.

**Rules**

* After any *bulk pay-stub* mutation (e.g. CSV upload), immediately invoke `refetchPayStubs({ first: 500 }, { fetchPolicy: 'network-only' })` to reconcile optimistic updates with the authoritative server snapshot.
* Pages that can be deep-linked (e.g. `/timesheet/:id/upload`) must preload the generated refetch query **or** call `refetchPayStubs` on mount so they do **not** rely on a parent route to hydrate the cache.
* Always build `existingPayStubMap` from the freshly refetched data before transforming the next CSV — this moves duplicate prevention from an optimistic best-effort to a server-authoritative guarantee.

**UX vs Integrity**

Optimistic updaters still provide instant feedback; the subsequent refetch ensures correctness even if the edge insertion logic ever drifts.

---

## 6. Component Implementation Details

### 6.1 PayStubRowWrapper: Dual-Prop Pattern

The `PayStubRowWrapper` component is the **critical boundary** where fragment references are preserved alongside domain models:

```typescript
const PayStubRowWrapper: React.FC<Props> = ({ payStubRef, ...otherProps }) => {
    // Resolve fragment to domain model
    const payStubData = useFragment(PayStubTable_payStubFragment, payStubRef);

    return (
        <PayStubRow
            payStub={payStubData}           // ✅ Domain model for UI logic
            payStubFragmentRef={payStubRef} // ✅ Preserved fragment reference
            {...otherProps}
        />
    );
};
```

### 6.2 TimesheetUIStore: Performance-Optimized State

The Zustand store provides **performance-optimized state management** with in-memory scoped drafts:

```typescript
interface TimesheetUIStore {
    // Draft state held as flat objects (in memory)
    payStubDrafts: Map<string, FlatPayStubDraft>;
    detailDrafts: Map<string, FlatPayStubDetailDraft>;

    // UI state (not persisted)
    expandedPayStubs: Set<string>;
    editingPayStubs: Set<string>;

    // Validation state
    validationErrorsByPayStubId: Map<string, ValidationError[]>;
    showSaveTimeValidationErrors: boolean;

    // Actions with automatic type safety
    updatePayStubDraft: (timesheetId: string, payStubId: string, changes: Partial<FlatPayStubDraft>) => void;
    setValidationErrors: (timesheetId: string, payStubId: string, errors: ValidationError[]) => void;
}
```

### 6.3 useMergedPayStub: Centralized Merge Logic

The `useMergedPayStub` hook eliminates scattered merge logic across components:

```typescript
export function useMergedPayStub(
    serverPayStub: PayStubDomainModel,
    timesheetId: string
): PayStubDomainModel {
    // Subscribe ONLY to the draft data for this specific pay stub
    const draftData = useTimesheetUIStore(
        state => state.getDraftForPayStub(timesheetId, serverPayStub.id)
    );

    // Memoize the merge operation with DEEP MERGE for nested objects
    const mergedData = useMemo(() => {
        if (!draftData) return serverPayStub;
        return deepMerge(serverPayStub, draftData);
    }, [serverPayStub, draftData]);

    return mergedData;
}
```

---

## 7. Performance Optimization Patterns

### 7.1 Fragment Colocation

Each component defines exactly the data it needs via GraphQL fragments:

```typescript
export const PayStubTable_payStubFragment = graphql`
    fragment PayStubTable_payStub on PayStub {
        id
        employeeId
        name
        totalHours
        details {
            id
            workDate
            stHours
            otHours
            dtHours
            bonus
            expenses
        }
        ...TimeSheetDetailTableView_payStub
    }
`;
```

### 7.2 Zustand Selector Performance

Use selective subscriptions to prevent unnecessary re-renders:

```typescript
const PayStubRow: React.FC<Props> = ({ payStub }) => {
    // Each subscription is independent and specific
    const isExpanded = useTimesheetUIStore(
        state => state.expandedPayStubs.has(`${timesheetId}:${payStub.id}`)
    );

    // Action dispatchers have stable references - no re-renders
    const updateDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
};
```

### 7.3 Optimistic UI Updates

UI reflects changes immediately via Zustand store, server updates reconcile later:

```typescript
// Immediate UI update
updatePayStubDraft(timesheetId, payStubId, { stHours: newValue });

// Background save operation
saveAllChanges(environment, timesheetId, employerGuid).catch(error => {
    // Revert UI on failure + show error
});
```

---

## 8. Type Safety Architecture

### 8.1 GraphQL Type Generation

Relay compiler generates TypeScript types from GraphQL schema and fragments:

```bash
relay-compiler --src ./src --schema ./schema.graphql
```

### 8.2 Schema-Aware Flat Types

Relay fragment types are readonly and fragment-scoped; utilities that live outside a fragment rely on **schema-aware flat types**:

```typescript
import type { PayStubTable_payStub$data } from '@/relay/__generated__/PayStubTable_payStub.graphql';

export type FlatPayStubDraft = Merge<
  StripRelayHelpers<PayStubTable_payStub$data>,
  {
    _uiLastModified: number;
  }
>;
```

---

## 9. Error Handling Architecture

### 9.1 Error Classification

**Network Layer Errors**
- GraphQL Errors: Server validation, business rule violations
- Network Errors: Connection timeouts, 500 errors, offline state

**Application Layer Errors**
- Validation Errors: Client-side business rule validation
- UI Errors: Component state corruption, rendering issues

### 9.2 Zustand Error State Integration

```typescript
const useTimesheetUIStore = create<TimesheetUIStore>()(
    (set, get) => ({
        errorsByPayStubId: new Map<string, TimesheetError>(),
        validationErrorsByPayStubId: new Map<string, ValidationError[]>(),

        setError: (payStubId: string, error: TimesheetError | null) => {
            // Error state management
        }
    })
);
```

---

## 10. Validation and Cell Highlighting

### 10.1 Validation Flow

1. **Real-time validation** during editing (warnings only)
2. **Save-time validation** on save attempt (blocking errors)
3. **Auto-expand rows** with validation errors
4. **Cell highlighting** for fields with errors

### 10.2 Recent Enhancements

- **Fixed validation errors** showing "Unknown" employee names by including employee fragments
- **Fixed HTML encoding** issues in error messages
- **Fixed employee name sanitization** to allow comma-separated names
- **Implemented auto-expand** for rows with validation errors
- **Fixed cell highlighting** through proper state synchronization

---

## 11. Areas for Improvement

1. **Real-time updates** – consider GraphQL subscriptions so multiple users see edits live
2. **Performance telemetry** – add React Profiler marks and dashboards
3. **Payload optimization** – audit fragments to remove unused fields
4. **Draft persistence** – consider optional localStorage persistence for draft state
5. **Validation clearing** – implement for all field types (currently only number fields)

---

_Last reviewed: 2025-07-22_