import { decodeGlobalId, toNumericId } from '../decodeGlobalId';

describe('decodeGlobalId', () => {
    // Mock btoa/atob for consistent testing
    const mockBtoa = (str: string) => Buffer.from(str).toString('base64');
    const mockAtob = (str: string) => Buffer.from(str, 'base64').toString('binary');

    beforeAll(() => {
        global.btoa = mockBtoa;
        global.atob = mockAtob;
    });

    describe('decodeGlobalId', () => {
        it('should decode a valid Global ID', () => {
            const globalId = mockBtoa('Employee:123');
            const result = decodeGlobalId(globalId);
            expect(result).toBe(123);
        });

        it('should handle null and undefined inputs', () => {
            expect(decodeGlobalId(null)).toBeNull();
            expect(decodeGlobalId(undefined)).toBeNull();
        });

        it('should handle invalid Global ID format', () => {
            const invalidGlobalId = mockBtoa('InvalidFormat');
            const result = decodeGlobalId(invalidGlobalId);
            expect(result).toBeNull();
        });

        it('should handle non-numeric ID in Global ID', () => {
            const globalId = mockBtoa('Employee:abc');
            const result = decodeGlobalId(globalId);
            expect(result).toBeNull();
        });

        it('should handle malformed base64', () => {
            const malformedBase64 = 'not-base64';
            const result = decodeGlobalId(malformedBase64);
            expect(result).toBeNull();
        });
    });

    describe('toNumericId', () => {
        it('should return null for null/undefined inputs', () => {
            expect(toNumericId(null)).toBeNull();
            expect(toNumericId(undefined)).toBeNull();
        });

        it('should return number inputs as-is', () => {
            expect(toNumericId(123)).toBe(123);
            expect(toNumericId(0)).toBe(0);
        });

        it('should handle NaN number inputs', () => {
            expect(toNumericId(NaN)).toBeNull();
        });

        it('should parse numeric string inputs', () => {
            expect(toNumericId('123')).toBe(123);
            expect(toNumericId('0')).toBe(0);
        });

        it('should decode Global ID string inputs', () => {
            const globalId = mockBtoa('Employee:456');
            expect(toNumericId(globalId)).toBe(456);
        });

        it('should handle non-numeric, non-Global ID strings', () => {
            expect(toNumericId('abc')).toBeNull();
        });
    });

    describe('Node.js compatibility', () => {
        it('should work when atob is not available (Node.js environment)', () => {
            const originalAtob = global.atob;
            delete (global as any).atob;

            try {
                const globalId = mockBtoa('Employee:789');
                const result = decodeGlobalId(globalId);
                expect(result).toBe(789);
            } finally {
                global.atob = originalAtob;
            }
        });
    });
});