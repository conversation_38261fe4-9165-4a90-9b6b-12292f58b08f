import { convertRawToProcessed } from '../convertRawToProcessed';
import { RawCsvRow, NormalizedCsvHeader } from '../../types';

describe('convertRawToProcessed', () => {
    it('should convert a valid row correctly', () => {
        const validRows: RawCsvRow[] = [{
            [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: '12345',
            [NormalizedCsvHeader.SSN]: '***********',
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.AGREEMENT]: 'LOCAL 7 TILE',
            [NormalizedCsvHeader.CLASSIFICATION]: '7TF Tile Finisher',
            [NormalizedCsvHeader.SUBCLASSIFICATION]: 'Sub-A',
            [NormalizedCsvHeader.JOBCODE]: '1531',
            [NormalizedCsvHeader.COSTCENTER]: 'CC100',
            [NormalizedCsvHeader.HOURLYRATE]: '45.50',
            [NormalizedCsvHeader.STHOURS]: '8',
            [NormalizedCsvHeader.OTHOURS]: '2',
            [NormalizedCsvHeader.DTHOURS]: '0',
            [NormalizedCsvHeader.DIRECTPAY]: '100',
            [NormalizedCsvHeader.EXPENSES]: '50',
            [NormalizedCsvHeader.EARNINGSCODE]: 'REG',
            [NormalizedCsvHeader.DAY]: 'Monday'
        }];

        const result = convertRawToProcessed(validRows);

        expect(result).toHaveLength(1);
        expect(result[0]).toMatchObject({
            employeeId: null,
            externalEmployeeId: '12345',
            ssn: '***********',
            workDate: '2025-05-05T00:00:00.000Z',
            agreementName: 'LOCAL 7 TILE',
            classificationName: '7TF Tile Finisher',
            subClassificationName: 'Sub-A',
            jobCode: '1531',
            costCenter: 'CC100',
            hourlyRate: 45.50,
            stHours: 8,
            otHours: 2,
            dtHours: 0,
            bonus: 100,
            expenses: 50,
            earningsCode: 'REG',
            day: 'Monday',
            rowIndex: 2,
            isValid: true,
            errors: []
        });
    });

    it('should clamp hours greater than 24 to 24', () => {
        const rowWithExcessiveHours: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.STHOURS]: '30',
            [NormalizedCsvHeader.OTHOURS]: '25.5',
            [NormalizedCsvHeader.DTHOURS]: '48'
        }];

        const result = convertRawToProcessed(rowWithExcessiveHours);

        expect(result[0].stHours).toBe(24);
        expect(result[0].otHours).toBe(24);
        expect(result[0].dtHours).toBe(24);
    });

    it('should clamp negative hours to 0', () => {
        const rowWithNegativeHours: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.STHOURS]: '-5',
            [NormalizedCsvHeader.OTHOURS]: '-10.5',
            [NormalizedCsvHeader.DTHOURS]: '-1'
        }];

        const result = convertRawToProcessed(rowWithNegativeHours);

        expect(result[0].stHours).toBe(0);
        expect(result[0].otHours).toBe(0);
        expect(result[0].dtHours).toBe(0);
    });

    it('should handle hour clamping edge cases', () => {
        const rowWithEdgeCases: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.STHOURS]: '0',
            [NormalizedCsvHeader.OTHOURS]: '24',
            [NormalizedCsvHeader.DTHOURS]: '24.0'
        }];

        const result = convertRawToProcessed(rowWithEdgeCases);

        expect(result[0].stHours).toBe(0);
        expect(result[0].otHours).toBe(24);
        expect(result[0].dtHours).toBe(24);
    });

    it('should handle null/empty hours values', () => {
        const rowWithEmptyHours: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.STHOURS]: '',
            [NormalizedCsvHeader.OTHOURS]: undefined,
            [NormalizedCsvHeader.DTHOURS]: '0'
        }];

        const result = convertRawToProcessed(rowWithEmptyHours);

        expect(result[0].stHours).toBeNull();
        expect(result[0].otHours).toBeNull();
        expect(result[0].dtHours).toBe(0);
    });

    it('should convert dates to UTC midnight format', () => {
        const testCases = [
            { input: '2025-05-05', expected: '2025-05-05T00:00:00.000Z' },
            { input: '5/5/2025', expected: '2025-05-05T00:00:00.000Z' },
            { input: '12/31/2025', expected: '2025-12-31T00:00:00.000Z' },
            { input: '1/1/2025', expected: '2025-01-01T00:00:00.000Z' }
        ];

        testCases.forEach(({ input, expected }) => {
            const row: RawCsvRow[] = [{
                [NormalizedCsvHeader.DATE]: input
            }];

            const result = convertRawToProcessed(row);
            expect(result[0].workDate).toBe(expected);
        });
    });

    it('should handle timezone edge cases for UTC conversion', () => {
        // Test dates that might be problematic in different timezones
        const edgeCases = [
            // Date that could shift in PST/PDT (-8/-7 hours)
            { input: '2025-01-01', expected: '2025-01-01T00:00:00.000Z' },
            // Date during DST transition periods
            { input: '2025-03-09', expected: '2025-03-09T00:00:00.000Z' },
            { input: '2025-11-02', expected: '2025-11-02T00:00:00.000Z' },
            // Year boundary dates
            { input: '2024-12-31', expected: '2024-12-31T00:00:00.000Z' },
            { input: '2025-01-01', expected: '2025-01-01T00:00:00.000Z' }
        ];

        edgeCases.forEach(({ input, expected }) => {
            const row: RawCsvRow[] = [{
                [NormalizedCsvHeader.DATE]: input
            }];

            const result = convertRawToProcessed(row);
            
            // The conversion should be independent of the local timezone
            expect(result[0].workDate).toBe(expected);
            
            // Verify the date is truly UTC (ends with Z)
            expect(result[0].workDate.endsWith('Z')).toBe(true);
        });
    });

    it('should handle invalid date formats', () => {
        const rowWithInvalidDate: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: 'invalid-date'
        }];

        const result = convertRawToProcessed(rowWithInvalidDate);
        expect(result[0].workDate).toBe('');
    });

    it('should correctly set row indices', () => {
        const multipleRows: RawCsvRow[] = [
            { [NormalizedCsvHeader.DATE]: '2025-05-05' },
            { [NormalizedCsvHeader.DATE]: '2025-05-06' },
            { [NormalizedCsvHeader.DATE]: '2025-05-07' }
        ];

        const result = convertRawToProcessed(multipleRows);

        expect(result[0].rowIndex).toBe(2); // First data row (after header)
        expect(result[1].rowIndex).toBe(3); // Second data row
        expect(result[2].rowIndex).toBe(4); // Third data row
    });

    it('should handle optional fields correctly', () => {
        const minimalRow: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: '12345'
        }];

        const result = convertRawToProcessed(minimalRow);

        expect(result[0]).toMatchObject({
            externalEmployeeId: '12345',
            ssn: undefined,
            agreementName: undefined,
            classificationName: undefined,
            subClassificationName: undefined,
            jobCode: undefined,
            costCenter: undefined,
            hourlyRate: null,
            stHours: null,
            otHours: null,
            dtHours: null,
            bonus: null,
            expenses: null,
            earningsCode: undefined,
            day: undefined
        });
    });

    it('should parse numeric values correctly', () => {
        const rowWithNumbers: RawCsvRow[] = [{
            [NormalizedCsvHeader.DATE]: '2025-05-05',
            [NormalizedCsvHeader.HOURLYRATE]: '45.50',
            [NormalizedCsvHeader.DIRECTPAY]: '1000.25',
            [NormalizedCsvHeader.EXPENSES]: '75.75'
        }];

        const result = convertRawToProcessed(rowWithNumbers);

        expect(result[0].hourlyRate).toBe(45.50);
        expect(result[0].bonus).toBe(1000.25);
        expect(result[0].expenses).toBe(75.75);
    });
});