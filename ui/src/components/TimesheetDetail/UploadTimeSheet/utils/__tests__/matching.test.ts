import { transformValidatedDataWithContext } from '../dataTransformation';
import { ProcessedTimesheetEntry } from '../../types';
import type { EmployeeUI, AgreementUI } from '@/src/types/relay-ui-extensions';

describe('Employee and Agreement Matching', () => {
    const mockEmployees: EmployeeUI[] = [
        {
            id: 'emp-1',
            firstName: '<PERSON>',
            lastName: 'Doe',
            externalEmployeeId: '12345',
            ssn: '***********',
            value: 'emp-1',
            text: '<PERSON><PERSON>, <PERSON>'
        },
        {
            id: 'emp-2',
            firstName: '<PERSON>',
            lastName: '<PERSON>',
            externalEmployeeId: '67890',
            ssn: '***********',
            value: 'emp-2',
            text: '<PERSON>, <PERSON>'
        },
        {
            id: 'emp-3',
            firstName: '<PERSON>',
            lastName: '<PERSON>',
            externalEmployeeId: '11111',
            ssn: '***********', // Same SSN as <PERSON>
            value: 'emp-3',
            text: '<PERSON>, <PERSON>'
        }
    ];

    const mockAgreements: AgreementUI[] = [
        {
            id: 'agr-1',
            name: 'LOCAL 7 TILE',
            value: 'agr-1',
            text: 'LOCAL 7 TILE'
        },
        {
            id: 'agr-2',
            name: 'Local 12 Plumbers',
            value: 'agr-2',
            text: 'Local 12 Plumbers'
        }
    ];

    describe('Employee ID vs SSN Priority', () => {
        it('should match by Employee ID first even when SSN matches different employee', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: '11111',
                ssn: '***********',
                workDate: '2025-05-05T00:00:00Z',
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            // Should match Bob Johnson (emp-3) by Employee ID, not John Doe by SSN
            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].employeeId).toBe('emp-3');
            expect(result.payStubs[0].name).toBe('Bob Johnson');
            expect(result.rowErrors).toHaveLength(0);
        });

        it('should fall back to SSN matching when Employee ID is not provided', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: undefined,
                ssn: '***********',
                workDate: '2025-05-05T00:00:00Z',
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            // Should match Jane Smith by SSN
            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].employeeId).toBe('emp-2');
            expect(result.payStubs[0].name).toBe('Jane Smith');
            expect(result.rowErrors).toHaveLength(0);
        });

        it('should handle SSN with different formats', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: undefined,
                ssn: '987654321', // No hyphens
                workDate: '2025-05-05T00:00:00Z',
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            // Should still match Jane Smith
            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].employeeId).toBe('emp-2');
            expect(result.rowErrors).toHaveLength(0);
        });
    });

    describe('Agreement Matching', () => {
        it('should match agreements case-insensitively', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: '12345',
                workDate: '2025-05-05T00:00:00Z',
                agreementName: 'local 7 tile', // lowercase
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, mockAgreements);

            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].details[0].agreementId).toBe('agr-1');
            expect(result.rowErrors).toHaveLength(0);
        });

        it('should report error for non-existent agreement', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: '12345',
                workDate: '2025-05-05T00:00:00Z',
                agreementName: 'Non-existent Agreement',
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, mockAgreements);

            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].details[0].agreementId).toBeNull();
            expect(result.rowErrors).toHaveLength(1);
            expect(result.rowErrors[0]).toMatchObject({
                type: 'row',
                rowIndex: 2,
                columnName: 'agreement',
                message: 'Agreement "Non-existent Agreement" not found',
                code: 'AGREEMENT_NOT_FOUND'
            });
        });

        it('should handle entries without agreement names', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: '12345',
                workDate: '2025-05-05T00:00:00Z',
                agreementName: undefined,
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, mockAgreements);

            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].details[0].agreementId).toBeNull();
            expect(result.rowErrors).toHaveLength(0);
        });
    });

    describe('Duplicate Detection', () => {
        it('should detect duplicate (employeeId, workDate) pairs', () => {
            const entries: ProcessedTimesheetEntry[] = [
                {
                    externalEmployeeId: '12345',
                    workDate: '2025-05-05T00:00:00Z',
                    stHours: 8,
                    rowIndex: 2,
                    isValid: true,
                    errors: []
                },
                {
                    externalEmployeeId: '12345',
                    workDate: '2025-05-05T00:00:00Z',
                    stHours: 4,
                    rowIndex: 3,
                    isValid: true,
                    errors: []
                }
            ];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            // First entry should be accepted, second should be rejected
            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].details).toHaveLength(1);
            expect(result.payStubs[0].details[0].stHours).toBe(8);
            
            expect(result.rowErrors).toHaveLength(1);
            expect(result.rowErrors[0]).toMatchObject({
                type: 'row',
                rowIndex: 3,
                message: 'Duplicate entry for employee John Doe on date 2025-05-05T00:00:00Z',
                code: 'DUPLICATE_ENTRY'
            });
        });

        it('should allow same employee on different dates', () => {
            const entries: ProcessedTimesheetEntry[] = [
                {
                    externalEmployeeId: '12345',
                    workDate: '2025-05-05T00:00:00Z',
                    stHours: 8,
                    rowIndex: 2,
                    isValid: true,
                    errors: []
                },
                {
                    externalEmployeeId: '12345',
                    workDate: '2025-05-06T00:00:00Z',
                    stHours: 8,
                    rowIndex: 3,
                    isValid: true,
                    errors: []
                }
            ];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            expect(result.payStubs).toHaveLength(1);
            expect(result.payStubs[0].details).toHaveLength(2);
            expect(result.rowErrors).toHaveLength(0);
        });

        it('should allow different employees on same date', () => {
            const entries: ProcessedTimesheetEntry[] = [
                {
                    externalEmployeeId: '12345',
                    workDate: '2025-05-05T00:00:00Z',
                    stHours: 8,
                    rowIndex: 2,
                    isValid: true,
                    errors: []
                },
                {
                    externalEmployeeId: '67890',
                    workDate: '2025-05-05T00:00:00Z',
                    stHours: 8,
                    rowIndex: 3,
                    isValid: true,
                    errors: []
                }
            ];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            expect(result.payStubs).toHaveLength(2);
            expect(result.rowErrors).toHaveLength(0);
        });
    });

    describe('Employee Not Found', () => {
        it('should report error for unmatched employee', () => {
            const entries: ProcessedTimesheetEntry[] = [{
                externalEmployeeId: '99999',
                ssn: '***********',
                workDate: '2025-05-05T00:00:00Z',
                rowIndex: 2,
                isValid: true,
                errors: []
            }];

            const result = transformValidatedDataWithContext(entries, mockEmployees, []);

            expect(result.payStubs).toHaveLength(0);
            expect(result.rowErrors).toHaveLength(1);
            expect(result.rowErrors[0]).toMatchObject({
                type: 'row',
                rowIndex: 2,
                message: 'Employee not found with Employee ID "99999"',
                code: 'EMPLOYEE_NOT_FOUND'
            });
        });
    });
});