/**
 * Extended Data Transformation Utilities
 *
 * This file contains functions for transforming validated CSV data with awareness
 * of existing PayStubs, separating add and modify operations, and detecting conflicts.
 */

import { ProcessedTimesheetEntry, PayStubUpload, PayStubDetailUpload, RowError } from '../types';
import { EmployeeUI, AgreementUI } from '@/src/types/relay-ui-extensions';
import { Constants } from '@/src/constants/global';
import { ExistingPayStubMap, ConflictInfo, detectConflict } from './existingPayStubMap';

// Re-export types from dataTransformation
import { transformValidatedDataWithContext } from './dataTransformation';

/**
 * ModifyPayStubInput type for updating existing PayStubs
 */
export interface ModifyPayStubInput {
    id: string; // PayStub ID
    employeeId: string;
    name?: string;
    details: Array<{
        id?: string; // Detail ID for existing details
        workDate: string;
        stHours?: number | null;
        otHours?: number | null;
        dtHours?: number | null;
        agreementId?: number | null;
        classificationId?: number | null;
        subClassificationId?: number | null;
        earningsCode?: string | null;
        jobCode?: string | null;
        costCenter?: string | null;
        hourlyRate?: number | null;
        bonus?: number | null;
        expenses?: number | null;
    }>;
}

/**
 * Transform result with separated add/modify operations
 */
export interface TransformResultWithExisting {
    payStubsToAdd: PayStubUpload[]; // New PayStubs
    payStubsToModify: ModifyPayStubInput[]; // Existing PayStubs to update
    conflicts: ConflictInfo[]; // Details that would overwrite existing data
    rowErrors: RowError[];
}

/**
 * Generate a client temporary ID for PayStub or PayStubDetail during CSV upload
 * Follows the consistent pattern used throughout the application
 */
function generateClientTempId(entityType: 'paystub' | 'detail'): string {
    return `client:temp:${entityType}:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Helper function to safely convert string IDs to numbers for backend mutations
 * GraphQL queries return IDs as strings, but backend mutations expect numbers
 */
function convertIdToNumber(id: string | number | null | undefined): number | undefined {
    if (id === null || id === undefined) return undefined;
    if (typeof id === 'number') return id;

    const parsed = parseInt(String(id), 10);
    return isNaN(parsed) ? undefined : parsed;
}

/**
 * Helper function to handle nullable values from CSV
 * Returns the value if defined, null if empty string, undefined if not provided
 */
function nullable<T>(v: T | undefined | null): T | null | undefined {
    return v !== undefined ? (v === '' ? null : v) : undefined;
}

/**
 * Matches an employee from context employee data by SSN or Employee ID
 * @param entry The processed timesheet entry
 * @param employees Employees from context (EmployeeUI[])
 * @returns Matched employee or null if no match
 */
const matchEmployeeFromContext = (entry: ProcessedTimesheetEntry, employees: EmployeeUI[]): EmployeeUI | null => {
    console.log('[CSV_UPLOAD_DEBUG] matchEmployeeFromContext called for entry:', {
        externalEmployeeId: entry.externalEmployeeId,
        ssn: entry.ssn,
        rowIndex: entry.rowIndex
    });

    if (!employees || employees.length === 0) {
        console.log('[CSV_UPLOAD_DEBUG] No employees available for matching');
        return null;
    }

    // Try to match by Employee ID first
    if (entry.externalEmployeeId) {
        console.log('[CSV_UPLOAD_DEBUG] Trying to match by Employee ID:', entry.externalEmployeeId);
        const matchedById = employees.find((employee) => {
            const matches = employee.externalEmployeeId?.trim() === entry.externalEmployeeId?.trim();
            if (matches) {
                console.log('[CSV_UPLOAD_DEBUG] Found match by Employee ID:', {
                    employeeId: employee.id,
                    externalEmployeeId: employee.externalEmployeeId
                });
            }
            return matches;
        });
        if (matchedById) return matchedById;
    }

    // Then try to match by SSN
    if (entry.ssn) {
        console.log('[CSV_UPLOAD_DEBUG] Trying to match by SSN');
        const normalizedEntrySSN = entry.ssn.replace(/[-\s]/g, '');
        const matchedBySSN = employees.find((employee) => {
            let employeeSSN: string | undefined;
            if (employee.ssn) {
                employeeSSN = Array.isArray(employee.ssn) ? employee.ssn[0] : employee.ssn;
            } else if ((employee as any).SSN) {
                employeeSSN = (employee as any).SSN;
            }
            const normalizedEmployeeSSN = employeeSSN?.replace(/[-\s]/g, '');
            const matches = normalizedEmployeeSSN === normalizedEntrySSN;
            if (matches) {
                console.log('[CSV_UPLOAD_DEBUG] Found match by SSN:', {
                    employeeId: employee.id,
                    ssn: employeeSSN
                });
            }
            return matches;
        });
        if (matchedBySSN) return matchedBySSN;
    }

    console.log('[CSV_UPLOAD_DEBUG] No employee match found');
    return null;
};

/**
 * Matches an agreement from context using Map-based O(1) lookup
 * @param agreementName The agreement name to match
 * @param agreements Agreements from context
 * @returns Matched agreement or null if no match
 */
const matchAgreementFromContext = (agreementName: string | undefined | null, agreements: AgreementUI[]): AgreementUI | null => {
    if (!agreementName || !agreements || agreements.length === 0) {
        return null;
    }

    // Case-insensitive match using normalized name
    const normalizedName = agreementName.trim().toLowerCase();
    return agreements.find((agreement) => agreement.name.trim().toLowerCase() === normalizedName) || null;
};

/**
 * Matches an earnings code from the TimesheetEarningCodesList by name
 * @param earningsCodeName The earnings code name to match
 * @returns Matched earnings code value or null if no match
 */
const matchEarningsCode = (earningsCodeName: string | undefined | null): string | null => {
    if (!earningsCodeName || earningsCodeName.trim() === '') {
        return null;
    }

    // Case-insensitive match
    const normalizedName = earningsCodeName.trim().toLowerCase();

    const matchedEarningsCode = Constants.TimesheetEarningCodesList.find(
        (earningsCode) => earningsCode.text.toLowerCase() === normalizedName
    );

    return matchedEarningsCode ? matchedEarningsCode.value : null;
};

/**
 * Creates a PayStubDetailUpload object from a processed timesheet entry
 * @param entry The processed timesheet entry
 * @param payStubId The ID of the parent PayStubUpload
 * @returns PayStubDetailUpload object
 */
const createPayStubDetail = (entry: ProcessedTimesheetEntry, payStubId: string): PayStubDetailUpload => {
    // Calculate total hours
    const stHours = entry.stHours || 0;
    const otHours = entry.otHours || 0;
    const dtHours = entry.dtHours || 0;
    const totalHours = stHours + otHours + dtHours;

    return {
        id: generateClientTempId('detail'), // Generate a unique client temp ID
        payStubId,
        workDate: entry.workDate,
        stHours: entry.stHours || null,
        otHours: entry.otHours || null,
        dtHours: entry.dtHours || null,
        totalHours: totalHours > 0 ? totalHours : null,
        jobCode: entry.jobCode || null,
        earningsCode: entry.earningsCode || null,
        agreementId: convertIdToNumber(entry.agreementId) || entry.agreementId || null,
        classificationId: convertIdToNumber(entry.classificationId) || entry.classificationId || null,
        subClassificationId: convertIdToNumber(entry.subClassificationId) || entry.subClassificationId || null,
        costCenter: entry.costCenter || null,
        hourlyRate: entry.hourlyRate || null,
        bonus: entry.bonus || null,
        expenses: entry.expenses || null,
        rowIndex: entry.rowIndex,
        inError: false,
        errors: []
    };
};

/**
 * Transforms validated CSV data with existing PayStub awareness
 * Separates new PayStubs from updates to existing ones and detects conflicts
 *
 * @param validatedData Array of processed timesheet entries
 * @param employees Employees from context (EmployeeUI[])
 * @param existingPayStubMap Map of existing PayStubs from the timesheet
 * @param agreements Agreements from context (if available)
 * @returns Object containing payStubsToAdd, payStubsToModify, conflicts, and rowErrors
 */
export const transformWithExistingData = (
    validatedData: ProcessedTimesheetEntry[],
    employees: EmployeeUI[],
    existingPayStubMap: ExistingPayStubMap,
    agreements: AgreementUI[] = []
): TransformResultWithExisting => {
    console.log('[CSV_UPLOAD_DEBUG] transformWithExistingData called');
    console.log('[CSV_UPLOAD_DEBUG] validatedData count:', validatedData.length);
    console.log('[CSV_UPLOAD_DEBUG] employees count:', employees.length);
    console.log('[CSV_UPLOAD_DEBUG] existingPayStubMap:', existingPayStubMap);
    console.log('[CSV_UPLOAD_DEBUG] existingPayStubMap keys:', Object.keys(existingPayStubMap));

    const rowErrors: RowError[] = [];
    const conflicts: ConflictInfo[] = [];
    const employeeMap = new Map<string | number | null, ProcessedTimesheetEntry[]>();
    const duplicateCheckSet = new Set<string>();

    // First pass: match employees and validate data
    validatedData.forEach((entry) => {
        const matchedEmployee = matchEmployeeFromContext(entry, employees);

        if (matchedEmployee) {
            entry.employeeId = matchedEmployee.id;
            entry.employeeName = `${matchedEmployee.firstName} ${matchedEmployee.lastName}`;

            console.log('[CSV_UPLOAD_DEBUG] Matched employee:', {
                entryIndex: entry.rowIndex,
                employeeId: entry.employeeId,
                employeeName: entry.employeeName,
                workDate: entry.workDate
            });

            // Match agreement if provided
            if (entry.agreementName && agreements.length > 0) {
                const matchedAgreement = matchAgreementFromContext(entry.agreementName, agreements);
                if (matchedAgreement) {
                    entry.agreementId = matchedAgreement.id;
                } else {
                    rowErrors.push({
                        type: 'row',
                        rowIndex: entry.rowIndex,
                        columnName: 'agreement',
                        message: `Agreement "${entry.agreementName}" not found`,
                        code: 'AGREEMENT_NOT_FOUND'
                    });
                }
            }

            // Match earnings code if provided
            if (entry.earningsCode) {
                const isValidValue = Constants.TimesheetEarningCodesList.some((code) => code.value === entry.earningsCode);
                if (!isValidValue) {
                    const matchedEarningsCode = matchEarningsCode(entry.earningsCode);
                    if (matchedEarningsCode) {
                        entry.earningsCode = matchedEarningsCode;
                    } else {
                        rowErrors.push({
                            type: 'row',
                            rowIndex: entry.rowIndex,
                            columnName: 'earningsCode',
                            message: `Earnings code "${entry.earningsCode}" not found`,
                            code: 'INVALID_VALUE'
                        });
                        entry.earningsCode = undefined;
                    }
                }
            }

            // Check for duplicate (employeeId, workDate) pairs within the CSV
            const duplicateKey = `${entry.employeeId}_${entry.workDate}`;
            if (duplicateCheckSet.has(duplicateKey)) {
                rowErrors.push({
                    type: 'row',
                    rowIndex: entry.rowIndex,
                    message: `Duplicate entry for employee ${entry.employeeName || entry.employeeId} on date ${entry.workDate}`,
                    code: 'DUPLICATE_ENTRY'
                });
                entry.isValid = false;
            } else {
                duplicateCheckSet.add(duplicateKey);

                // Check for conflicts with existing PayStub details
                const lookupKey = String(entry.employeeId);
                console.log(`[CSV_UPLOAD_DEBUG] Looking up existing data with key '${lookupKey}'`);
                const existingData = existingPayStubMap[lookupKey];

                if (existingData) {
                    console.log('[CSV_UPLOAD_DEBUG] Found existing PayStub data:', {
                        payStubId: existingData.payStubId,
                        detailDates: Array.from(existingData.existingDetails.keys())
                    });

                    if (existingData.existingDetails.has(entry.workDate)) {
                        const existingDetail = existingData.existingDetails.get(entry.workDate)!;
                        console.log('[CSV_UPLOAD_DEBUG] Found existing detail for date:', entry.workDate);
                        const conflict = detectConflict(
                            String(entry.employeeId),
                            entry.employeeName || '',
                            entry.workDate,
                            {
                                stHours: entry.stHours || null,
                                otHours: entry.otHours || null,
                                dtHours: entry.dtHours || null,
                                bonus: entry.bonus || null,
                                expenses: entry.expenses || null
                            },
                            existingDetail
                        );

                        if (conflict) {
                            console.log('[CSV_UPLOAD_DEBUG] Conflict detected:', conflict);
                            conflicts.push(conflict);
                        }
                    } else {
                        console.log('[CSV_UPLOAD_DEBUG] No existing detail for date:', entry.workDate);
                    }
                } else {
                    console.log(`[CSV_UPLOAD_DEBUG] No existing PayStub data found for employee key '${lookupKey}'`);
                }

                // Group by employee ID
                const employeeId = entry.employeeId || null;
                const employeeEntries = employeeMap.get(employeeId) || [];
                employeeEntries.push(entry);
                employeeMap.set(employeeId, employeeEntries);
            }
        } else {
            rowErrors.push({
                type: 'row',
                rowIndex: entry.rowIndex,
                message: `Employee not found with ${entry.externalEmployeeId ? 'Employee ID' : 'SSN'} "${entry.externalEmployeeId || entry.ssn}"`,
                code: 'EMPLOYEE_NOT_FOUND'
            });
            entry.isValid = false;
        }
    });

    // Second pass: create PayStubs, separating adds from updates
    const payStubsToAdd: PayStubUpload[] = [];
    const payStubsToModify: ModifyPayStubInput[] = [];

    employeeMap.forEach((entries, employeeId) => {
        const validEntries = entries.filter((entry) => entry.isValid);
        if (validEntries.length === 0) return;

        const firstEntry = validEntries[0];
        const employeeIdStr = String(employeeId);

        console.log(`[CSV_UPLOAD_DEBUG] Processing employee:`, {
            employeeId: employeeId,
            employeeIdType: typeof employeeId,
            employeeIdStr: employeeIdStr,
            validEntriesCount: validEntries.length
        });
        console.log(`[CSV_UPLOAD_DEBUG] Looking up existing data with key '${employeeIdStr}'`);
        console.log('[CSV_UPLOAD_DEBUG] Available keys in existingPayStubMap:', Object.keys(existingPayStubMap));
        console.log('[CSV_UPLOAD_DEBUG] Key comparison:', {
            lookupKey: employeeIdStr,
            mapKeys: Object.keys(existingPayStubMap),
            keyMatch: Object.keys(existingPayStubMap).includes(employeeIdStr)
        });

        const existingData = existingPayStubMap[employeeIdStr];
        console.log('[CSV_UPLOAD_DEBUG] Existing data lookup result:', existingData ? 'FOUND' : 'NOT FOUND');

        if (existingData) {
            // Employee has existing PayStub - create modify input
            console.log('[CSV_UPLOAD_DEBUG] Creating MODIFY input for existing employee:', {
                employeeId: employeeIdStr,
                payStubId: existingData.payStubId
            });
            const modifyDetails: ModifyPayStubInput['details'] = [];

            validEntries.forEach((entry) => {
                const existingDetail = existingData.existingDetails.get(entry.workDate);

                // Use nullable helper to handle CSV values properly
                // Only includes fields that are explicitly provided in the CSV
                const modifyDetail: ModifyPayStubInput['details'][0] = {
                    id: existingDetail?.id, // Include ID if updating existing detail
                    workDate: entry.workDate,
                    stHours: nullable(entry.stHours),
                    otHours: nullable(entry.otHours),
                    dtHours: nullable(entry.dtHours),
                    agreementId: nullable(convertIdToNumber(entry.agreementId)),
                    classificationId: nullable(convertIdToNumber(entry.classificationId)),
                    subClassificationId: nullable(convertIdToNumber(entry.subClassificationId)),
                    earningsCode: nullable(entry.earningsCode),
                    jobCode: nullable(entry.jobCode),
                    costCenter: nullable(entry.costCenter),
                    hourlyRate: nullable(entry.hourlyRate),
                    bonus: nullable(entry.bonus),
                    expenses: nullable(entry.expenses)
                };

                modifyDetails.push(modifyDetail);
            });

            payStubsToModify.push({
                id: existingData.payStubId,
                employeeId: employeeIdStr,
                name: firstEntry.employeeName,
                details: modifyDetails
            });
        } else {
            // New employee - create new PayStub
            console.log('[CSV_UPLOAD_DEBUG] Creating ADD input for new employee:', employeeIdStr);
            const payStubId = generateClientTempId('paystub');
            const details = validEntries.map((entry) => createPayStubDetail(entry, payStubId));

            const stHours = details.reduce((sum, detail) => sum + (detail.stHours || 0), 0);
            const otHours = details.reduce((sum, detail) => sum + (detail.otHours || 0), 0);
            const dtHours = details.reduce((sum, detail) => sum + (detail.dtHours || 0), 0);
            const totalHours = stHours + otHours + dtHours;
            const bonus = details.reduce((sum, detail) => sum + (detail.bonus || 0), 0);
            const expenses = details.reduce((sum, detail) => sum + (detail.expenses || 0), 0);

            payStubsToAdd.push({
                id: payStubId,
                employeeId,
                name: firstEntry.employeeName || `Employee ${employeeId}`,
                stHours: stHours > 0 ? stHours : null,
                otHours: otHours > 0 ? otHours : null,
                dtHours: dtHours > 0 ? dtHours : null,
                totalHours: totalHours > 0 ? totalHours : null,
                bonus: bonus > 0 ? bonus : null,
                expenses: expenses > 0 ? expenses : null,
                details,
                expanded: false,
                delete: false,
                inError: false,
                errors: []
            });
        }
    });

    console.log('[CSV_UPLOAD_DEBUG] Final transform result:', {
        payStubsToAdd: payStubsToAdd.length,
        payStubsToModify: payStubsToModify.length,
        addedEmployeeIds: payStubsToAdd.map((p) => p.employeeId),
        modifiedEmployeeIds: payStubsToModify.map((p) => p.employeeId)
    });

    return { payStubsToAdd, payStubsToModify, conflicts, rowErrors };
};
