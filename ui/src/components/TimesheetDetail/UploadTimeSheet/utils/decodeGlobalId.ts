/**
 * Simple implementation of fromGlobalId for decoding Relay Global IDs
 * Based on the Relay Global ID spec: base64(type:id)
 */
function fromGlobalId(globalId: string): { type: string; id: string } {
    try {
        // Handle both browser (atob) and Node.js (Buffer) environments
        const decoded = typeof atob === 'function' 
            ? atob(globalId) 
            : Buffer.from(globalId, 'base64').toString('binary');
        
        const colonIndex = decoded.indexOf(':');
        if (colonIndex === -1) {
            throw new Error('Invalid Global ID format');
        }
        return {
            type: decoded.substring(0, colonIndex),
            id: decoded.substring(colonIndex + 1)
        };
    } catch (error) {
        throw new Error(`Failed to decode Global ID: ${error}`);
    }
}

/**
 * Decodes a Relay Global ID to extract the numeric ID
 * @param globalId The Relay Global ID string
 * @returns The numeric ID as a number or null if invalid
 */
export const decodeGlobalId = (globalId: string | null | undefined): number | null => {
    if (!globalId) {
        return null;
    }

    try {
        const { id } = fromGlobalId(globalId);
        const numericId = parseInt(id, 10);
        return isNaN(numericId) ? null : numericId;
    } catch (error) {
        console.warn('Failed to decode Global ID:', globalId, error);
        return null;
    }
};

/**
 * Safely converts an ID that might be either a Global ID or numeric ID to a number
 * @param id The ID to convert (can be string or number)
 * @returns The numeric ID as a number or null if invalid
 */
export const toNumericId = (id: string | number | null | undefined): number | null => {
    if (id === null || id === undefined) {
        return null;
    }

    // If it's already a number, return it
    if (typeof id === 'number') {
        return isNaN(id) ? null : id;
    }

    // If it's a string, try to parse as number first
    const directNumeric = parseInt(String(id), 10);
    if (!isNaN(directNumeric)) {
        return directNumeric;
    }

    // If that fails, try to decode as Global ID
    return decodeGlobalId(String(id));
};