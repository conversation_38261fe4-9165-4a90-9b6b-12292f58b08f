import { RawCsvRow, ProcessedTimesheetEntry, NormalizedCsvHeader } from '../types';

/**
 * Clamps a numeric value to a range of 0-24 hours
 * @param value The value to clamp
 * @returns The clamped value (0-24) or null if the input is null/undefined
 */
const clampHours = (value: number | null | undefined): number | null => {
    if (value === null || value === undefined) {
        return null;
    }
    return Math.max(0, Math.min(value, 24));
};

/**
 * Safely parses a date string and converts it to YYYY-MM-DD format
 * Avoids creating Date objects to prevent timezone-related issues
 * @param dateStr The date string to parse
 * @returns Date string in YYYY-MM-DD format or empty string if invalid
 */
const safeParseLocalDate = (dateStr: string): string => {
    if (!dateStr || typeof dateStr !== 'string') {
        return '';
    }

    const trimmedDateStr = dateStr.trim();

    // Parse YYYY-MM-DD format - already in the format we want
    const isoMatch = trimmedDateStr.match(/^(\d{4})-(\d{2})-(\d{2})$/);
    if (isoMatch) {
        const year = parseInt(isoMatch[1], 10);
        const month = parseInt(isoMatch[2], 10);
        const day = parseInt(isoMatch[3], 10);

        // Basic validation without creating Date objects
        if (year < 1900 || year > 2100) return '';
        if (month < 1 || month > 12) return '';
        if (day < 1 || day > 31) return '';

        // Return the string as-is since it's already in YYYY-MM-DD format
        return trimmedDateStr;
    }

    // Parse MM/DD/YYYY format and convert to YYYY-MM-DD
    const usMatch = trimmedDateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
    if (usMatch) {
        const month = parseInt(usMatch[1], 10);
        const day = parseInt(usMatch[2], 10);
        const year = parseInt(usMatch[3], 10);

        // Basic validation without creating Date objects
        if (year < 1900 || year > 2100) return '';
        if (month < 1 || month > 12) return '';
        if (day < 1 || day > 31) return '';

        // Format as YYYY-MM-DD
        const formattedMonth = String(month).padStart(2, '0');
        const formattedDay = String(day).padStart(2, '0');
        return `${year}-${formattedMonth}-${formattedDay}`;
    }

    // Invalid format
    return '';
};

/**
 * Converts raw CSV rows to processed timesheet entries
 * @param validRows Array of validated raw CSV rows
 * @returns Array of processed timesheet entries
 */
export const convertRawToProcessed = (validRows: RawCsvRow[]): ProcessedTimesheetEntry[] => {
    return validRows.map((row, index) => {
        // Parse hours values
        const stHours = row[NormalizedCsvHeader.STHOURS] ? parseFloat(row[NormalizedCsvHeader.STHOURS]) : null;
        const otHours = row[NormalizedCsvHeader.OTHOURS] ? parseFloat(row[NormalizedCsvHeader.OTHOURS]) : null;
        const dtHours = row[NormalizedCsvHeader.DTHOURS] ? parseFloat(row[NormalizedCsvHeader.DTHOURS]) : null;

        return {
            // Employee identification fields
            employeeId: null, // Will be populated in transformation phase
            employeeName: undefined,
            externalEmployeeId: row[NormalizedCsvHeader.EXTERNALEMPLOYEEID],
            ssn: row[NormalizedCsvHeader.SSN],

            // Classification data
            agreementId: null, // Will be populated in transformation phase
            agreementName: row[NormalizedCsvHeader.AGREEMENT],
            classificationId: null,
            classificationName: row[NormalizedCsvHeader.CLASSIFICATION],
            subClassificationId: null,
            subClassificationName: row[NormalizedCsvHeader.SUBCLASSIFICATION],

            // Work date information
            workDate: safeParseLocalDate(row[NormalizedCsvHeader.DATE] || ''),
            day: row[NormalizedCsvHeader.DAY],

            // Financial data (convert strings to numbers with hour clamping)
            hourlyRate: row[NormalizedCsvHeader.HOURLYRATE] ? parseFloat(row[NormalizedCsvHeader.HOURLYRATE]) : null,
            jobCode: row[NormalizedCsvHeader.JOBCODE],
            earningsCode: row[NormalizedCsvHeader.EARNINGSCODE],
            costCenter: row[NormalizedCsvHeader.COSTCENTER],
            stHours: clampHours(stHours),
            otHours: clampHours(otHours),
            dtHours: clampHours(dtHours),
            bonus: row[NormalizedCsvHeader.DIRECTPAY] ? parseFloat(row[NormalizedCsvHeader.DIRECTPAY]) : null,
            expenses: row[NormalizedCsvHeader.EXPENSES] ? parseFloat(row[NormalizedCsvHeader.EXPENSES]) : null,

            // Metadata
            rowIndex: index + 2, // +1 for 0-based index, +1 for header row
            isValid: true,
            errors: []
        };
    });
};
