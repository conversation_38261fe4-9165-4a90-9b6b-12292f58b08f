/**
 * Utility functions for managing existing PayStub data during CSV upload
 * 
 * This module provides functionality to map existing PayStubs to avoid
 * creating duplicates when uploading CSV data for employees who already
 * have PayStubs in the timesheet.
 */

// Define minimal PayStub type needed for this utility
export interface PayStubData {
    readonly id: string;
    readonly employeeId: string;
    readonly details: ReadonlyArray<{
        readonly id: string;
        readonly workDate: any;
        readonly stHours: number | null | undefined;
        readonly otHours: number | null | undefined;
        readonly dtHours: number | null | undefined;
        readonly bonus: number | null | undefined;
        readonly expenses: number | null | undefined;
        readonly jobCode: string | null | undefined;
        readonly costCenter: string | null | undefined;
        readonly earningsCode: string | null | undefined;
        readonly agreementId: number | null | undefined;
        readonly classificationId: number | null | undefined;
        readonly subClassificationId: number | null | undefined;
        readonly hourlyRate: number | null | undefined;
    }>;
}

/**
 * PayStub detail structure from the timesheet
 */
export interface PayStubDetail {
    id: string;
    workDate: string;
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    bonus: number | null;
    expenses: number | null;
    jobCode?: string | null;
    costCenter?: string | null;
    earningsCode?: string | null;
    agreementId?: number | null;
    classificationId?: number | null;
    subClassificationId?: number | null;
    hourlyRate?: number | null;
}

/**
 * Represents the mapping of existing PayStub data for an employee
 */
export interface ExistingPayStubData {
    payStubId: string;
    existingDetails: Map<string, PayStubDetail>; // keyed by workDate
}

/**
 * Maps employee IDs to their existing PayStub data
 */
export interface ExistingPayStubMap {
    [employeeId: string]: ExistingPayStubData;
}

/**
 * Builds a map of existing PayStubs from the current timesheet
 * 
 * This function creates a lookup structure that allows efficient checking
 * of whether an employee already has a PayStub in the timesheet, and if so,
 * what detail dates already have data.
 * 
 * @param payStubs - Array of PayStub data from the timesheet
 * @returns Map of employee IDs to their existing PayStub data
 */
export function buildExistingPayStubMap(payStubs: ReadonlyArray<PayStubData>): ExistingPayStubMap {
    const map: ExistingPayStubMap = {};
    
    console.log('[CSV_UPLOAD_DEBUG] buildExistingPayStubMap called with', payStubs.length, 'payStubs');
    
    payStubs.forEach((payStub, index) => {
        console.log(`[CSV_UPLOAD_DEBUG] Processing payStub[${index}]:`, {
            id: payStub.id,
            employeeId: payStub.employeeId,
            detailsCount: payStub.details?.length || 0
        });
        
        if (!payStub.employeeId) {
            console.log(`[CSV_UPLOAD_DEBUG] Skipping payStub[${index}] - no employeeId`);
            return; // Skip PayStubs without employee IDs
        }

        const detailsMap = new Map<string, PayStubDetail>();
        
        if (payStub.details && Array.isArray(payStub.details)) {
            payStub.details.forEach((detail, detailIndex) => {
                if (!detail.workDate) {
                    console.log(`[CSV_UPLOAD_DEBUG] Skipping detail[${detailIndex}] - no workDate`);
                    return; // Skip details without work dates
                }
                
                detailsMap.set(detail.workDate, {
                    id: detail.id,
                    workDate: detail.workDate,
                    stHours: detail.stHours ?? null,
                    otHours: detail.otHours ?? null,
                    dtHours: detail.dtHours ?? null,
                    bonus: detail.bonus ?? null,
                    expenses: detail.expenses ?? null,
                    jobCode: detail.jobCode ?? null,
                    costCenter: detail.costCenter ?? null,
                    earningsCode: detail.earningsCode ?? null,
                    agreementId: detail.agreementId ?? null,
                    classificationId: detail.classificationId ?? null,
                    subClassificationId: detail.subClassificationId ?? null,
                    hourlyRate: detail.hourlyRate ?? null
                });
            });
        }
        
        const mapKey = String(payStub.employeeId);
        map[mapKey] = {
            payStubId: payStub.id,
            existingDetails: detailsMap
        };
        
        console.log(`[CSV_UPLOAD_DEBUG] Added to map with key '${mapKey}':`, {
            payStubId: payStub.id,
            detailsCount: detailsMap.size
        });
    });
    
    console.log('[CSV_UPLOAD_DEBUG] Final map keys:', Object.keys(map));
    console.log('[CSV_UPLOAD_DEBUG] Final map:', map);
    
    return map;
}

/**
 * Checks if a PayStub detail has meaningful data (non-zero hours or other values)
 * 
 * @param detail - The PayStub detail to check
 * @returns true if the detail has meaningful data, false otherwise
 */
export function hasDetailMeaningfulData(detail: PayStubDetail): boolean {
    return (
        (detail.stHours ?? 0) > 0 ||
        (detail.otHours ?? 0) > 0 ||
        (detail.dtHours ?? 0) > 0 ||
        (detail.bonus ?? 0) > 0 ||
        (detail.expenses ?? 0) > 0 ||
        !!detail.jobCode ||
        !!detail.costCenter ||
        !!detail.earningsCode
    );
}

/**
 * Conflict information for when CSV data would overwrite existing PayStub details
 */
export interface ConflictInfo {
    employeeId: string;
    employeeName: string;
    workDate: string;
    existingHours: {
        st: number | null;
        ot: number | null;
        dt: number | null;
    };
    newHours: {
        st: number | null;
        ot: number | null;
        dt: number | null;
    };
    existingBonus: number | null;
    newBonus: number | null;
    existingExpenses: number | null;
    newExpenses: number | null;
}

/**
 * Detects conflicts between CSV data and existing PayStub details
 * 
 * @param employeeId - The employee ID
 * @param employeeName - The employee name for display
 * @param workDate - The work date to check
 * @param newData - The new data from CSV
 * @param existingDetail - The existing PayStub detail
 * @returns ConflictInfo if there's a conflict, null otherwise
 */
export function detectConflict(
    employeeId: string,
    employeeName: string,
    workDate: string,
    newData: {
        stHours: number | null;
        otHours: number | null;
        dtHours: number | null;
        bonus: number | null;
        expenses: number | null;
    },
    existingDetail: PayStubDetail
): ConflictInfo | null {
    // Only consider it a conflict if the existing detail has meaningful data
    if (!hasDetailMeaningfulData(existingDetail)) {
        return null;
    }
    
    // Check if any hours, bonus, or expenses would be overwritten
    const hasConflict = 
        ((existingDetail.stHours ?? 0) > 0 && (newData.stHours ?? 0) > 0) ||
        ((existingDetail.otHours ?? 0) > 0 && (newData.otHours ?? 0) > 0) ||
        ((existingDetail.dtHours ?? 0) > 0 && (newData.dtHours ?? 0) > 0) ||
        ((existingDetail.bonus ?? 0) > 0 && (newData.bonus ?? 0) > 0) ||
        ((existingDetail.expenses ?? 0) > 0 && (newData.expenses ?? 0) > 0);
    
    if (!hasConflict) {
        return null;
    }
    
    return {
        employeeId,
        employeeName,
        workDate,
        existingHours: {
            st: existingDetail.stHours,
            ot: existingDetail.otHours,
            dt: existingDetail.dtHours
        },
        newHours: {
            st: newData.stHours,
            ot: newData.otHours,
            dt: newData.dtHours
        },
        existingBonus: existingDetail.bonus,
        newBonus: newData.bonus,
        existingExpenses: existingDetail.expenses,
        newExpenses: newData.expenses
    };
}