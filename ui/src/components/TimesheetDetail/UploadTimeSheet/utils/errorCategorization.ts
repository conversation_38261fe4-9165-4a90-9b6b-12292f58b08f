/**
 * Enhanced Error Categorization and Formatting Utilities
 *
 * This module provides functions to categorize and format validation errors
 * for better user experience in the upload error dialog.
 */

import { RowError, HeaderError, FileError, ProcessingError } from '../types';
import { getPayPeriodDateRange } from './dataValidation';

/**
 * Error category interface for structured error display
 */
export interface ErrorCategory {
    category: string;
    summary: string;
    context?: string;
    details: string[];
    instructions?: string[];
    errorCount: number;
}

/**
 * Categorizes row errors by their error codes for better display
 */
export const categorizeRowErrors = (errors: RowError[]): Record<string, RowError[]> => {
    const categories: Record<string, RowError[]> = {
        dateValidation: [],
        missingRequired: [],
        invalidFormat: [],
        invalidValue: [],
        employeeNotFound: [],
        agreementNotFound: [],
        classificationNotFound: [],
        subClassificationNotFound: [],
        duplicateEntry: [],
        unknown: []
    };

    errors.forEach((error) => {
        switch (error.code) {
            case 'OUTSIDE_PAY_PERIOD':
                categories.dateValidation.push(error);
                break;
            case 'MISSING_REQUIRED':
                categories.missingRequired.push(error);
                break;
            case 'INVALID_FORMAT':
                categories.invalidFormat.push(error);
                break;
            case 'INVALID_VALUE':
                categories.invalidValue.push(error);
                break;
            case 'EMPLOYEE_NOT_FOUND':
                categories.employeeNotFound.push(error);
                break;
            case 'AGREEMENT_NOT_FOUND':
                categories.agreementNotFound.push(error);
                break;
            case 'CLASSIFICATION_NOT_FOUND':
                categories.classificationNotFound.push(error);
                break;
            case 'SUBCLASSIFICATION_NOT_FOUND':
                categories.subClassificationNotFound.push(error);
                break;
            case 'DUPLICATE_ENTRY':
                categories.duplicateEntry.push(error);
                break;
            default:
                categories.unknown.push(error);
                break;
        }
    });

    return categories;
};

/**
 * Formats date validation errors with pay period context
 */
export const formatDateValidationErrors = (errors: RowError[], payPeriodEndDate?: string): ErrorCategory => {
    const dateRange = payPeriodEndDate ? getPayPeriodDateRange(payPeriodEndDate) : 'unknown date range';

    // Create simplified row details without repeating pay period information
    const details = errors.map((error) => {
        return `• Row ${error.rowIndex}: Date is outside the pay period`;
    });

    return {
        category: 'Date Validation Errors',
        summary: `${errors.length} date validation error${errors.length > 1 ? 's' : ''} in your CSV file:`,
        context: `Pay Period: ${dateRange}`,
        details,
        instructions: [
            'Open your CSV file and check the "Date" column',
            'Update any dates outside the pay period range',
            'Save the file and try uploading again'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats missing required field errors
 */
export const formatMissingRequiredErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        if (error.columnName) {
            return `• Row ${error.rowIndex}: Missing required field "${error.columnName}"`;
        }
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Missing Required Fields',
        summary: `${errors.length} missing required field${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Check your CSV file for empty required columns',
            'Ensure all required fields have values',
            'Save the file and try uploading again'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats invalid format errors
 */
export const formatInvalidFormatErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        if (error.columnName) {
            return `• Row ${error.rowIndex}: Invalid format in "${error.columnName}" column`;
        }
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Invalid Format Errors',
        summary: `${errors.length} format error${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Check the format of dates, numbers, and other fields',
            'Ensure dates are in MM/DD/YYYY or YYYY-MM-DD format',
            "Ensure numbers don't have too many decimal places",
            'Save the file and try uploading again'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats invalid value errors
 */
export const formatInvalidValueErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        if (error.columnName) {
            return `• Row ${error.rowIndex}: Invalid value in "${error.columnName}" column`;
        }
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Invalid Value Errors',
        summary: `${errors.length} invalid value error${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Check that numeric fields contain valid numbers',
            'Ensure hours are between 0 and 24',
            'Ensure rates and amounts are positive numbers',
            'Save the file and try uploading again'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats employee not found errors
 */
export const formatEmployeeNotFoundErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Employee Not Found',
        summary: `${errors.length} employee matching error${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Check that Employee IDs or SSNs match your employee records',
            'Verify employee data is up to date',
            'Contact your administrator if employee data needs updating'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats agreement/classification not found errors
 */
export const formatClassificationErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Classification Errors',
        summary: `${errors.length} classification error${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Check that Agreement and Classification names match your records',
            'Verify classification data is up to date',
            'Contact your administrator if classification data needs updating'
        ],
        errorCount: errors.length
    };
};

/**
 * Formats duplicate entry errors
 */
export const formatDuplicateEntryErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    // Check types of duplicate errors
    const hasDuplicateDateInTimesheet = errors.some(error => 
        error.message.includes('already has a timesheet entry for this date')
    );
    const hasDuplicateDateInCSV = errors.some(error =>
        error.message.includes('already has an entry for this date in the CSV file')
    );

    // Customize instructions based on the type of duplicate errors
    let instructions: string[] = [];
    
    if (hasDuplicateDateInCSV && hasDuplicateDateInTimesheet) {
        instructions = [
            'Open your CSV file and check for:',
            '  - Employees with multiple entries for the same date within the CSV',
            '  - Employees with entries for dates they already have in the timesheet',
            'Remove duplicate entries or change the dates',
            'Save the file and try uploading again'
        ];
    } else if (hasDuplicateDateInCSV) {
        instructions = [
            'Open your CSV file and check for employees with multiple entries for the same date',
            'Each employee can only have one entry per work date',
            'Remove duplicate entries or change the dates',
            'Save the file and try uploading again'
        ];
    } else if (hasDuplicateDateInTimesheet) {
        instructions = [
            'Open your CSV file and check the "Date" column',
            'Remove rows where employees already have entries for those dates in the timesheet',
            'Alternatively, update the dates to different work days',
            'Save the file and try uploading again'
        ];
    } else {
        instructions = [
            'Check for duplicate entries in your CSV file',
            'Remove duplicate rows',
            'Save the file and try uploading again'
        ];
    }

    // Customize the context message based on error types
    let context: string | undefined;
    if (hasDuplicateDateInCSV && hasDuplicateDateInTimesheet) {
        context = 'Found duplicate date entries both within your CSV and against existing timesheet data.';
    } else if (hasDuplicateDateInCSV) {
        context = 'Some employees have multiple entries for the same date within your CSV file.';
    } else if (hasDuplicateDateInTimesheet) {
        context = 'Some employees already have timesheet entries for the dates in your CSV file.';
    }

    return {
        category: 'Duplicate Entries',
        summary: `${errors.length} duplicate entry error${errors.length > 1 ? 's' : ''} in your CSV file`,
        context,
        details,
        instructions,
        errorCount: errors.length
    };
};

/**
 * Formats unknown errors
 */
export const formatUnknownErrors = (errors: RowError[]): ErrorCategory => {
    const details = errors.map((error) => {
        return `• Row ${error.rowIndex}: ${error.message}`;
    });

    return {
        category: 'Other Errors',
        summary: `${errors.length} other error${errors.length > 1 ? 's' : ''}:`,
        details,
        instructions: [
            'Review the error details below',
            'Check your CSV file for any obvious issues',
            'Contact support if the problem persists'
        ],
        errorCount: errors.length
    };
};

/**
 * Processes all row errors and returns categorized, formatted error categories
 */
export const processRowErrors = (errors: RowError[], payPeriodEndDate?: string): ErrorCategory[] => {
    const categories = categorizeRowErrors(errors);
    const errorCategories: ErrorCategory[] = [];

    // Process each category in order of importance
    if (categories.dateValidation.length > 0) {
        errorCategories.push(formatDateValidationErrors(categories.dateValidation, payPeriodEndDate));
    }

    if (categories.missingRequired.length > 0) {
        errorCategories.push(formatMissingRequiredErrors(categories.missingRequired));
    }

    if (categories.invalidFormat.length > 0) {
        errorCategories.push(formatInvalidFormatErrors(categories.invalidFormat));
    }

    if (categories.invalidValue.length > 0) {
        errorCategories.push(formatInvalidValueErrors(categories.invalidValue));
    }

    if (categories.employeeNotFound.length > 0) {
        errorCategories.push(formatEmployeeNotFoundErrors(categories.employeeNotFound));
    }

    if (
        categories.agreementNotFound.length > 0 ||
        categories.classificationNotFound.length > 0 ||
        categories.subClassificationNotFound.length > 0
    ) {
        const allClassificationErrors = [
            ...categories.agreementNotFound,
            ...categories.classificationNotFound,
            ...categories.subClassificationNotFound
        ];
        errorCategories.push(formatClassificationErrors(allClassificationErrors));
    }

    if (categories.duplicateEntry.length > 0) {
        errorCategories.push(formatDuplicateEntryErrors(categories.duplicateEntry));
    }

    if (categories.unknown.length > 0) {
        errorCategories.push(formatUnknownErrors(categories.unknown));
    }

    return errorCategories;
};

/**
 * Processes all validation errors (file, header, row) and returns structured data
 */
export const processAllErrors = (
    errors: ProcessingError[],
    payPeriodEndDate?: string
): {
    fileErrors: FileError[];
    headerErrors: HeaderError[];
    rowErrorCategories: ErrorCategory[];
    totalErrorCount: number;
} => {
    const fileErrors = errors.filter((error): error is FileError => error.type === 'file');
    const headerErrors = errors.filter((error): error is HeaderError => error.type === 'header');
    const rowErrors = errors.filter((error): error is RowError => error.type === 'row');

    const rowErrorCategories = processRowErrors(rowErrors, payPeriodEndDate);
    const totalErrorCount = fileErrors.length + headerErrors.length + rowErrors.length;

    return {
        fileErrors,
        headerErrors,
        rowErrorCategories,
        totalErrorCount
    };
};
