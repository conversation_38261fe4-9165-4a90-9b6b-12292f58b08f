import { ProcessedTimesheetEntry, PayStubUpload } from '../types';
import { toNumericId } from './decodeGlobalId';
import type { EmployeeUI, AgreementUI } from '@/src/types/relay-ui-extensions';

/**
 * Relay-compatible PayStub input format for bulk uploads
 * This matches the structure expected by the bulk upload mutation
 */
export interface RelayPayStubInput {
    employeeId: string; // Global ID string
    name?: string;
    details: Array<{
        workDate: string;
        stHours?: number;
        otHours?: number;
        dtHours?: number;
        agreementId?: number;
        classificationId?: number;
        subClassificationId?: number;
        earningsCode?: string;
        jobCode?: string;
        costCenter?: string;
        hourlyRate?: number;
        bonus?: number;
        expenses?: number;
    }>;
}

/**
 * Safely converts a value to string, handling null/undefined cases
 */
function safeConvertToString(value: unknown): string | undefined {
    if (value === null || value === undefined) return undefined;
    if (typeof value === 'string') return value;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);

    try {
        return String(value);
    } catch (error) {
        console.warn('Failed to convert value to string:', value, error);
        return undefined;
    }
}

/**
 * Safely converts a value to number, handling null/undefined and invalid cases
 */
function safeConvertToNumber(value: unknown): number | null {
    if (value === null || value === undefined) return null;
    if (typeof value === 'number') return isNaN(value) ? null : value;
    if (typeof value === 'string') {
        if (value.trim() === '') return null;
        const parsed = parseFloat(value.replace(/[,\s]/g, '')); // Remove commas and spaces
        return isNaN(parsed) ? null : parsed;
    }
    return null;
}

/**
 * Matches an employee from context using either Employee ID or SSN
 * Priority: Employee ID first, then SSN
 */
function matchEmployeeFromContext(entry: ProcessedTimesheetEntry, employees: EmployeeUI[]): EmployeeUI | null {
    if (!employees || employees.length === 0) {
        return null;
    }

    // Try Employee ID first
    if (entry.externalEmployeeId) {
        const entryIdString = safeConvertToString(entry.externalEmployeeId);
        if (entryIdString) {
            const matchedById = employees.find((employee) => {
                const employeeIdString = safeConvertToString(employee.externalEmployeeId);
                return employeeIdString && employeeIdString.trim() === entryIdString.trim();
            });
            if (matchedById) return matchedById;
        }
    }

    // Then try SSN
    if (entry.ssn) {
        const entrySSN = typeof entry.ssn === 'string' ? entry.ssn : entry.ssn[0];
        const normalizedEntrySSN = entrySSN ? entrySSN.replace(/[^0-9]/g, '') : ''; // Remove non-digits
        if (normalizedEntrySSN) {
            const matchedBySSN = employees.find((employee) => {
                if (!employee.ssn) return false;
                const employeeSSN = typeof employee.ssn === 'string' ? employee.ssn : employee.ssn[0];
                const normalizedEmployeeSSN = employeeSSN ? employeeSSN.replace(/[^0-9]/g, '') : '';
                return normalizedEmployeeSSN === normalizedEntrySSN;
            });
            if (matchedBySSN) return matchedBySSN;
        }
    }

    return null;
}

/**
 * Matches an agreement from context using Map-based O(1) lookup
 */
function matchAgreementFromContext(agreementName: string | undefined | null, agreements: AgreementUI[]): AgreementUI | null {
    if (!agreementName || !agreements || agreements.length === 0) {
        return null;
    }

    // Case-insensitive match using normalized name
    const normalizedName = agreementName.trim().toLowerCase();
    return agreements.find((agreement) => agreement.name.trim().toLowerCase() === normalizedName) || null;
}

/**
 * Converts PayStubUpload objects from context data to Relay input format
 * This function takes processed data from transformValidatedDataWithContext
 * and converts it to the format expected by the bulkAddPayStubs mutation
 *
 * NOTE: This function uses a "fail-fast" error handling strategy by throwing
 * errors immediately. This is appropriate since it's a final conversion step
 * that should only receive validated data. If validation errors are expected,
 * use transformValidatedDataWithContext first to collect rowErrors.
 *
 * @param payStubs Array of PayStubUpload objects from context transformation
 * @returns Array of RelayPayStubInput objects ready for GraphQL mutation
 * @throws Error if any PayStub has invalid data (e.g., missing employee ID)
 */
export const convertContextToRelayPayStubInput = (payStubs: PayStubUpload[]): RelayPayStubInput[] => {
    return payStubs.map((payStub, index) => {
        const employeeId = safeConvertToString(payStub.employeeId);

        if (!employeeId) {
            throw new Error(
                `Invalid employee ID found for PayStub at index ${index}. PayStub name: "${payStub.name}". This indicates the PayStub was not properly validated before conversion.`
            );
        }

        const result = {
            employeeId,
            name: payStub.name,
            details: payStub.details.map((detail) => ({
                workDate: detail.workDate,
                stHours: safeConvertToNumber(detail.stHours) || undefined,
                otHours: safeConvertToNumber(detail.otHours) || undefined,
                dtHours: safeConvertToNumber(detail.dtHours) || undefined,
                agreementId: detail.agreementId ? toNumericId(detail.agreementId) || undefined : undefined,
                classificationId: detail.classificationId ? toNumericId(detail.classificationId) || undefined : undefined,
                subClassificationId: detail.subClassificationId ? toNumericId(detail.subClassificationId) || undefined : undefined,
                earningsCode: detail.earningsCode ? safeConvertToString(detail.earningsCode) : undefined,
                jobCode: detail.jobCode ? safeConvertToString(detail.jobCode) : undefined,
                costCenter: detail.costCenter ? safeConvertToString(detail.costCenter) : undefined,
                hourlyRate: safeConvertToNumber(detail.hourlyRate) || undefined,
                bonus: safeConvertToNumber(detail.bonus) || undefined,
                expenses: safeConvertToNumber(detail.expenses) || undefined
            }))
        };

        return result;
    });
};
