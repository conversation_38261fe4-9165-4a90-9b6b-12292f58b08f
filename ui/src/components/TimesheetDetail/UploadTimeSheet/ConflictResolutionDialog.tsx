/**
 * Conflict Resolution Dialog Component
 * 
 * This dialog is displayed when CSV upload would overwrite existing PayStub details.
 * It allows users to choose how to handle conflicts:
 * - Overwrite all conflicting data
 * - Skip conflicting rows
 * - Cancel the entire upload
 */

import React, { useMemo } from 'react';
import {
    Dialog,
    Heading,
    Content,
    Divider,
    Header,
    ButtonGroup,
    Button,
    Text,
    View,
    Flex,
    Well
} from '@adobe/react-spectrum';
import type { ConflictInfo } from './utils/existingPayStubMap';

/**
 * Props for ConflictResolutionDialog component
 */
interface ConflictResolutionDialogProps {
    /** Array of conflicts detected during CSV upload */
    conflicts: ConflictInfo[];
    /** Callback when user resolves conflicts */
    onResolve: (resolution: 'overwrite' | 'skip' | 'cancel') => void;
    /** Whether the dialog is open */
    isOpen: boolean;
    /** Callback to close the dialog (used for cancel) */
    onClose: () => void;
}

/**
 * Formats hours for display
 */
const formatHours = (hours: number | null): string => {
    if (hours === null || hours === 0) return '0';
    return hours.toFixed(2);
};

/**
 * Formats currency for display
 */
const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === 0) return '$0.00';
    return `$${amount.toFixed(2)}`;
};

/**
 * ConflictResolutionDialog Component
 * 
 * Displays conflicts between CSV data and existing timesheet data,
 * allowing users to decide how to proceed.
 */
const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
    conflicts,
    onResolve,
    isOpen,
    onClose
}) => {
    // Group conflicts by employee for better display
    const groupedConflicts = useMemo(() => {
        const groups = new Map<string, ConflictInfo[]>();
        
        conflicts.forEach(conflict => {
            const existing = groups.get(conflict.employeeId) || [];
            existing.push(conflict);
            groups.set(conflict.employeeId, existing);
        });
        
        return Array.from(groups.entries()).map(([employeeId, employeeConflicts]) => ({
            employeeId,
            employeeName: employeeConflicts[0].employeeName,
            conflicts: employeeConflicts.sort((a, b) => 
                a.workDate.localeCompare(b.workDate)
            )
        }));
    }, [conflicts]);

    const handleOverwrite = () => {
        onResolve('overwrite');
        onClose();
    };

    const handleSkip = () => {
        onResolve('skip');
        onClose();
    };

    const handleCancel = () => {
        onResolve('cancel');
        onClose();
    };

    return isOpen ? (
        <Dialog size="L">
            <Heading>Conflicts Found</Heading>
            <Header>CSV Upload Conflicts</Header>
            <Divider />
                    <Content>
                        <View paddingBottom="size-200">
                            <Text>
                                Some employees already have hours recorded for these dates. 
                                How would you like to proceed?
                            </Text>
                        </View>

                        <View maxHeight="size-4600" overflow="auto">
                            {groupedConflicts.map(({ employeeName, conflicts }) => (
                                <View key={employeeName} marginBottom="size-300">
                                    <Heading level={4} marginBottom="size-100">
                                        {employeeName}
                                    </Heading>
                                    
                                    {conflicts.map((conflict) => (
                                        <Well key={conflict.workDate} marginBottom="size-100">
                                            <Flex direction="column" gap="size-100">
                                                <Text>
                                                    <strong>Date: {conflict.workDate}</strong>
                                                </Text>
                                                
                                                <Flex direction="row" gap="size-400">
                                                    <View flex>
                                                        <Text UNSAFE_style={{ color: 'var(--spectrum-semantic-negative-color-default)' }}>
                                                            <strong>Existing:</strong>
                                                        </Text>
                                                        <View marginStart="size-100">
                                                            {(conflict.existingHours.st || 0) > 0 && (
                                                                <Text>ST: {formatHours(conflict.existingHours.st)}</Text>
                                                            )}
                                                            {(conflict.existingHours.ot || 0) > 0 && (
                                                                <Text>OT: {formatHours(conflict.existingHours.ot)}</Text>
                                                            )}
                                                            {(conflict.existingHours.dt || 0) > 0 && (
                                                                <Text>DT: {formatHours(conflict.existingHours.dt)}</Text>
                                                            )}
                                                            {(conflict.existingBonus || 0) > 0 && (
                                                                <Text>Direct Pay: {formatCurrency(conflict.existingBonus)}</Text>
                                                            )}
                                                            {(conflict.existingExpenses || 0) > 0 && (
                                                                <Text>Expenses: {formatCurrency(conflict.existingExpenses)}</Text>
                                                            )}
                                                        </View>
                                                    </View>
                                                    
                                                    <View flex>
                                                        <Text UNSAFE_style={{ color: 'var(--spectrum-semantic-positive-color-default)' }}>
                                                            <strong>New:</strong>
                                                        </Text>
                                                        <View marginStart="size-100">
                                                            {(conflict.newHours.st || 0) > 0 && (
                                                                <Text>ST: {formatHours(conflict.newHours.st)}</Text>
                                                            )}
                                                            {(conflict.newHours.ot || 0) > 0 && (
                                                                <Text>OT: {formatHours(conflict.newHours.ot)}</Text>
                                                            )}
                                                            {(conflict.newHours.dt || 0) > 0 && (
                                                                <Text>DT: {formatHours(conflict.newHours.dt)}</Text>
                                                            )}
                                                            {(conflict.newBonus || 0) > 0 && (
                                                                <Text>Direct Pay: {formatCurrency(conflict.newBonus)}</Text>
                                                            )}
                                                            {(conflict.newExpenses || 0) > 0 && (
                                                                <Text>Expenses: {formatCurrency(conflict.newExpenses)}</Text>
                                                            )}
                                                        </View>
                                                    </View>
                                                </Flex>
                                            </Flex>
                                        </Well>
                                    ))}
                                </View>
                            ))}
                        </View>

                        <Divider size="S" marginY="size-200" />

                        <ButtonGroup align="end">
                            <Button variant="secondary" onPress={handleCancel}>
                                Cancel Upload
                            </Button>
                            <Button variant="secondary" onPress={handleSkip}>
                                Skip Conflicts
                            </Button>
                            <Button variant="cta" onPress={handleOverwrite}>
                                Overwrite All
                            </Button>
                        </ButtonGroup>
                    </Content>
                </Dialog>
    ) : null;
};

export default ConflictResolutionDialog;