import React from 'react';
import { render, screen } from '@testing-library/react';
import { AgreementComboBoxCell } from '../AgreementComboBoxCell';
import { useAgreementsByEmployer } from '../../../../hooks/useAgreementsByEmployer';
import type { AgreementUI } from '../../../../types/relay-ui-extensions';

// Mock the useAgreementsByEmployer hook
jest.mock('../../../../hooks/useAgreementsByEmployer');

const mockUseAgreementsByEmployer = useAgreementsByEmployer as jest.MockedFunction<typeof useAgreementsByEmployer>;

describe('AgreementComboBoxCell', () => {
  const mockAgreements: AgreementUI[] = [
    { id: '1', value: '1', text: 'Agreement 1', name: 'Agreement 1' },
    { id: '2', value: '2', text: 'Agreement 2', name: 'Agreement 2' },
  ];

  const defaultProps = {
    value: null,
    onChange: jest.fn(),
    employerGuid: 'test-employer-guid',
    isDisabled: false,
    isQuiet: false,
    rowIndex: 0,
    colIndex: 0,
    isActive: false,
    isEditingActive: false,
    onActivate: jest.fn(),
    onDeactivate: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Early return guard for payStubEmployeeId', () => {
    it('should handle missing payStubEmployeeId correctly', () => {
      mockUseAgreementsByEmployer.mockReturnValue({
        agreements: mockAgreements,
        isLoading: false,
        hasError: false,
        errorMessage: undefined,
      });

      const { container } = render(
        <AgreementComboBoxCell
          {...defaultProps}
          payStubEmployeeId={undefined}
        />
      );

      // Should render the component
      expect(container.firstChild).toBeInTheDocument();
      // Should not show agreement text when no employee is selected
      expect(screen.queryByText('Agreement 1')).not.toBeInTheDocument();
      expect(screen.queryByText('Agreement 2')).not.toBeInTheDocument();
    });

    it('should show agreements when payStubEmployeeId is provided', () => {
      mockUseAgreementsByEmployer.mockReturnValue({
        agreements: mockAgreements,
        isLoading: false,
        hasError: false,
        errorMessage: undefined,
      });

      render(
        <AgreementComboBoxCell
          {...defaultProps}
          payStubEmployeeId="employee-123"
          value="1"
        />
      );

      // Should show the selected agreement text
      expect(screen.getByText('Agreement 1')).toBeInTheDocument();
    });
  });

  describe('Regression test for invalid selections', () => {
    it('should prevent selection when payStubEmployeeId is missing', () => {
      mockUseAgreementsByEmployer.mockReturnValue({
        agreements: mockAgreements,
        isLoading: false,
        hasError: false,
        errorMessage: undefined,
      });

      render(
        <AgreementComboBoxCell
          {...defaultProps}
          payStubEmployeeId={undefined}
          isEditingActive={true}
        />
      );

      // ComboBox should be disabled when no payStubEmployeeId
      const comboBox = screen.getByRole('button', { name: 'Agreement Picker' });
      expect(comboBox).toHaveAttribute('aria-disabled', 'true');
    });

    it('should allow selection when payStubEmployeeId is present', () => {
      mockUseAgreementsByEmployer.mockReturnValue({
        agreements: mockAgreements,
        isLoading: false,
        hasError: false,
        errorMessage: undefined,
      });

      render(
        <AgreementComboBoxCell
          {...defaultProps}
          payStubEmployeeId="employee-123"
          isEditingActive={true}
        />
      );

      // ComboBox should be enabled when payStubEmployeeId is present
      const comboBox = screen.getByRole('button', { name: 'Agreement Picker' });
      expect(comboBox).not.toHaveAttribute('aria-disabled', 'true');
    });
  });
});