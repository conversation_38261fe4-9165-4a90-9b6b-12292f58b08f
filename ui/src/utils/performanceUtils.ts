/**
 * Performance Utilities
 * 
 * Utilities to prevent memory leaks, optimize performance, and detect issues.
 * Addresses O(n²) complexity issues and provides efficient data structures.
 */

import { useMemo, useCallback, useRef, useEffect, useState } from 'react';

/**
 * Memory-efficient Map implementation with automatic cleanup
 */
export class SafeMap<K, V> extends Map<K, V> {
    private maxSize: number;
    private accessOrder: K[] = [];
    
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }
    
    set(key: K, value: V): this {
        // Update access order
        const existingIndex = this.accessOrder.indexOf(key);
        if (existingIndex !== -1) {
            this.accessOrder.splice(existingIndex, 1);
        }
        this.accessOrder.push(key);
        
        // Set the value
        super.set(key, value);
        
        // Cleanup if needed
        this.cleanup();
        
        return this;
    }
    
    get(key: K): V | undefined {
        const value = super.get(key);
        if (value !== undefined) {
            // Update access order
            const existingIndex = this.accessOrder.indexOf(key);
            if (existingIndex !== -1) {
                this.accessOrder.splice(existingIndex, 1);
                this.accessOrder.push(key);
            }
        }
        return value;
    }
    
    delete(key: K): boolean {
        const result = super.delete(key);
        const index = this.accessOrder.indexOf(key);
        if (index !== -1) {
            this.accessOrder.splice(index, 1);
        }
        return result;
    }
    
    private cleanup(): void {
        while (this.size > this.maxSize) {
            const oldestKey = this.accessOrder.shift();
            if (oldestKey !== undefined) {
                super.delete(oldestKey);
            }
        }
    }
    
    getStats(): { size: number; maxSize: number; accessOrderLength: number } {
        return {
            size: this.size,
            maxSize: this.maxSize,
            accessOrderLength: this.accessOrder.length
        };
    }
}

/**
 * Debounced function utility to prevent excessive calls
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
): (...args: Parameters<T>) => void {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    
    return function executedFunction(...args: Parameters<T>) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(null, args);
        };
        
        const callNow = immediate && !timeout;
        
        if (timeout) {
            clearTimeout(timeout);
        }
        
        timeout = setTimeout(later, wait);
        
        if (callNow) {
            func.apply(null, args);
        }
    };
}

/**
 * Throttle function to limit execution frequency
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean = false;
    
    return function executedFunction(...args: Parameters<T>) {
        if (!inThrottle) {
            func.apply(null, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Memoization utility with size limit to prevent memory leaks
 */
export function createMemoizer<Args extends readonly unknown[], Return>(
    fn: (...args: Args) => Return,
    maxCacheSize = 100,
    keyGenerator?: (...args: Args) => string
): (...args: Args) => Return {
    const cache = new SafeMap<string, Return>(maxCacheSize);
    
    const generateKey = keyGenerator || ((...args: Args) => JSON.stringify(args));
    
    return (...args: Args): Return => {
        const key = generateKey(...args);
        
        if (cache.has(key)) {
            return cache.get(key)!;
        }
        
        const result = fn(...args);
        cache.set(key, result);
        
        return result;
    };
}

/**
 * Efficient array operations to prevent O(n²) complexity
 */
export class EfficientArray<T> {
    private items: T[] = [];
    private indexMap = new Map<T, number>();
    
    constructor(initialItems?: T[]) {
        if (initialItems) {
            this.items = [...initialItems];
            this.rebuildIndexMap();
        }
    }
    
    add(item: T): void {
        if (!this.indexMap.has(item)) {
            const index = this.items.length;
            this.items.push(item);
            this.indexMap.set(item, index);
        }
    }
    
    remove(item: T): boolean {
        const index = this.indexMap.get(item);
        if (index !== undefined) {
            this.items.splice(index, 1);
            this.rebuildIndexMap();
            return true;
        }
        return false;
    }
    
    has(item: T): boolean {
        return this.indexMap.has(item);
    }
    
    getItems(): readonly T[] {
        return this.items;
    }
    
    clear(): void {
        this.items = [];
        this.indexMap.clear();
    }
    
    size(): number {
        return this.items.length;
    }
    
    private rebuildIndexMap(): void {
        this.indexMap.clear();
        this.items.forEach((item, index) => {
            this.indexMap.set(item, index);
        });
    }
}

/**
 * React hook for stable memoization with dependency tracking
 */
export function useStableMemo<T>(
    factory: () => T,
    deps: React.DependencyList,
    maxCacheSize = 10
): T {
    const cache = useRef(new SafeMap<string, T>(maxCacheSize));
    const depsRef = useRef<React.DependencyList | undefined>(undefined);
    
    return useMemo(() => {
        const key = JSON.stringify(deps);
        
        // Check if dependencies changed
        if (depsRef.current && JSON.stringify(depsRef.current) === key) {
            const cached = cache.current.get(key);
            if (cached !== undefined) {
                return cached;
            }
        }
        
        const result = factory();
        cache.current.set(key, result);
        depsRef.current = deps;
        
        return result;
    }, deps);
}

// Extend Performance interface to include memory property
interface PerformanceWithMemory extends Performance {
    memory?: {
        usedJSHeapSize: number;
        totalJSHeapSize: number;
    };
}

/**
 * React hook for memory leak detection and cleanup
 */
export function useMemoryLeakDetection(componentName: string): void {
    const mountTime = useRef(Date.now());
    const lastMemoryCheck = useRef(Date.now());
    
    useEffect(() => {
        const checkMemory = () => {
            const now = Date.now();
            if (now - lastMemoryCheck.current > 10000) { // Check every 10 seconds
                lastMemoryCheck.current = now;
                
                if (typeof performance !== 'undefined' && 'memory' in performance) {
                    const perfWithMemory = performance as PerformanceWithMemory;
                    const memory = perfWithMemory.memory;
                    const memoryMB = memory?.usedJSHeapSize ? memory.usedJSHeapSize / 1024 / 1024 : 0;
                    
                    if (memoryMB > 100) { // Alert if over 100MB
                        console.warn(`[MEMORY] High memory usage detected in ${componentName}: ${memoryMB.toFixed(2)}MB`);
                    }
                    
                    // Check for potential memory leaks (rapid growth)
                    const timeAlive = now - mountTime.current;
                    const memoryGrowthRate = memoryMB / (timeAlive / 1000); // MB per second
                    
                    if (memoryGrowthRate > 1) { // More than 1MB per second growth
                        console.error(`[MEMORY_LEAK] Potential memory leak in ${componentName}: ${memoryGrowthRate.toFixed(2)}MB/s`);
                    }
                }
            }
        };
        
        const interval = setInterval(checkMemory, 5000);
        
        return () => {
            clearInterval(interval);
        };
    }, [componentName]);
}

/**
 * Optimized deep clone with circular reference detection
 */
export function safeDeepClone<T>(obj: T, maxDepth = 10): T {
    const seen = new WeakSet();
    
    function clone(item: any, depth: number): any {
        if (depth > maxDepth) {
            console.warn('[PERFORMANCE] Maximum clone depth exceeded, returning shallow copy');
            return item;
        }
        
        if (item === null || typeof item !== 'object') {
            return item;
        }
        
        if (seen.has(item)) {
            console.warn('[PERFORMANCE] Circular reference detected in deep clone');
            return null;
        }
        
        seen.add(item);
        
        if (Array.isArray(item)) {
            return item.map(element => clone(element, depth + 1));
        }
        
        if (item instanceof Date) {
            return new Date(item.getTime());
        }
        
        if (item instanceof Map) {
            const clonedMap = new Map();
            item.forEach((value, key) => {
                clonedMap.set(clone(key, depth + 1), clone(value, depth + 1));
            });
            return clonedMap;
        }
        
        if (item instanceof Set) {
            const clonedSet = new Set();
            item.forEach(value => {
                clonedSet.add(clone(value, depth + 1));
            });
            return clonedSet;
        }
        
        const clonedObj: any = {};
        Object.keys(item).forEach(key => {
            clonedObj[key] = clone(item[key], depth + 1);
        });
        
        return clonedObj;
    }
    
    return clone(obj, 0);
}

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
    private static measurements = new Map<string, number[]>();
    
    static startMeasurement(name: string): () => void {
        const startTime = performance.now();
        
        return () => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (!this.measurements.has(name)) {
                this.measurements.set(name, []);
            }
            
            const measurements = this.measurements.get(name)!;
            measurements.push(duration);
            
            // Keep only last 100 measurements
            if (measurements.length > 100) {
                measurements.shift();
            }
            
            // Log if performance is degrading
            if (measurements.length >= 10) {
                const average = measurements.reduce((a, b) => a + b, 0) / measurements.length;
                if (average > 100) { // More than 100ms average
                    console.warn(`[PERFORMANCE] Slow operation detected: ${name} (${average.toFixed(2)}ms average)`);
                }
            }
        };
    }
    
    static getStats(name: string): { count: number; average: number; max: number; min: number } | null {
        const measurements = this.measurements.get(name);
        if (!measurements || measurements.length === 0) {
            return null;
        }
        
        return {
            count: measurements.length,
            average: measurements.reduce((a, b) => a + b, 0) / measurements.length,
            max: Math.max(...measurements),
            min: Math.min(...measurements)
        };
    }
    
    static reset(name?: string): void {
        if (name) {
            this.measurements.delete(name);
        } else {
            this.measurements.clear();
        }
    }
}

/**
 * React hook for performance measurement
 */
export function usePerformanceMeasurement(name: string, deps: React.DependencyList): void {
    const endMeasurement = useRef<(() => void) | null>(null);
    
    useEffect(() => {
        endMeasurement.current = PerformanceMonitor.startMeasurement(name);
        
        return () => {
            if (endMeasurement.current) {
                endMeasurement.current();
                endMeasurement.current = null;
            }
        };
    }, deps);
}

/**
 * Efficient state merger to prevent unnecessary re-renders
 */
export function shallowMerge<T extends Record<string, any>>(prev: T, updates: Partial<T>): T {
    // Check if any values actually changed
    const hasChanges = Object.keys(updates).some(key => prev[key] !== updates[key]);
    
    if (!hasChanges) {
        return prev; // Return same reference to prevent re-renders
    }
    
    return { ...prev, ...updates };
}

/**
 * React hook for efficient state updates with shallow comparison
 */
export function useShallowState<T extends Record<string, any>>(
    initialState: T
): [T, (updates: Partial<T>) => void] {
    const [state, setState] = useState<T>(initialState);
    
    const updateState = useCallback((updates: Partial<T>) => {
        setState(prevState => shallowMerge(prevState, updates));
    }, []);
    
    return [state, updateState];
}