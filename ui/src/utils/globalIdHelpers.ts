/**
 * Phase 2.5: Enhanced Developer Experience Utilities
 * 
 * Comprehensive utility functions for working with Global IDs throughout the application.
 * Provides convenient helpers, debugging tools, and migration utilities for developers.
 */

import { RelayIdService } from '../services/RelayIdService';
import { GlobalID, GlobalIDGuards, GlobalIDUtils, AnyGlobalID } from '../types/GlobalID';
import { SafeIdConverter } from './safeIdConversion';

/**
 * Development and debugging utilities for Global IDs
 */
export class GlobalIdDevUtils {
  /**
   * Pretty-prints a Global ID with its decoded information
   * Useful for debugging and development
   */
  static inspect(globalId: string): string {
    if (!globalId) {
      return '🔍 [Empty ID]';
    }

    if (GlobalIDGuards.isClientTempID(globalId)) {
      return `🔍 [Client Temp] ${globalId}`;
    }

    if (GlobalIDGuards.isGlobalIDFormat(globalId)) {
      const entityType = GlobalIDUtils.getEntityType(globalId as AnyGlobalID);
      const numericId = GlobalIDUtils.getNumericId(globalId as AnyGlobalID);
      return `🔍 [${entityType}:${numericId}] encoded as "${globalId}"`;
    }

    return `🔍 [Invalid] "${globalId}" (not a valid Global ID)`;
  }

  /**
   * Analyzes an array of IDs and provides summary statistics
   */
  static analyzeIdArray(ids: string[]): {
    total: number;
    validGlobalIds: number;
    clientTempIds: number;
    invalidIds: number;
    entityTypes: Record<string, number>;
    examples: {
      validGlobalId?: string;
      clientTempId?: string;
      invalidId?: string;
    };
  } {
    const analysis = {
      total: ids.length,
      validGlobalIds: 0,
      clientTempIds: 0,
      invalidIds: 0,
      entityTypes: {} as Record<string, number>,
      examples: {} as any
    };

    for (const id of ids) {
      if (GlobalIDGuards.isClientTempID(id)) {
        analysis.clientTempIds++;
        if (!analysis.examples.clientTempId) {
          analysis.examples.clientTempId = id;
        }
        
        const entityType = 'ClientTemp';
        analysis.entityTypes[entityType] = (analysis.entityTypes[entityType] || 0) + 1;
      } else if (GlobalIDGuards.isGlobalIDFormat(id)) {
        analysis.validGlobalIds++;
        if (!analysis.examples.validGlobalId) {
          analysis.examples.validGlobalId = id;
        }
        
        const entityType = GlobalIDUtils.getEntityType(id as AnyGlobalID) || 'Unknown';
        analysis.entityTypes[entityType] = (analysis.entityTypes[entityType] || 0) + 1;
      } else {
        analysis.invalidIds++;
        if (!analysis.examples.invalidId) {
          analysis.examples.invalidId = id;
        }
      }
    }

    return analysis;
  }

  /**
   * Validates that an object's ID fields are properly formatted
   */
  static validateObjectIds(
    obj: Record<string, any>, 
    expectedIdFields: Array<{ field: string; entityType: string; required?: boolean }>
  ): {
    isValid: boolean;
    errors: Array<{ field: string; error: string; value: any }>;
  } {
    const errors: Array<{ field: string; error: string; value: any }> = [];

    for (const { field, entityType, required = true } of expectedIdFields) {
      const value = obj[field];

      // Check required fields
      if (required && (value === null || value === undefined || value === '')) {
        errors.push({
          field,
          error: `Required ${entityType} ID field is missing`,
          value
        });
        continue;
      }

      // Skip validation for optional empty fields
      if (!required && (value === null || value === undefined || value === '')) {
        continue;
      }

      // Validate ID format
      if (typeof value !== 'string') {
        errors.push({
          field,
          error: `${entityType} ID must be a string`,
          value
        });
        continue;
      }

      // Check if it's a valid Global ID format
      if (!GlobalIDGuards.isAnyGlobalID(value)) {
        errors.push({
          field,
          error: `Invalid Global ID format for ${entityType}`,
          value
        });
        continue;
      }

      // For server IDs, validate entity type matches
      if (GlobalIDGuards.isGlobalIDFormat(value)) {
        const actualType = GlobalIDUtils.getEntityType(value as AnyGlobalID);
        if (actualType !== entityType) {
          errors.push({
            field,
            error: `Expected ${entityType} ID, but found ${actualType}`,
            value
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Logs Global ID analysis to console for debugging
   */
  static logAnalysis(label: string, ids: string[]): void {
    console.group(`🔍 Global ID Analysis: ${label}`);
    
    const analysis = this.analyzeIdArray(ids);
    
    console.log(`📊 Summary: ${analysis.total} total IDs`);
    console.log(`✅ Valid Global IDs: ${analysis.validGlobalIds}`);
    console.log(`🔧 Client Temp IDs: ${analysis.clientTempIds}`);
    console.log(`❌ Invalid IDs: ${analysis.invalidIds}`);
    
    if (Object.keys(analysis.entityTypes).length > 0) {
      console.log('📋 Entity Types:');
      Object.entries(analysis.entityTypes).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`);
      });
    }
    
    if (Object.keys(analysis.examples).length > 0) {
      console.log('📝 Examples:');
      Object.entries(analysis.examples).forEach(([type, example]) => {
        console.log(`   ${type}: ${this.inspect(example)}`);
      });
    }
    
    console.groupEnd();
  }
}

/**
 * Migration utilities for transitioning between ID formats
 */
export class GlobalIdMigrationUtils {
  /**
   * Converts legacy numeric/GUID strings to proper Global IDs
   */
  static legacyToGlobalId(
    legacyId: string | number,
    entityType: 'PayStub' | 'PayStubDetail' | 'Employee' | 'TimeSheet'
  ): string {
    if (typeof legacyId === 'number') {
      return RelayIdService.toGlobalId(entityType, legacyId);
    }

    const stringId = String(legacyId);

    // If it's already a Global ID, return as-is
    if (GlobalIDGuards.isGlobalIDFormat(stringId)) {
      return stringId;
    }

    // If it looks like a GUID, convert it
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(stringId)) {
      // For legacy GUID support, we might need special handling
      console.warn(`Converting legacy GUID ${stringId} to Global ID. This should be rare in the new system.`);
      return RelayIdService.toGlobalId(entityType, stringId);
    }

    // If it looks like a number string, convert it
    if (/^\d+$/.test(stringId)) {
      return RelayIdService.toGlobalId(entityType, parseInt(stringId, 10));
    }

    // Otherwise, assume it's already in the correct format
    return stringId;
  }

  /**
   * Batch converts an array of legacy IDs to Global IDs
   */
  static batchLegacyToGlobalId(
    legacyIds: Array<string | number>,
    entityType: 'PayStub' | 'PayStubDetail' | 'Employee' | 'TimeSheet'
  ): string[] {
    return legacyIds.map(id => this.legacyToGlobalId(id, entityType));
  }

  /**
   * Safely updates an object's ID fields from legacy to Global ID format
   */
  static updateObjectIds<T extends Record<string, any>>(
    obj: T,
    fieldMappings: Array<{ field: keyof T; entityType: 'PayStub' | 'PayStubDetail' | 'Employee' | 'TimeSheet' }>
  ): T {
    const updated = { ...obj };

    for (const { field, entityType } of fieldMappings) {
      const currentValue = updated[field];
      if (currentValue !== null && currentValue !== undefined && currentValue !== '') {
        try {
          updated[field] = this.legacyToGlobalId(currentValue, entityType) as T[typeof field];
        } catch (error) {
          console.warn(`Failed to convert ${String(field)} from ${currentValue}:`, error);
          // Leave the original value if conversion fails
        }
      }
    }

    return updated;
  }
}

/**
 * Performance-optimized utilities for working with Global IDs
 */
export class GlobalIdPerformanceUtils {
  private static readonly entityTypeCache = new Map<string, string | null>();
  private static readonly numericIdCache = new Map<string, number | null>();

  /**
   * Cached entity type extraction (useful for repeated operations)
   */
  static getCachedEntityType(globalId: string): string | null {
    if (this.entityTypeCache.has(globalId)) {
      return this.entityTypeCache.get(globalId)!;
    }

    const entityType = GlobalIDUtils.getEntityType(globalId as AnyGlobalID);
    this.entityTypeCache.set(globalId, entityType);
    return entityType;
  }

  /**
   * Cached numeric ID extraction
   */
  static getCachedNumericId(globalId: string): number | null {
    if (this.numericIdCache.has(globalId)) {
      return this.numericIdCache.get(globalId)!;
    }

    const numericId = GlobalIDUtils.getNumericId(globalId as AnyGlobalID);
    this.numericIdCache.set(globalId, numericId);
    return numericId;
  }

  /**
   * Clears the internal caches (useful for testing or memory management)
   */
  static clearCaches(): void {
    this.entityTypeCache.clear();
    this.numericIdCache.clear();
  }

  /**
   * Groups Global IDs by entity type efficiently
   */
  static groupByEntityType(globalIds: string[]): Record<string, string[]> {
    const groups: Record<string, string[]> = {};

    for (const id of globalIds) {
      const entityType = this.getCachedEntityType(id) || 'Unknown';
      if (!groups[entityType]) {
        groups[entityType] = [];
      }
      groups[entityType].push(id);
    }

    return groups;
  }

  /**
   * Filters Global IDs by entity type efficiently
   */
  static filterByEntityType(globalIds: string[], entityType: string): string[] {
    return globalIds.filter(id => this.getCachedEntityType(id) === entityType);
  }
}

/**
 * React component helpers for Global ID handling
 */
export class GlobalIdReactUtils {
  /**
   * Generates stable React keys from Global IDs
   */
  static toReactKey(globalId: string, prefix?: string): string {
    const safeId = SafeIdConverter.tryToStringId(globalId) || 'unknown';
    return prefix ? `${prefix}-${safeId}` : safeId;
  }

  /**
   * Generates React keys for arrays of Global IDs
   */
  static toReactKeys(globalIds: string[], prefix?: string): string[] {
    return globalIds.map(id => this.toReactKey(id, prefix));
  }

  /**
   * Creates a stable sorting comparator for React list rendering
   */
  static createReactSortComparator(
    sortBy: 'entityType' | 'numericId' | 'string' = 'string'
  ): (a: string, b: string) => number {
    switch (sortBy) {
      case 'entityType':
        return (a, b) => {
          const typeA = GlobalIdPerformanceUtils.getCachedEntityType(a) || '';
          const typeB = GlobalIdPerformanceUtils.getCachedEntityType(b) || '';
          return typeA.localeCompare(typeB);
        };
      
      case 'numericId':
        return (a, b) => {
          const numA = GlobalIdPerformanceUtils.getCachedNumericId(a) || 0;
          const numB = GlobalIdPerformanceUtils.getCachedNumericId(b) || 0;
          return numA - numB;
        };
      
      case 'string':
      default:
        return (a, b) => a.localeCompare(b);
    }
  }

  /**
   * Validates props that should contain Global IDs
   */
  static validateGlobalIdProps(
    props: Record<string, any>,
    expectedFields: Array<{ field: string; entityType: string; required?: boolean }>
  ): {
    isValid: boolean;
    warnings: string[];
  } {
    const result = GlobalIdDevUtils.validateObjectIds(props, expectedFields);
    
    if (!result.isValid && process.env.NODE_ENV === 'development') {
      const warnings = result.errors.map(error => 
        `Invalid prop '${error.field}': ${error.error} (received: ${JSON.stringify(error.value)})`
      );
      
      return { isValid: false, warnings };
    }

    return { isValid: result.isValid, warnings: [] };
  }
}

/**
 * Convenient exports for common operations
 */
export const globalIdHelpers = {
  // Inspection and debugging
  inspect: GlobalIdDevUtils.inspect,
  analyze: GlobalIdDevUtils.analyzeIdArray,
  logAnalysis: GlobalIdDevUtils.logAnalysis,
  validate: GlobalIdDevUtils.validateObjectIds,

  // Migration utilities
  fromLegacy: GlobalIdMigrationUtils.legacyToGlobalId,
  batchFromLegacy: GlobalIdMigrationUtils.batchLegacyToGlobalId,
  updateObject: GlobalIdMigrationUtils.updateObjectIds,

  // Performance utilities
  groupByType: GlobalIdPerformanceUtils.groupByEntityType,
  filterByType: GlobalIdPerformanceUtils.filterByEntityType,
  clearCaches: GlobalIdPerformanceUtils.clearCaches,

  // React utilities
  toKey: GlobalIdReactUtils.toReactKey,
  toKeys: GlobalIdReactUtils.toReactKeys,
  createComparator: GlobalIdReactUtils.createReactSortComparator,
  validateProps: GlobalIdReactUtils.validateGlobalIdProps,

  // Type guards (re-exported for convenience)
  isValid: GlobalIDGuards.isAnyGlobalID,
  isClientTemp: GlobalIDGuards.isClientTempID,
  isServerGenerated: GlobalIDGuards.isGlobalIDFormat,

  // Utilities (re-exported for convenience)
  getType: GlobalIDUtils.getEntityType,
  getNumericId: GlobalIDUtils.getNumericId,
  areEqual: GlobalIDUtils.areEqual,
  sort: GlobalIDUtils.sortGlobalIds
};

// Default export for convenience
export default globalIdHelpers;