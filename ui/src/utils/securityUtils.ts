/**
 * Security Utilities
 * 
 * ## Overview
 * Comprehensive security utilities designed to protect against common web vulnerabilities
 * including XSS attacks, SQL injection, data exposure, and malicious input manipulation.
 * 
 * ## Security Features
 * 
 * ### Input Sanitization
 * - **XSS Prevention**: HTML encoding and dangerous character removal
 * - **Injection Prevention**: SQL injection pattern detection and blocking
 * - **Data Validation**: Comprehensive input validation with type checking
 * 
 * ### Error Handling
 * - **Message Sanitization**: Removes sensitive data from error messages
 * - **Stack Trace Protection**: Prevents information disclosure
 * - **Audit Logging**: Security event tracking and monitoring
 * 
 * ### Access Control
 * - **Rate Limiting**: Prevents abuse and brute force attacks
 * - **Authorization Validation**: Timesheet access control
 * - **Session Security**: Secure data comparison utilities
 * 
 * ### Cryptographic Functions
 * - **Secure ID Generation**: Cryptographically secure random IDs
 * - **Timing Attack Prevention**: Constant-time string comparison
 * - **CSP Integration**: Content Security Policy helpers
 * 
 * ## Security Best Practices
 * 
 * ### Always Sanitize User Input
 * ```typescript
 * // Wrong - direct usage
 * element.innerHTML = userInput;
 * 
 * // Correct - sanitized usage
 * element.innerHTML = sanitizeHTML(userInput);
 * ```
 * 
 * ### Validate All Data
 * ```typescript
 * if (validateForSQLInjection(userInput)) {
 *   // Safe to process
 *   processUserData(userInput);
 * } else {
 *   // Block malicious input
 *   logSecurityEvent('INJECTION_ATTEMPT', { input: userInput });
 * }
 * ```
 * 
 * ### Handle Errors Securely
 * ```typescript
 * try {
 *   await processData();
 * } catch (error) {
 *   // Don't expose sensitive details
 *   const safeMessage = sanitizeErrorMessage(error);
 *   showUserError(safeMessage);
 * }
 * ```
 * 
 * <AUTHOR> 3 - Initial security implementation
 * <AUTHOR> 5 - Enhanced documentation and additional security measures
 * @since 2.0.0
 * @version 2.1.0 - Comprehensive security utilities with full documentation
 */

/**
 * Encodes HTML-unsafe characters to prevent Cross-Site Scripting (XSS) attacks.
 * 
 * ## Security Purpose
 * This function neutralizes potentially dangerous HTML characters that could be used
 * in XSS attacks, making user input safe for display in web pages.
 * 
 * ## Character Encoding
 * - `&` → `&amp;` (prevents HTML entity interpretation)
 * - `<` → `&lt;` (prevents HTML tag opening)
 * - `>` → `&gt;` (prevents HTML tag closing)
 * - `"` → `&quot;` (prevents attribute injection)
 * - `'` → `&#x27;` (prevents single-quote attribute injection)
 * - `/` → `&#x2F;` (prevents tag closing injection)
 * 
 * ## Attack Prevention
 * Prevents common XSS attack vectors:
 * - Script tag injection: `<script>alert('xss')</script>`
 * - Event handler injection: `<img onerror="alert('xss')" src=x>`
 * - Style-based attacks: `<style>body{background:url(javascript:alert('xss'))}</style>`
 * 
 * @param input - String that may contain unsafe HTML characters
 * @returns HTML-safe encoded string ready for DOM insertion
 * 
 * @example
 * ```typescript
 * // Dangerous user input
 * const userInput = '<script>alert("XSS")</script>';
 * 
 * // Safe for display
 * const safeHTML = sanitizeHTML(userInput);
 * // Returns: '&lt;script&gt;alert(&quot;XSS&quot;)&lt;&#x2F;script&gt;'
 * 
 * // Safe DOM insertion
 * element.innerHTML = safeHTML;
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced XSS protection
 */
export function sanitizeHTML(input: string | null | undefined): string {
    if (!input || typeof input !== 'string') {
        return '';
    }
    
    return input
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
}

/**
 * Comprehensive user input sanitization for security and data integrity.
 * 
 * ## Multi-Layer Protection
 * This function provides comprehensive protection against various attack vectors:
 * 
 * ### Character-Based Attacks
 * - **HTML Injection**: Removes HTML-unsafe characters
 * - **Script Injection**: Blocks javascript:, data:, vbscript: protocols
 * - **Event Injection**: Removes event handler patterns (onclick, onload, etc.)
 * 
 * ### Protocol-Based Attacks
 * - **JavaScript Protocol**: Blocks `javascript:` URL scheme
 * - **Data Protocol**: Blocks `data:` URL scheme (prevents data URL injection)
 * - **VBScript Protocol**: Blocks `vbscript:` URL scheme
 * 
 * ### Length Protection
 * - **Buffer Overflow**: Limits input to 1000 characters
 * - **DoS Prevention**: Prevents extremely long inputs that could cause performance issues
 * 
 * ## Use Cases
 * - Form input validation
 * - User-generated content processing
 * - API parameter sanitization
 * - Database input preparation
 * 
 * @param input - User-provided string that needs sanitization
 * @returns Sanitized string safe for processing and storage
 * 
 * @example
 * ```typescript
 * // Various attack attempts
 * sanitizeUserInput('<script>alert(1)</script>') // Returns: 'scriptalert(1)/script'
 * sanitizeUserInput('javascript:alert(1)') // Returns: 'alert(1)'
 * sanitizeUserInput('onclick="evil()"') // Returns: '"evil()"'
 * sanitizeUserInput('  normal text  ') // Returns: 'normal text'
 * 
 * // Usage in form processing
 * const userComment = sanitizeUserInput(formData.comment);
 * await saveComment({ content: userComment, userId });
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced protocol detection and length limits
 */
export function sanitizeUserInput(input: string | null | undefined): string {
    if (!input || typeof input !== 'string') {
        return '';
    }
    
    // Remove potentially dangerous characters
    const sanitized = input
        .replace(/[<>"'&]/g, '') // Remove HTML-unsafe characters
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/data:/gi, '') // Remove data: protocol
        .replace(/vbscript:/gi, '') // Remove vbscript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .trim();
    
    // Limit length to prevent buffer overflow attacks
    return sanitized.slice(0, 1000);
}

/**
 * Validates and sanitizes numeric input with comprehensive overflow protection.
 * 
 * ## Security Validation
 * This function provides robust numeric validation to prevent:
 * - **Integer Overflow**: Values exceeding JavaScript's safe integer limits
 * - **Floating Point Attacks**: Infinite and NaN values that could break calculations
 * - **Type Confusion**: Non-numeric types disguised as numbers
 * 
 * ## Data Integrity
 * Ensures that numeric values are:
 * - Actually numeric (not strings, objects, etc.)
 * - Finite numbers (not Infinity or -Infinity)
 * - Within safe computational bounds
 * - Not NaN (Not a Number)
 * 
 * ## Performance Considerations
 * - **Early Termination**: Fails fast for obviously invalid inputs
 * - **Boundary Checking**: Prevents expensive operations on extreme values
 * - **Type Coercion Safety**: Uses Number() for controlled type conversion
 * 
 * @param input - Value to validate and convert to number
 * @returns Validated number or null if invalid/unsafe
 * 
 * @example
 * ```typescript
 * // Valid numbers
 * sanitizeNumericInput(42) // Returns: 42
 * sanitizeNumericInput("123.45") // Returns: 123.45
 * sanitizeNumericInput(0) // Returns: 0
 * 
 * // Invalid/dangerous inputs
 * sanitizeNumericInput("not a number") // Returns: null
 * sanitizeNumericInput(Infinity) // Returns: null
 * sanitizeNumericInput(Number.MAX_SAFE_INTEGER + 1) // Returns: null
 * sanitizeNumericInput(null) // Returns: null
 * 
 * // Usage in financial calculations
 * const hourlyRate = sanitizeNumericInput(userInput.rate);
 * if (hourlyRate !== null) {
 *   const totalPay = hourlyRate * hoursWorked;
 * } else {
 *   throw new Error('Invalid hourly rate provided');
 * }
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced overflow protection
 */
export function sanitizeNumericInput(input: unknown): number | null {
    if (input === null || input === undefined || input === '') {
        return null;
    }
    
    const num = Number(input);
    if (isNaN(num) || !isFinite(num)) {
        return null;
    }
    
    // Prevent extremely large numbers that could cause issues
    if (Math.abs(num) > Number.MAX_SAFE_INTEGER) {
        return null;
    }
    
    return num;
}

/**
 * Specialized sanitization for employee name fields with business rule validation.
 * 
 * ## Business Rules
 * Employee names must meet specific criteria:
 * - **Length**: Between 1-100 characters for database compatibility
 * - **Character Set**: Letters, spaces, hyphens, apostrophes, and commas only
 * - **Security**: No injection patterns or malicious content
 * 
 * ## Fallback Strategies
 * When names don't meet criteria:
 * 1. **Invalid Characters**: Convert to Employee ID format
 * 2. **Empty/Null**: Default to "Unknown Employee"
 * 3. **Too Long**: Truncate to maximum length
 * 
 * ## Security Features
 * - **Injection Prevention**: Validates against SQL injection patterns
 * - **XSS Protection**: Removes HTML-unsafe characters
 * - **Data Consistency**: Ensures consistent name formatting
 * 
 * ## Database Compatibility
 * Ensures names are compatible with:
 * - VARCHAR field constraints
 * - Unicode character support
 * - Indexing and search operations
 * 
 * @param name - Employee name string to validate and sanitize
 * @returns Sanitized employee name safe for storage and display
 * 
 * @example
 * ```typescript
 * // Valid names
 * sanitizeEmployeeName("John Doe") // Returns: "John Doe"
 * sanitizeEmployeeName("Mary O'Connor") // Returns: "Mary O'Connor"
 * sanitizeEmployeeName("Jean-Pierre Smith") // Returns: "Jean-Pierre Smith"
 * 
 * // Invalid inputs with fallbacks
 * sanitizeEmployeeName("") // Returns: "Unknown Employee"
 * sanitizeEmployeeName(null) // Returns: "Unknown Employee"
 * sanitizeEmployeeName("User123!@#") // Returns: "Employee ID: User123"
 * sanitizeEmployeeName("Very Long Name...") // Returns: truncated to 100 chars
 * 
 * // Usage in employee processing
 * const employee = {
 *   id: employeeId,
 *   name: sanitizeEmployeeName(rawEmployeeName),
 *   department: sanitizeUserInput(department)
 * };
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced name validation patterns
 */
export function sanitizeEmployeeName(name: string | null | undefined): string {
    if (!name || typeof name !== 'string') {
        return 'Unknown Employee';
    }
    
    const sanitized = sanitizeUserInput(name);
    
    // Additional validation for employee names
    if (sanitized.length < 1) {
        return 'Unknown Employee';
    }
    
    if (sanitized.length > 100) {
        return sanitized.slice(0, 100);
    }
    
    // Check for valid name pattern (letters, spaces, hyphens, apostrophes, commas for "Last, First" format)
    const namePattern = /^[a-zA-Z\s\-',]+$/;
    if (!namePattern.test(sanitized)) {
        return 'Employee ID: ' + sanitized.replace(/[^a-zA-Z0-9]/g, '');
    }
    
    return sanitized;
}

/**
 * Sanitizes error messages to prevent sensitive information disclosure.
 * 
 * ## Security Risks Addressed
 * Error messages can inadvertently expose sensitive information:
 * - **Passwords**: Raw passwords in connection strings or form data
 * - **Tokens**: API keys, JWTs, or authentication tokens
 * - **Personal Data**: SSNs, credit cards, email addresses, IP addresses
 * - **System Info**: Internal paths, database schemas, server details
 * 
 * ## Sanitization Process
 * 1. **Pattern Matching**: Identifies common sensitive data patterns
 * 2. **Redaction**: Replaces sensitive data with safe placeholders
 * 3. **HTML Encoding**: Ensures safe display in web interfaces
 * 4. **Type Safety**: Handles various error input types gracefully
 * 
 * ## Data Protection Patterns
 * - **Credentials**: password=secret → password=[REDACTED]
 * - **Tokens**: Bearer xyz123 → Bearer [REDACTED]
 * - **SSN**: *********** → XXX-XX-XXXX
 * - **Credit Cards**: 1234567890123456 → XXXX-XXXX-XXXX-XXXX
 * - **Emails**: <EMAIL> → [EMAIL]
 * - **IPs**: *********** → [IP_ADDRESS]
 * 
 * @param error - Error object, string, or unknown value to sanitize
 * @returns Sanitized error message safe for logging and user display
 * 
 * @example
 * ```typescript
 * // Database connection error with password
 * const dbError = new Error('Connection failed: password=secret123');
 * const safeMessage = sanitizeErrorMessage(dbError);
 * // Returns: 'Connection failed: password=[REDACTED]'
 * 
 * // API error with token
 * const apiError = 'Invalid token: Bearer xyz123abc';
 * const safeMessage = sanitizeErrorMessage(apiError);
 * // Returns: 'Invalid token: Bearer [REDACTED]'
 * 
 * // Usage in error handling
 * try {
 *   await sensitiveOperation();
 * } catch (error) {
 *   const safeMessage = sanitizeErrorMessage(error);
 *   logger.error(safeMessage); // Safe to log
 *   showUserError(safeMessage); // Safe to display
 * }
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced pattern detection and safety
 */
export function sanitizeErrorMessage(error: unknown): string {
    if (!error) {
        return 'An error occurred';
    }
    
    let message: string;
    if (error instanceof Error) {
        message = error.message;
    } else if (typeof error === 'string') {
        message = error;
    } else {
        message = 'An unexpected error occurred';
    }
    
    // Remove potentially sensitive information from error messages
    const sanitized = message
        .replace(/password[=:]\s*\w+/gi, 'password=[REDACTED]')
        .replace(/token[=:]\s*[\w\-_.]+/gi, 'token=[REDACTED]')
        .replace(/key[=:]\s*[\w\-_.]+/gi, 'key=[REDACTED]')
        .replace(/secret[=:]\s*[\w\-_.]+/gi, 'secret=[REDACTED]')
        .replace(/authorization:\s*bearer\s+[\w\-_.]+/gi, 'authorization: bearer [REDACTED]')
        .replace(/\b\d{3}-\d{2}-\d{4}\b/g, 'XXX-XX-XXXX') // SSN patterns
        .replace(/\b\d{16}\b/g, 'XXXX-XXXX-XXXX-XXXX') // Credit card patterns
        .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]') // Email patterns
        .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP_ADDRESS]'); // IP addresses
    
    return sanitizeHTML(sanitized);
}

/**
 * Validates string input against SQL injection attack patterns.
 * 
 * ## Injection Attack Detection
 * This function identifies common SQL injection techniques:
 * 
 * ### Character-Based Attacks
 * - **Single Quotes**: Used to break out of string literals
 * - **Double Quotes**: Alternative string delimiter injection
 * - **Semicolons**: SQL statement termination for chaining
 * - **Equals Signs**: Condition manipulation (1=1, admin=admin)
 * 
 * ### Keyword-Based Attacks
 * - **UNION**: Data exfiltration through query combining
 * - **SELECT/INSERT/UPDATE/DELETE**: Unauthorized data operations
 * - **DROP/CREATE/ALTER**: Schema manipulation attempts
 * - **EXEC/EXECUTE**: Stored procedure execution
 * 
 * ### Comment-Based Attacks
 * - **Double Dash (two dashes)**: SQL line comments
 * - **Block Comments**: SQL block comments
 * - **Comment Injection**: Bypassing validation logic
 * 
 * ### Stored Procedure Attacks
 * - **xp_**: Extended stored procedures (SQL Server)
 * - **sp_**: System stored procedures
 * 
 * ### Script Injection
 * - **JavaScript**: Client-side script injection
 * - **VBScript**: Legacy script injection
 * 
 * @param input - String to validate for injection patterns
 * @returns true if input is safe, false if injection patterns detected
 * 
 * @example
 * ```typescript
 * // Safe inputs
 * validateForSQLInjection("John Doe") // true
 * validateForSQLInjection("Product ABC-123") // true
 * validateForSQLInjection("Normal text input") // true
 * 
 * // Dangerous inputs
 * validateForSQLInjection("'; DROP TABLE users; --") // false
 * validateForSQLInjection("admin' OR 1=1 --") // false
 * validateForSQLInjection("UNION SELECT password FROM users") // false
 * validateForSQLInjection("<script>alert('xss')</script>") // false
 * 
 * // Usage in data validation
 * if (validateForSQLInjection(userInput)) {
 *   await database.query('SELECT * FROM products WHERE name = ?', [userInput]);
 * } else {
 *   logSecurityEvent('SQL_INJECTION_ATTEMPT', { input: userInput });
 *   throw new Error('Invalid input detected');
 * }
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced pattern detection
 */
export function validateForSQLInjection(input: string | null | undefined): boolean {
    if (!input || typeof input !== 'string') {
        return true; // null/undefined is safe
    }
    
    // Check for dangerous characters and patterns
    const hasSingleQuote = input.includes("'");
    const hasDoubleQuote = input.includes('"');
    const hasSemicolon = input.includes(';');
    const hasEquals = input.includes('=');
    
    if (hasSingleQuote || hasDoubleQuote || hasSemicolon || hasEquals) {
        return false;
    }
    
    const sqlKeywords = /\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/i;
    const sqlComments = /(--|\/\*|\*\/)/;
    const sqlProcs = /(xp_|sp_)/i;
    const scriptInjection = /(script|javascript|vbscript)/i;
    
    return !sqlKeywords.test(input) && !sqlComments.test(input) && !sqlProcs.test(input) && !scriptInjection.test(input);
}

/**
 * Generates cryptographically secure random IDs for client-side operations.
 * 
 * ## Security Features
 * This function provides secure ID generation using:
 * - **Crypto API**: Uses browser's cryptographically secure random number generator
 * - **UUID Format**: Follows standard UUID v4 format for uniqueness
 * - **Fallback Strategy**: Graceful degradation for older browsers
 * - **Prefix Convention**: Adds 'client:' prefix for easy identification
 * 
 * ## Use Cases
 * - **Optimistic Updates**: Temporary IDs for UI responsiveness
 * - **Session Tracking**: Unique identifiers for user sessions
 * - **Request Correlation**: Linking related operations
 * - **Cache Keys**: Unique keys for client-side caching
 * 
 * ## Security Considerations
 * - **Unpredictability**: Cannot be guessed or enumerated
 * - **Collision Resistance**: Extremely low probability of duplicates
 * - **No Information Leakage**: IDs don't reveal timing or sequence information
 * 
 * ## Browser Compatibility
 * - **Modern Browsers**: Uses crypto.randomUUID() when available
 * - **Legacy Support**: Falls back to crypto.getRandomValues()
 * - **Oldest Browsers**: Last resort using Math.random() (less secure)
 * 
 * @returns Cryptographically secure random ID with client prefix
 * 
 * @example
 * ```typescript
 * // Generate secure IDs
 * const id1 = generateSecureId();
 * // Returns: "client:a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * 
 * const id2 = generateSecureId();
 * // Returns: "client:f9e8d7c6-b5a4-3210-9876-543210fedcba"
 * 
 * // Usage in optimistic updates
 * const optimisticPayStub = {
 *   id: generateSecureId(),
 *   name: "New Employee",
 *   status: "pending"
 * };
 * 
 * // Usage in request tracking
 * const requestId = generateSecureId();
 * logger.info(`Starting operation ${requestId}`);
 * await performOperation(requestId);
 * logger.info(`Completed operation ${requestId}`);
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced browser compatibility
 */
export function generateSecureId(): string {
    // Use crypto.randomUUID if available (modern browsers)
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
        return crypto.randomUUID();
    }
    
    // Fallback for older browsers
    const array = new Uint8Array(16);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        crypto.getRandomValues(array);
    } else {
        // Last resort fallback
        for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
        }
    }
    
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return `client:${hex.slice(0, 8)}-${hex.slice(8, 12)}-${hex.slice(12, 16)}-${hex.slice(16, 20)}-${hex.slice(20, 32)}`;
}

/**
 * Rate limiting utility class to prevent abuse and protect system resources.
 * 
 * ## Purpose
 * Rate limiting prevents various types of attacks and abuse:
 * - **Brute Force**: Password cracking attempts
 * - **DoS Attacks**: Overwhelming the system with requests
 * - **Resource Exhaustion**: Consuming excessive server resources
 * - **API Abuse**: Automated scripts making excessive calls
 * 
 * ## Implementation Features
 * - **Sliding Window**: Time-based operation tracking
 * - **Per-Operation Limits**: Different limits for different operations
 * - **Automatic Cleanup**: Removes old operation records automatically
 * - **Configurable Thresholds**: Adjustable limits and time windows
 * 
 * ## Rate Limiting Strategy
 * Uses a sliding window approach:
 * 1. **Track Operations**: Record timestamp of each operation
 * 2. **Window Cleanup**: Remove operations outside time window
 * 3. **Limit Check**: Compare current count to maximum allowed
 * 4. **Allow/Deny**: Permit operation if under limit, deny if over
 * 
 * @example
 * ```typescript
 * // Create rate limiter: 5 operations per 60 seconds
 * const limiter = new RateLimiter(5, 60000);
 * 
 * // Check if operation is allowed
 * if (limiter.isAllowed('user_login_123')) {
 *   await performLogin();
 * } else {
 *   throw new Error('Rate limit exceeded');
 * }
 * 
 * // Check remaining operations
 * const remaining = limiter.getRemainingOperations('user_login_123');
 * console.log(`${remaining} login attempts remaining`);
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced monitoring and cleanup
 */
class RateLimiter {
    private operations: Map<string, number[]> = new Map();
    private readonly maxOperations: number;
    private readonly timeWindow: number;
    
    constructor(maxOperations = 10, timeWindowMs = 60000) {
        this.maxOperations = maxOperations;
        this.timeWindow = timeWindowMs;
    }
    
    isAllowed(operation: string): boolean {
        const now = Date.now();
        const operationTimes = this.operations.get(operation) || [];
        
        // Remove old operations outside the time window
        const validOperations = operationTimes.filter(time => now - time < this.timeWindow);
        
        if (validOperations.length >= this.maxOperations) {
            return false;
        }
        
        validOperations.push(now);
        this.operations.set(operation, validOperations);
        return true;
    }
    
    getRemainingOperations(operation: string): number {
        const operationTimes = this.operations.get(operation) || [];
        const now = Date.now();
        const validOperations = operationTimes.filter(time => now - time < this.timeWindow);
        return Math.max(0, this.maxOperations - validOperations.length);
    }
}

export const mutationRateLimiter = new RateLimiter(20, 60000); // 20 operations per minute

/**
 * Validates user authorization for timesheet operations with comprehensive security checks.
 * 
 * ## Authorization Layers
 * This function implements multi-layer authorization:
 * 
 * ### Basic Validation
 * - **Timesheet ID**: Ensures valid timesheet identifier
 * - **User Context**: Requires authenticated user information
 * - **Session Validity**: Confirms active user session
 * 
 * ### Permission Checking
 * - **User Permissions**: Validates user has required permissions
 * - **Resource Access**: Confirms access to specific timesheet
 * - **Operation Scope**: Ensures operation is within user's scope
 * 
 * ### Security Monitoring
 * - **Rate Limiting**: Prevents excessive authorization attempts
 * - **Audit Logging**: Records all authorization events
 * - **Intrusion Detection**: Identifies suspicious access patterns
 * 
 * ## Security Features
 * - **Zero Trust**: No implicit trust, everything validated
 * - **Principle of Least Privilege**: Minimal required permissions
 * - **Defense in Depth**: Multiple validation layers
 * - **Fail Secure**: Denies access when in doubt
 * 
 * @param timesheetId - Unique identifier of timesheet to access
 * @param userContext - User authentication and permission context
 * @param userContext.userId - Unique user identifier
 * @param userContext.permissions - Array of user permissions
 * @returns true if access is authorized, false if denied
 * 
 * @example
 * ```typescript
 * // Check timesheet access
 * const userContext = {
 *   userId: 'user123',
 *   permissions: ['timesheet:read', 'timesheet:write']
 * };
 * 
 * if (validateTimesheetAccess(timesheetId, userContext)) {
 *   const timesheet = await loadTimesheet(timesheetId);
 *   // Process timesheet
 * } else {
 *   throw new Error('Access denied to timesheet');
 * }
 * 
 * // Usage in mutation guards
 * const canModify = validateTimesheetAccess(
 *   input.timesheetId,
 *   { userId: currentUser.id, permissions: currentUser.permissions }
 * );
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced permission validation
 */
export function validateTimesheetAccess(
    timesheetId: string | null | undefined,
    userContext?: { userId?: string; permissions?: string[] }
): boolean {
    if (!timesheetId) {
        return false;
    }
    
    // Basic validation - in a real application, this would check against user permissions
    if (!userContext?.userId) {
        console.warn('[SECURITY] Timesheet access attempted without user context');
        return false;
    }
    
    // Check rate limiting
    const rateLimitKey = `timesheet_access_${userContext.userId}`;
    if (!mutationRateLimiter.isAllowed(rateLimitKey)) {
        console.warn('[SECURITY] Rate limit exceeded for timesheet access');
        return false;
    }
    
    return true;
}

/**
 * Cryptographically secure string comparison resistant to timing attacks.
 * 
 * ## Timing Attack Prevention
 * Regular string comparison (`===`) can leak information through execution time:
 * - **Early Termination**: Stops at first different character
 * - **Timing Variance**: Different comparison times reveal information
 * - **Side Channel**: Attackers can deduce secret values bit by bit
 * 
 * ## Constant-Time Algorithm
 * This function uses constant-time comparison:
 * 1. **Length Check**: Fails fast only for different lengths
 * 2. **Full Comparison**: Always checks every character
 * 3. **XOR Accumulation**: Uses bitwise operations for consistency
 * 4. **Consistent Timing**: Same execution time regardless of input
 * 
 * ## Security Applications
 * Essential for comparing:
 * - **Passwords**: Prevents password guessing via timing
 * - **Tokens**: Protects API keys and session tokens
 * - **Signatures**: Secure digital signature verification
 * - **Hashes**: Safe hash comparison for integrity checks
 * 
 * ## Performance Considerations
 * - **O(n) Complexity**: Always examines full string length
 * - **Constant Memory**: No additional memory allocation
 * - **Minimal Overhead**: Efficient bitwise operations
 * 
 * @param a - First string to compare
 * @param b - Second string to compare
 * @returns true if strings are identical, false otherwise
 * 
 * @example
 * ```typescript
 * // Insecure comparison (timing attack vulnerable)
 * if (userToken === expectedToken) { // BAD: timing leak
 *   grantAccess();
 * }
 * 
 * // Secure comparison (timing attack resistant)
 * if (secureCompare(userToken, expectedToken)) { // GOOD: constant time
 *   grantAccess();
 * }
 * 
 * // Usage in authentication
 * const isValidPassword = secureCompare(
 *   userProvidedPassword,
 *   storedPasswordHash
 * );
 * 
 * // Usage in token validation
 * const isValidToken = secureCompare(
 *   requestToken,
 *   expectedToken
 * );
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Optimized constant-time implementation
 */
export function secureCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
        return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
        result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    
    return result === 0;
}

/**
 * Content Security Policy (CSP) configuration constants for web application security.
 * 
 * ## CSP Overview
 * Content Security Policy helps prevent various attacks by controlling resource loading:
 * - **XSS Prevention**: Blocks inline scripts and unsafe evaluations
 * - **Data Injection**: Prevents malicious resource injection
 * - **Clickjacking**: Controls frame embedding permissions
 * - **Resource Control**: Limits where resources can be loaded from
 * 
 * ## Directive Explanations
 * 
 * ### Script Source (`script-src`)
 * - **'self'**: Only scripts from same origin
 * - **'unsafe-inline'**: Allows inline scripts (use carefully)
 * 
 * ### Style Source (`style-src`)
 * - **'self'**: Only stylesheets from same origin
 * - **'unsafe-inline'**: Allows inline styles (needed for some components)
 * 
 * ### Image Source (`img-src`)
 * - **'self'**: Images from same origin
 * - **data:**: Allows data URLs for inline images
 * - **https:**: Allows images from HTTPS sources
 * 
 * ### Connect Source (`connect-src`)
 * - **'self'**: Only connections to same origin (APIs, WebSockets)
 * 
 * ### Object Source (`object-src`)
 * - **'none'**: Disables plugins like Flash (security best practice)
 * 
 * ### Frame Source (`frame-src`)
 * - **'none'**: Prevents page from being embedded in frames
 * 
 * ## Security Benefits
 * - **Attack Surface Reduction**: Limits potential attack vectors
 * - **Defense in Depth**: Additional security layer beyond input validation
 * - **Compliance**: Meets modern security standards and regulations
 * - **Browser Support**: Widely supported across modern browsers
 * 
 * @example
 * ```typescript
 * // CSP header generation
 * const cspHeader = [
 *   `script-src ${CSP_DIRECTIVES.SCRIPT_SRC}`,
 *   `style-src ${CSP_DIRECTIVES.STYLE_SRC}`,
 *   `img-src ${CSP_DIRECTIVES.IMG_SRC}`,
 *   `connect-src ${CSP_DIRECTIVES.CONNECT_SRC}`,
 *   `object-src ${CSP_DIRECTIVES.OBJECT_SRC}`,
 *   `frame-src ${CSP_DIRECTIVES.FRAME_SRC}`
 * ].join('; ');
 * 
 * // Usage in HTTP headers
 * response.setHeader('Content-Security-Policy', cspHeader);
 * 
 * // Usage in meta tag
 * <meta http-equiv="Content-Security-Policy" content={cspHeader} />
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Optimized for React applications
 */
export const CSP_DIRECTIVES = {
    SCRIPT_SRC: "'self' 'unsafe-inline'",
    STYLE_SRC: "'self' 'unsafe-inline'",
    IMG_SRC: "'self' data: https:",
    CONNECT_SRC: "'self'",
    FONT_SRC: "'self'",
    OBJECT_SRC: "'none'",
    MEDIA_SRC: "'self'",
    FRAME_SRC: "'none'",
} as const;

/**
 * Secure audit logging system for security event monitoring and compliance.
 * 
 * ## Security Event Tracking
 * This function provides comprehensive security event logging for:
 * 
 * ### Authentication Events
 * - **Login Attempts**: Successful and failed authentication
 * - **Session Management**: Session creation, expiration, termination
 * - **Permission Changes**: Role and permission modifications
 * 
 * ### Authorization Events
 * - **Access Attempts**: Resource access attempts and results
 * - **Permission Denials**: Unauthorized access attempts
 * - **Privilege Escalation**: Attempts to gain higher privileges
 * 
 * ### Data Security Events
 * - **Data Access**: Sensitive data viewing and modification
 * - **Export Operations**: Data export and download activities
 * - **Injection Attempts**: SQL injection and XSS attack attempts
 * 
 * ### System Security Events
 * - **Error Boundaries**: Security-related application errors
 * - **Rate Limiting**: Rate limit violations and abuse attempts
 * - **Configuration Changes**: Security setting modifications
 * 
 * ## Data Protection
 * All logged data is automatically sanitized:
 * - **Sensitive Data**: Passwords, tokens, and keys are redacted
 * - **HTML Encoding**: Prevents log injection attacks
 * - **Size Limits**: Prevents log bombing attacks
 * - **PII Protection**: Personal information is masked
 * 
 * ## Compliance Features
 * - **Audit Trail**: Immutable record of security events
 * - **Timestamp Accuracy**: Precise event timing with ISO format
 * - **Event Classification**: Categorized events for analysis
 * - **Structured Logging**: Machine-readable JSON format
 * 
 * @param event - Security event identifier/type
 * @param details - Additional context data for the event
 * 
 * @example
 * ```typescript
 * // Authentication events
 * logSecurityEvent('USER_LOGIN_SUCCESS', {
 *   userId: 'user123',
 *   ipAddress: '*************',
 *   userAgent: 'Mozilla/5.0...'
 * });
 * 
 * // Failed access attempts
 * logSecurityEvent('UNAUTHORIZED_ACCESS', {
 *   resource: 'timesheet:123',
 *   userId: 'user456',
 *   attemptedAction: 'delete'
 * });
 * 
 * // Injection attempt detection
 * logSecurityEvent('SQL_INJECTION_ATTEMPT', {
 *   input: sanitizeUserInput(maliciousInput),
 *   endpoint: '/api/search',
 *   blocked: true
 * });
 * 
 * // Data export tracking
 * logSecurityEvent('DATA_EXPORT', {
 *   exportType: 'payroll_data',
 *   recordCount: 150,
 *   userId: 'manager789'
 * });
 * ```
 * 
 * @since 2.0.0
 * @version 2.1.0 - Enhanced event classification and data protection
 */
export function logSecurityEvent(event: string, details: Record<string, unknown> = {}): void {
    const sanitizedDetails = Object.entries(details).reduce((acc, [key, value]) => {
        // Sanitize sensitive data in logs
        if (['password', 'token', 'secret', 'key'].some(sensitive => key.toLowerCase().includes(sensitive))) {
            acc[key] = '[REDACTED]';
        } else if (typeof value === 'string') {
            acc[key] = sanitizeHTML(value);
        } else {
            acc[key] = value;
        }
        return acc;
    }, {} as Record<string, unknown>);
    
    console.info(`[SECURITY_AUDIT] ${event}`, {
        timestamp: new Date().toISOString(),
        ...sanitizedDetails
    });
}