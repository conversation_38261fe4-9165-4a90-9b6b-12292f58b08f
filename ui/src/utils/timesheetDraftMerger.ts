import type { PayStubDetailUI } from '../types/relay-ui-extensions';
import type { ModifiablePayStubDetail } from '../types/timesheet-detail';

/**
 * Timesheet-specific utility for merging draft changes into persisted data
 * Provides type-safe merging for timesheet pay stub details while maintaining the original data structure
 */

export interface Draftable {
    id: string;
}

/**
 * Interface for details that can be validated
 * Contains only the fields needed for validation, omitting UI-specific fields
 */
export interface ValidatableDetail {
    id: string;
    workDate: string | null;
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    bonus: number | null;
    expenses: number | null;
    jobCode: string | null;
    costCenter: string | null;
    agreementId: number | null;
    classificationId: number | null;
    delete?: boolean;
}

/**
 * Utility function to build draft objects from temporary edits
 * @param tempEdits - Raw draft edits from the store
 * @param parseNum - Function to parse numeric values
 * @param parseIntId - Function to parse ID values
 * @returns Draft object with properly typed values
 */
export function buildDraftFromEdits(
    tempEdits: Record<string, unknown>,
    parseNum: (val: unknown) => number | null,
    parseIntId: (val: unknown) => number | null
): Partial<ModifiablePayStubDetail | ValidatableDetail> {
    const draft: Record<string, unknown> = {};

    // Convert draft edits to properly typed format
    if (Object.hasOwn(tempEdits, 'stHours')) draft.stHours = parseNum(tempEdits.stHours);
    if (Object.hasOwn(tempEdits, 'otHours')) draft.otHours = parseNum(tempEdits.otHours);
    if (Object.hasOwn(tempEdits, 'dtHours')) draft.dtHours = parseNum(tempEdits.dtHours);
    if (Object.hasOwn(tempEdits, 'bonus')) draft.bonus = parseNum(tempEdits.bonus);
    if (Object.hasOwn(tempEdits, 'expenses')) draft.expenses = parseNum(tempEdits.expenses);
    if (Object.hasOwn(tempEdits, 'hourlyRate')) draft.hourlyRate = parseNum(tempEdits.hourlyRate);
    if (Object.hasOwn(tempEdits, 'jobCode')) draft.jobCode = typeof tempEdits.jobCode === 'string' ? tempEdits.jobCode || null : null;
    if (Object.hasOwn(tempEdits, 'costCenter'))
        draft.costCenter = typeof tempEdits.costCenter === 'string' ? tempEdits.costCenter || null : null;
    if (Object.hasOwn(tempEdits, 'agreementId')) draft.agreementId = parseIntId(tempEdits.agreementId);
    if (Object.hasOwn(tempEdits, 'classificationId')) draft.classificationId = parseIntId(tempEdits.classificationId);
    if (Object.hasOwn(tempEdits, '_uiDelete')) draft.delete = Boolean(tempEdits._uiDelete);

    return draft;
}

/**
 * Core merge function that combines persisted data with draft changes
 * @param persisted - Array of persisted objects
 * @param drafts - Record of draft changes keyed by object ID
 * @returns Array of merged objects with draft changes applied
 *
 * Note: When no draft exists for an item, the original object reference is returned.
 * Callers should deep-clone the result if they need to mutate the returned array
 * without affecting the original persisted data.
 */
export function mergeDrafts<T extends Draftable>(persisted: readonly T[], drafts: Record<string, Partial<T>>): T[] {
    return persisted.map((item) => {
        const draft = drafts[item.id];
        if (!draft || Object.keys(draft).length === 0) {
            return item;
        }
        // Filter out undefined values to prevent overwriting persisted data with undefined
        const filteredDraft = Object.fromEntries(Object.entries(draft).filter(([, value]) => value !== undefined));
        return { ...item, ...filteredDraft };
    });
}

/**
 * Type-safe wrapper for merging PayStubDetailUI objects
 * @param persisted - Array of persisted PayStubDetailUI objects
 * @param drafts - Record of draft changes keyed by detail ID
 * @returns Array of merged PayStubDetailUI objects
 */
export function mergeDetailDraftsForUI(
    persisted: readonly PayStubDetailUI[],
    drafts: Record<string, Partial<PayStubDetailUI>>
): PayStubDetailUI[] {
    return mergeDrafts(persisted, drafts);
}

/**
 * Type-safe wrapper for merging ValidatableDetail objects
 * @param persisted - Array of persisted ValidatableDetail objects
 * @param drafts - Record of draft changes keyed by detail ID
 * @returns Array of merged ValidatableDetail objects
 */
export function mergeDetailDraftsForValidation(
    persisted: readonly ValidatableDetail[],
    drafts: Record<string, Partial<ValidatableDetail>>
): ValidatableDetail[] {
    return mergeDrafts(persisted, drafts);
}
