/**
 * Error Boundary and <PERSON>rro<PERSON> Handling Utilities
 * 
 * Comprehensive error handling utilities to provide graceful degradation,
 * error recovery, and detailed error reporting.
 */

import React, { Component, ErrorInfo, ReactNode, useState, useCallback } from 'react';
import { sanitizeErrorMessage, logSecurityEvent } from './securityUtils';
import { sanitizePayload, createSafeErrorContext } from '@/src/services/pii-sanitizer';

/**
 * Error types for classification
 */
export enum ErrorType {
    NETWORK = 'network',
    VALIDATION = 'validation',
    PERMISSION = 'permission',
    TIMEOUT = 'timeout',
    UNKNOWN = 'unknown',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    CORRUPTION = 'corruption'
}

/**
 * Enhanced error interface with additional context
 */
export interface EnhancedError extends Error {
    type?: ErrorType;
    code?: string;
    context?: Record<string, unknown>;
    timestamp?: number;
    retryable?: boolean;
    recoverable?: boolean;
    userMessage?: string;
}

/**
 * Error boundary state
 */
interface ErrorBoundaryState {
    hasError: boolean;
    error: EnhancedError | null;
    errorId: string | null;
    retryCount: number;
}

/**
 * Props for error boundary
 */
interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: (error: EnhancedError, retry: () => void) => ReactNode;
    onError?: (error: EnhancedError, errorInfo: ErrorInfo) => void;
    maxRetries?: number;
    resetOnPropsChange?: boolean;
    resetKeys?: Array<string | number>;
}

/**
 * Enhanced Error Boundary component
 */
export class EnhancedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    private resetTimeoutId: ReturnType<typeof setTimeout> | null = null;
    
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorId: null,
            retryCount: 0
        };
    }
    
    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        const enhancedError = enhanceError(error);
        const errorId = generateErrorId();
        
        return {
            hasError: true,
            error: enhancedError,
            errorId,
            retryCount: 0
        };
    }
    
    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        const enhancedError = enhanceError(error, {
            componentStack: errorInfo.componentStack,
            errorBoundary: this.constructor.name
        });
        
        // Log error for monitoring
        logError(enhancedError, errorInfo);
        
        // Call custom error handler
        if (this.props.onError) {
            this.props.onError(enhancedError, errorInfo);
        }
        
        // Security logging for potential attacks
        if (enhancedError.type === ErrorType.SECURITY) {
            logSecurityEvent('ERROR_BOUNDARY_SECURITY_ERROR', {
                error: enhancedError.message,
                stack: enhancedError.stack,
                context: enhancedError.context
            });
        }
    }
    
    componentDidUpdate(prevProps: ErrorBoundaryProps) {
        const { resetOnPropsChange, resetKeys } = this.props;
        const { hasError } = this.state;
        
        if (hasError && (resetOnPropsChange || resetKeys)) {
            if (resetKeys) {
                const hasResetKeyChanged = resetKeys.some((key, index) => 
                    key !== (prevProps.resetKeys && prevProps.resetKeys[index])
                );
                
                if (hasResetKeyChanged) {
                    this.resetErrorBoundary();
                }
            }
        }
    }
    
    resetErrorBoundary = () => {
        if (this.resetTimeoutId) {
            clearTimeout(this.resetTimeoutId);
        }
        
        this.setState({
            hasError: false,
            error: null,
            errorId: null,
            retryCount: 0
        });
    };
    
    retry = () => {
        const { maxRetries = 3 } = this.props;
        const { retryCount } = this.state;
        
        if (retryCount < maxRetries) {
            this.setState(prevState => ({
                hasError: false,
                error: null,
                retryCount: prevState.retryCount + 1
            }));
            
            // Auto-reset after a delay if retry fails
            this.resetTimeoutId = setTimeout(() => {
                this.resetErrorBoundary();
            }, 10000);
        }
    };
    
    render() {
        const { hasError, error } = this.state;
        const { children, fallback } = this.props;
        
        if (hasError && error) {
            if (fallback) {
                return fallback(error, this.retry);
            }
            
            return (
                <DefaultErrorFallback 
                    error={error} 
                    retry={this.retry}
                    canRetry={this.state.retryCount < (this.props.maxRetries || 3)}
                />
            );
        }
        
        return children;
    }
}

/**
 * Default error fallback component
 */
interface DefaultErrorFallbackProps {
    error: EnhancedError;
    retry: () => void;
    canRetry: boolean;
}

function DefaultErrorFallback({ error, retry, canRetry }: DefaultErrorFallbackProps) {
    const userMessage = error.userMessage || getDefaultUserMessage(error.type);
    
    return (
        <div 
            className="error-boundary-fallback"
            style={{
                padding: '20px',
                border: '1px solid #f5c6cb',
                borderRadius: '4px',
                backgroundColor: '#f8d7da',
                color: '#721c24',
                margin: '10px 0'
            }}
        >
            <h3>Something went wrong</h3>
            <p>{sanitizeErrorMessage(userMessage)}</p>
            {canRetry && (
                <button 
                    onClick={retry}
                    style={{
                        marginTop: '10px',
                        padding: '8px 16px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                >
                    Try Again
                </button>
            )}
            {process.env.NODE_ENV === 'development' && (
                <details style={{ marginTop: '10px' }}>
                    <summary>Error Details (Development Only)</summary>
                    <pre style={{ 
                        marginTop: '10px', 
                        padding: '10px', 
                        backgroundColor: '#f8f9fa',
                        overflow: 'auto',
                        fontSize: '12px'
                    }}>
                        {createSafeErrorContext(error).stackTrace || error.stack}
                    </pre>
                </details>
            )}
        </div>
    );
}

/**
 * Enhance error with additional context and classification
 */
export function enhanceError(error: Error, context?: Record<string, unknown>): EnhancedError {
    const enhanced: EnhancedError = {
        ...error,
        timestamp: Date.now(),
        context: {
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            url: typeof window !== 'undefined' ? window.location.href : 'unknown',
            ...context
        }
    };
    
    // Classify error type
    enhanced.type = classifyError(error);
    
    // Determine if error is retryable
    enhanced.retryable = isRetryableError(enhanced.type, error);
    
    // Determine if error is recoverable
    enhanced.recoverable = isRecoverableError(enhanced.type, error);
    
    // Generate user-friendly message
    enhanced.userMessage = generateUserMessage(enhanced.type, error);
    
    return enhanced;
}

/**
 * Classify error based on message and context
 */
function classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
        return ErrorType.NETWORK;
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
        return ErrorType.VALIDATION;
    }
    
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
        return ErrorType.PERMISSION;
    }
    
    if (message.includes('timeout') || message.includes('timed out')) {
        return ErrorType.TIMEOUT;
    }
    
    if (message.includes('injection') || message.includes('xss') || message.includes('security')) {
        return ErrorType.SECURITY;
    }
    
    if (message.includes('memory') || message.includes('performance')) {
        return ErrorType.PERFORMANCE;
    }
    
    if (message.includes('corrupt') || message.includes('data integrity')) {
        return ErrorType.CORRUPTION;
    }
    
    return ErrorType.UNKNOWN;
}

/**
 * Determine if error is retryable
 */
function isRetryableError(type: ErrorType, error: Error): boolean {
    switch (type) {
        case ErrorType.NETWORK:
        case ErrorType.TIMEOUT:
        case ErrorType.PERFORMANCE:
            return true;
        case ErrorType.VALIDATION:
        case ErrorType.PERMISSION:
        case ErrorType.SECURITY:
        case ErrorType.CORRUPTION:
            return false;
        default:
            return false;
    }
}

/**
 * Determine if error is recoverable
 */
function isRecoverableError(type: ErrorType, error: Error): boolean {
    switch (type) {
        case ErrorType.NETWORK:
        case ErrorType.TIMEOUT:
        case ErrorType.VALIDATION:
        case ErrorType.PERFORMANCE:
            return true;
        case ErrorType.PERMISSION:
        case ErrorType.SECURITY:
        case ErrorType.CORRUPTION:
            return false;
        default:
            return true;
    }
}

/**
 * Generate user-friendly error message
 */
function generateUserMessage(type: ErrorType, error: Error): string {
    switch (type) {
        case ErrorType.NETWORK:
            return 'Network connection error. Please check your internet connection and try again.';
        case ErrorType.VALIDATION:
            return 'Some information is invalid. Please check your entries and try again.';
        case ErrorType.PERMISSION:
            return 'You do not have permission to perform this action.';
        case ErrorType.TIMEOUT:
            return 'The operation timed out. Please try again.';
        case ErrorType.SECURITY:
            return 'A security error occurred. Please contact support.';
        case ErrorType.PERFORMANCE:
            return 'The application is experiencing performance issues. Please try again.';
        case ErrorType.CORRUPTION:
            return 'Data corruption detected. Please refresh the page and try again.';
        default:
            return 'An unexpected error occurred. Please try again or contact support.';
    }
}

/**
 * Get default user message for error type
 */
function getDefaultUserMessage(type?: ErrorType): string {
    return generateUserMessage(type || ErrorType.UNKNOWN, new Error('Unknown error'));
}

/**
 * Generate unique error ID
 */
function generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Log error for monitoring and debugging
 */
function logError(error: EnhancedError, errorInfo?: ErrorInfo): void {
    // Phase 5: Sanitize all error context to prevent PII leakage
    const sanitizedContext = error.context ? sanitizePayload(error.context) : {};
    
    const logData = {
        errorId: generateErrorId(),
        type: error.type,
        message: sanitizeErrorMessage(error.message),
        timestamp: error.timestamp,
        context: sanitizedContext,
        retryable: error.retryable,
        recoverable: error.recoverable,
        componentStack: errorInfo?.componentStack
    };
    
    // Log to console in development (with sanitized data)
    if (process.env.NODE_ENV === 'development') {
        console.error('[ERROR_BOUNDARY]', logData);
    }
    
    // In production, you would send this to your error monitoring service
    // Example: sendToErrorMonitoring(logData);
}

/**
 * Error recovery utilities
 */
export class ErrorRecovery {
    private static recoveryStrategies = new Map<ErrorType, () => void>();
    
    static registerRecoveryStrategy(type: ErrorType, strategy: () => void): void {
        this.recoveryStrategies.set(type, strategy);
    }
    
    static recover(error: EnhancedError): boolean {
        if (!error.recoverable) {
            return false;
        }
        
        const strategy = this.recoveryStrategies.get(error.type || ErrorType.UNKNOWN);
        if (strategy) {
            try {
                strategy();
                return true;
            } catch (recoveryError) {
                console.error('[ERROR_RECOVERY] Recovery strategy failed:', recoveryError);
                return false;
            }
        }
        
        return false;
    }
}

/**
 * React hook for error handling
 */
export function useErrorHandler(): {
    handleError: (error: Error, context?: Record<string, unknown>) => void;
    clearError: () => void;
    error: EnhancedError | null;
} {
    const [error, setError] = useState<EnhancedError | null>(null);
    
    const handleError = useCallback((error: Error, context?: Record<string, unknown>) => {
        const enhanced = enhanceError(error, context);
        setError(enhanced);
        logError(enhanced);
        
        // Attempt automatic recovery
        if (enhanced.recoverable) {
            const recovered = ErrorRecovery.recover(enhanced);
            if (recovered) {
                setError(null);
            }
        }
    }, []);
    
    const clearError = useCallback(() => {
        setError(null);
    }, []);
    
    return { handleError, clearError, error };
}