/**
 * Field Display Name Utilities
 *
 * Provides consistent user-friendly field name mapping across the application.
 * This utility consolidates all field display name logic to prevent drift between
 * different components and services.
 */

/**
 * Maps internal field names to user-friendly display names
 *
 * @param field - The internal field name (e.g., 'stHours', 'agreementId')
 * @returns User-friendly field name (e.g., 'Standard hours', 'Agreement')
 *
 * @example
 * ```typescript
 * getFieldDisplayName('stHours') // 'Standard hours'
 * getFieldDisplayName('agreementId') // 'Agreement'
 * getFieldDisplayName('unknownField') // 'unknownField'
 * ```
 */
export function getFieldDisplayName(field: string): string {
    const fieldNames: Record<string, string> = {
        // Hours fields
        stHours: 'Standard hours',
        otHours: 'Overtime hours',
        dtHours: 'Double time hours',
        totalHours: 'Total hours',

        // Financial fields
        bonus: 'Bonus',
        expenses: 'Expenses',
        hourlyRate: 'Hourly rate',

        // Work classification fields
        jobCode: 'Job code',
        costCenter: 'Cost center',
        agreementId: 'Agreement',
        classificationId: 'Classification',
        subClassificationId: 'Sub-classification',
        earningsCode: 'Earnings code',

        // Date and identification fields
        workDate: 'Work date',
        employeeId: 'Employee',
        name: 'Day',

        // UI fields
        actions: 'Actions',

        // Validation fields
        conversion: 'Data conversion',
        payStubs: 'Pay stubs',
        header: 'Header information'
    };

    return fieldNames[field] || field;
}

/**
 * Gets display name with optional formatting for different contexts
 *
 * @param field - The internal field name
 * @param context - Optional context for specialized formatting
 * @returns Formatted field display name
 */
export function getFieldDisplayNameWithContext(field: string, context?: 'error' | 'label' | 'tooltip'): string {
    const baseName = getFieldDisplayName(field);

    switch (context) {
        case 'error':
            return baseName;
        case 'label':
            return baseName;
        case 'tooltip':
            return `${baseName} field`;
        default:
            return baseName;
    }
}

/**
 * Gets a descriptive field name with additional context for accessibility
 *
 * @param field - The internal field name
 * @returns Extended field description for screen readers
 */
export function getFieldAccessibilityName(field: string): string {
    const fieldDescriptions: Record<string, string> = {
        stHours: 'Standard Hours (regular work time)',
        otHours: 'Overtime Hours (time over 40 hours)',
        dtHours: 'Double Time Hours (premium overtime)',
        hourlyRate: 'Hourly Rate (pay per hour)',
        bonus: 'Bonus Amount',
        expenses: 'Expense Amount',
        jobCode: 'Job Code (work type identifier)',
        costCenter: 'Cost Center (department code)',
        agreementId: 'Labor Agreement',
        classificationId: 'Job Classification',
        subClassificationId: 'Job Sub-Classification',
        earningsCode: 'Earnings Code (pay type)',
        employeeId: 'Employee Selection',
        workDate: 'Work Date',
        conversion: 'Data Conversion'
    };

    return fieldDescriptions[field] || getFieldDisplayName(field);
}
