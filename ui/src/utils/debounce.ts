/**
 * Generic Debounce Utility
 * 
 * This module provides a type-safe, reusable debounce implementation
 * that can be used throughout the application for performance optimization.
 */

/**
 * Function signature that can be debounced
 */
export type DebouncableFunction<TArgs extends unknown[], TReturn> = (
    ...args: TArgs
) => TReturn;

/**
 * Debounced function with cancel method
 */
export interface DebouncedFunction<TArgs extends unknown[], TReturn> {
    (...args: TArgs): void;
    /** Cancel any pending execution */
    cancel: () => void;
    /** Flush - immediately execute any pending call */
    flush: () => void;
    /** Check if there's a pending execution */
    pending: () => boolean;
}

/**
 * Options for debounce behavior
 */
export interface DebounceOptions {
    /** Whether to call on the leading edge (default: false) */
    leading?: boolean;
    /** Whether to call on the trailing edge (default: true) */
    trailing?: boolean;
    /** Maximum time to wait before forcing execution */
    maxWait?: number;
}

/**
 * Creates a debounced version of a function that delays invoking the function
 * until after `delay` milliseconds have elapsed since the last time it was invoked.
 * 
 * @param func - The function to debounce
 * @param delay - The delay in milliseconds
 * @param options - Options to control debounce behavior
 * @returns A debounced version of the function with cancel, flush, and pending methods
 * 
 * @example
 * ```typescript
 * const debouncedSave = debounce(saveData, 300);
 * 
 * // Call multiple times - only last call will execute after 300ms
 * debouncedSave(data1);
 * debouncedSave(data2);
 * debouncedSave(data3); // Only this will execute
 * 
 * // Cancel pending execution
 * debouncedSave.cancel();
 * 
 * // Execute immediately
 * debouncedSave.flush();
 * 
 * // Check if pending
 * if (debouncedSave.pending()) {
 *   console.log('Save is pending');
 * }
 * ```
 */
export function debounce<TArgs extends unknown[], TReturn>(
    func: DebouncableFunction<TArgs, TReturn>,
    delay: number,
    options: DebounceOptions = {}
): DebouncedFunction<TArgs, TReturn> {
    const { leading = false, trailing = true, maxWait } = options;

    let timeoutId: ReturnType<typeof setTimeout> | null = null;
    let maxTimeoutId: ReturnType<typeof setTimeout> | null = null;
    let lastCallTime: number | null = null;
    let lastInvokeTime = 0;
    let lastArgs: TArgs | null = null;
    let lastThis: unknown = null;
    let result: TReturn | undefined;

    // Validate inputs
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    if (typeof delay !== 'number' || delay < 0) {
        throw new TypeError('Expected delay to be a non-negative number');
    }
    if (maxWait !== undefined && (typeof maxWait !== 'number' || maxWait < delay)) {
        throw new TypeError('Expected maxWait to be a number greater than or equal to delay');
    }

    const invokeFunc = (time: number): TReturn | undefined => {
        const args = lastArgs;
        const thisArg = lastThis;

        lastArgs = null;
        lastThis = null;
        lastInvokeTime = time;

        if (args) {
            result = func.apply(thisArg, args);
        }
        return result;
    };

    const leadingEdge = (time: number): void => {
        lastInvokeTime = time;
        
        // Start the timer for the trailing edge
        timeoutId = setTimeout(timerExpired, delay);
        
        // Invoke immediately if leading is true
        if (leading) {
            invokeFunc(time);
        }
    };

    const timerExpired = (): void => {
        const time = Date.now();
        
        if (shouldInvoke(time)) {
            trailingEdge(time);
        } else {
            // Restart the timer
            const timeSinceLastCall = time - (lastCallTime || 0);
            const timeSinceLastInvoke = time - lastInvokeTime;
            const timeWaiting = delay - timeSinceLastCall;
            const remainingMaxWait = maxWait ? maxWait - timeSinceLastInvoke : Infinity;
            
            timeoutId = setTimeout(
                timerExpired,
                Math.min(timeWaiting, remainingMaxWait)
            );
        }
    };

    const trailingEdge = (time: number): TReturn | undefined => {
        timeoutId = null;
        maxTimeoutId = null;

        // Only invoke if we have lastArgs, meaning func has been called
        if (trailing && lastArgs) {
            return invokeFunc(time);
        }
        
        lastArgs = null;
        lastThis = null;
        return result;
    };

    const cancel = (): void => {
        if (timeoutId !== null) {
            clearTimeout(timeoutId);
            timeoutId = null;
        }
        if (maxTimeoutId !== null) {
            clearTimeout(maxTimeoutId);
            maxTimeoutId = null;
        }
        lastInvokeTime = 0;
        lastArgs = null;
        lastCallTime = null;
        lastThis = null;
    };

    const flush = (): TReturn | undefined => {
        if (timeoutId === null) {
            return result;
        }
        
        const time = Date.now();
        return trailingEdge(time);
    };

    const pending = (): boolean => {
        return timeoutId !== null;
    };

    const shouldInvoke = (time: number): boolean => {
        const timeSinceLastCall = time - (lastCallTime || 0);
        const timeSinceLastInvoke = time - lastInvokeTime;

        return (
            lastCallTime === null ||
            timeSinceLastCall >= delay ||
            timeSinceLastCall < 0 ||
            (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
        );
    };

    const debounced = function (this: unknown, ...args: TArgs): void {
        const time = Date.now();
        const isInvoking = shouldInvoke(time);

        lastArgs = args;
        lastThis = this;
        lastCallTime = time;

        if (isInvoking) {
            if (timeoutId === null) {
                leadingEdge(time);
            } else if (maxWait !== undefined) {
                // Handle maxWait
                if (maxTimeoutId === null) {
                    maxTimeoutId = setTimeout(() => {
                        if (timeoutId) {
                            clearTimeout(timeoutId);
                            timeoutId = null;
                        }
                        trailingEdge(Date.now());
                    }, maxWait);
                }
            }
        } else if (timeoutId === null) {
            timeoutId = setTimeout(timerExpired, delay);
        }
    };

    debounced.cancel = cancel;
    debounced.flush = flush;
    debounced.pending = pending;

    return debounced;
}

/**
 * Creates a throttled version of a function that only invokes the function
 * at most once per every `delay` milliseconds.
 * 
 * @param func - The function to throttle
 * @param delay - The delay in milliseconds
 * @param options - Options to control throttle behavior (leading/trailing)
 * @returns A throttled version of the function
 * 
 * @example
 * ```typescript
 * const throttledScroll = throttle(handleScroll, 100);
 * window.addEventListener('scroll', throttledScroll);
 * ```
 */
export function throttle<TArgs extends unknown[], TReturn>(
    func: DebouncableFunction<TArgs, TReturn>,
    delay: number,
    options: Omit<DebounceOptions, 'maxWait'> = {}
): DebouncedFunction<TArgs, TReturn> {
    const { leading = true, trailing = true } = options;
    
    return debounce(func, delay, {
        leading,
        trailing,
        maxWait: delay,
    });
}