/**
 * Domain-GraphQL Conversion Functions
 *
 * Critical Missing Implementation from Fragment-Domain Model Architecture Fix
 *
 * This file provides the conversion functions between domain models used by UI components
 * and GraphQL input types used by mutations. These converters are essential for the
 * dual-prop pattern implementation that fixes the fragment reference type casting issues.
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ UI Components (Domain Models)           │
 * ├─────────────────────────────────────────┤
 * │ Conversion Layer (THIS FILE)            │ ← Domain ↔ GraphQL
 * ├─────────────────────────────────────────┤
 * │ GraphQL Operations (Wire Format)        │
 * └─────────────────────────────────────────┘
 */

import type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput,
    AddTimesheetInput,
    AddPayStubInput,
    AddPayStubDetailInput
} from '../types/graphql-timesheet';

import type { PayStubDomainModel, PayStubDetailDomainModel, TimesheetDomainModel } from '../types/timesheet-domain';

import { RelayIdService } from '../services/RelayIdService';
import { TimesheetIdConverter } from './timesheet-id-converters';

// =============================================================================
// TIMESHEET CONVERSION FUNCTIONS
// =============================================================================

/**
 * Converts timesheet domain model to GraphQL input format
 * Used when saving timesheet-level changes through mutations
 */
export function convertTimesheetDomainToGraphQL(timesheet: Partial<TimesheetDomainModel>, employerGuid: string): ModifyTimeSheetInput {
    if (!timesheet.id) {
        throw new Error('Timesheet ID is required for GraphQL conversion');
    }

    return {
        id: RelayIdService.isGlobalId(timesheet.id)
            ? timesheet.id
            : RelayIdService.toGlobalId('TimeSheet', RelayIdService.toNumericId(timesheet.id)), // Optimize: avoid decode→re-encode if already global ID
        employerGuid: employerGuid,
        name: timesheet.name,
        status: timesheet.status,
        type: timesheet.type,
        readOnly: timesheet.settings?.readOnly,
        showDTHoursColumn: timesheet.settings?.showDTHoursColumn,
        showCostCenterColumn: timesheet.settings?.showCostCenterColumn,
        showBonusColumn: timesheet.settings?.showBonusColumn,
        showExpensesColumn: timesheet.settings?.showExpensesColumn,
        showEarningsCodesColumn: timesheet.settings?.showEarningsCodesColumn,
        // Note: payStubs field no longer exists in ModifyTimeSheetInput
        // Use separate arrays (addPayStubs, modifyPayStubs, deletePayStubIds) at the operation level
        modifyPayStubs: timesheet.payStubs ? convertPayStubDomainArrayToGraphQL(timesheet.payStubs) : undefined
    };
}

// =============================================================================
// PAYSTUB CONVERSION FUNCTIONS
// =============================================================================

/**
 * Converts a Map of PayStub drafts to GraphQL ModifyPayStubInput array
 * This is the critical missing function from the implementation plan
 *
 * @param payStubDrafts - Map of PayStub ID to partial domain model drafts
 * @returns Array of GraphQL input objects ready for mutation
 */
export function convertDomainToModifyInput(payStubDrafts: Map<string, Partial<PayStubDomainModel>>): ModifyPayStubInput[] {
    return Array.from(payStubDrafts.entries()).map(([id, draft]) => {
        if (!draft.employee?.id) {
            throw new Error(`PayStub ${id} is missing required employee ID`);
        }

        return {
            id: id, // Pass ID as string for GraphQL
            employeeId: draft.employee.id, // Pass Global ID directly - HotChocolate will decode it
            employeeName: draft.employee.fullName || draft.employeeName,
            name: draft.name,
            stHours: draft.hours?.standard,
            otHours: draft.hours?.overtime,
            dtHours: draft.hours?.doubletime,
            bonus: draft.amounts?.bonus,
            expenses: draft.amounts?.expenses,
            // Note: delete field removed from GraphQL schema
            details: draft.details ? convertDraftDetailsToGraphQL(draft.details) : undefined
        };
    });
}

/**
 * Converts array of PayStub domain models to GraphQL input array
 * Used for bulk operations and complete timesheet saves
 */
export function convertPayStubDomainArrayToGraphQL(payStubs: PayStubDomainModel[]): ModifyPayStubInput[] {
    return payStubs.map((payStub) => convertPayStubDomainToGraphQL(payStub));
}

/**
 * Converts single PayStub domain model to GraphQL input format
 * Core conversion function for individual PayStub operations
 */
export function convertPayStubDomainToGraphQL(payStub: PayStubDomainModel | Partial<PayStubDomainModel>): ModifyPayStubInput {
    if (!payStub.id) {
        throw new Error('PayStub ID is required for GraphQL conversion');
    }

    if (!payStub.employee?.id && !payStub.employeeId) {
        throw new Error('Employee ID is required for PayStub GraphQL conversion');
    }

    const employeeId = payStub.employee?.id || payStub.employeeId;

    if (!employeeId) {
        throw new Error(`PayStub ${payStub.id} is missing required employee ID`);
    }

    return {
        id: payStub.id, // Pass ID as string for GraphQL
        employeeId: employeeId, // Pass Global ID directly - HotChocolate will decode it
        employeeName: payStub.employee?.fullName || payStub.employeeName,
        name: payStub.name,
        stHours: payStub.hours?.standard,
        otHours: payStub.hours?.overtime,
        dtHours: payStub.hours?.doubletime,
        bonus: payStub.amounts?.bonus,
        expenses: payStub.amounts?.expenses,
        // Note: delete field removed from GraphQL schema
        details: payStub.details ? convertPayStubDetailDomainArrayToGraphQL(payStub.details) : undefined
    };
}

// =============================================================================
// PAYSTUB DETAIL CONVERSION FUNCTIONS
// =============================================================================

/**
 * Converts PayStub detail domain models to GraphQL input array
 * This is the critical missing function implementation from Phase 0
 *
 * @param details - Array of PayStub detail domain models
 * @returns Array of GraphQL input objects with proper field mapping
 */
export function convertDraftDetailsToGraphQL(details: PayStubDetailDomainModel[]): ModifyPayStubDetailInput[] {
    return details.map((detail) => convertPayStubDetailDomainToGraphQL(detail));
}

/**
 * Converts array of PayStubDetail domain models to GraphQL input array
 * Used for complete detail array operations
 */
export function convertPayStubDetailDomainArrayToGraphQL(details: PayStubDetailDomainModel[]): ModifyPayStubDetailInput[] {
    return details.map((detail) => convertPayStubDetailDomainToGraphQL(detail));
}

/**
 * Converts single PayStubDetail domain model to GraphQL input format
 * Core conversion function ensuring proper field name mapping
 */
export function convertPayStubDetailDomainToGraphQL(
    detail: PayStubDetailDomainModel | Partial<PayStubDetailDomainModel>
): ModifyPayStubDetailInput {
    if (!detail.workDate) {
        throw new Error('Work date is required for PayStubDetail GraphQL conversion');
    }

    return {
        id: detail.id, // Pass Global ID directly
        workDate: detail.workDate,
        name: detail.name,
        stHours: detail.hours?.standard,
        otHours: detail.hours?.overtime,
        dtHours: detail.hours?.doubletime,
        jobCode: detail.job?.jobCode,
        earningsCode: detail.earnings?.earningsCode,
        agreementId: detail.agreements?.agreementId,
        classificationId: detail.agreements?.classificationId,
        subClassificationId: detail.agreements?.subClassificationId,
        costCenter: detail.job?.costCenter,
        hourlyRate: detail.job?.hourlyRate,
        bonus: detail.amounts?.bonus,
        expenses: detail.amounts?.expenses,
        reportLineItemId: detail.reportLineItemId,
        // Note: delete field removed from GraphQL schema
    };
}

// =============================================================================
// ADD OPERATION CONVERSION FUNCTIONS (Phase 2 Implementation)
// =============================================================================

/**
 * Converts domain model to AddPayStub GraphQL input format
 * Used when adding new PayStubs to existing timesheets
 */
export function convertDomainToAddInput(payStub: PayStubDomainModel, employeeId: string): AddPayStubInput {
    return {
        employeeId: employeeId, // Pass Global ID directly - backend now accepts ID! type
        employeeName: payStub.employee?.fullName || payStub.employeeName,
        name: payStub.name,
        stHours: payStub.hours?.standard || 0,
        otHours: payStub.hours?.overtime || 0,
        dtHours: payStub.hours?.doubletime || 0,
        bonus: payStub.amounts?.bonus || 0,
        expenses: payStub.amounts?.expenses || 0,
        details: payStub.details?.map((detail) => convertDomainToAddDetailInput(detail)) || [],
        // Note: delete field removed from GraphQL schema
        expanded: payStub.ui?.expanded,
        inEdit: payStub.ui?.isEditing
    };
}

/**
 * Converts domain PayStubDetail to AddPayStubDetail GraphQL input format
 * Helper function for adding new PayStub details
 */
export function convertDomainToAddDetailInput(detail: PayStubDetailDomainModel): AddPayStubDetailInput {
    return {
        payStubId: detail.payStubId, // Pass Global ID directly
        workDate: detail.workDate,
        name: detail.name,
        stHours: detail.hours?.standard || 0,
        otHours: detail.hours?.overtime || 0,
        dtHours: detail.hours?.doubletime || 0,
        jobCode: detail.job?.jobCode,
        earningsCode: detail.earnings?.earningsCode,
        agreementId: detail.agreements?.agreementId,
        classificationId: detail.agreements?.classificationId,
        subClassificationId: detail.agreements?.subClassificationId,
        costCenter: detail.job?.costCenter,
        hourlyRate: detail.job?.hourlyRate,
        bonus: detail.amounts?.bonus || 0,
        expenses: detail.amounts?.expenses || 0,
        reportLineItemId: detail.reportLineItemId,
        // Note: delete field removed from GraphQL schema
    };
}

// =============================================================================
// DELETE OPERATION CONVERSION FUNCTIONS (Phase 2 Implementation)
// =============================================================================

/**
 * Converts PayStub ID to delete operation input
 * Used for removing PayStubs from timesheets
 */
export function convertDomainToDeleteInput(payStubId: string): { id: string } {
    return {
        id: payStubId // Pass Global ID directly
    };
}

/**
 * Converts PayStubDetail ID to delete operation input
 * Used for removing PayStub details
 */
export function convertDomainToDeleteDetailInput(detailId: string): { id: string } {
    return {
        id: detailId // Pass Global ID directly
    };
}

// =============================================================================
// MOVE OPERATION CONVERSION FUNCTIONS (Phase 2 Implementation)
// =============================================================================

/**
 * Converts PayStub move operation to GraphQL input format
 * Used for employee reassignment and PayStub transfers
 */
export function convertDomainToMoveInput(
    payStubId: string,
    newEmployeeId: string,
    newTimesheetId?: string
): { payStubId: string; newEmployeeId: string; newTimesheetId?: string } {
    return {
        payStubId: payStubId, // Pass Global ID directly
        newEmployeeId: newEmployeeId, // Pass Global ID directly
        newTimesheetId: newTimesheetId // Pass Global ID directly
    };
}

// =============================================================================
// EMPTY PAYSTUB OPERATION CONVERSION (Phase 2 Implementation)
// =============================================================================

/**
 * Converts domain model to AddEmptyPayStub GraphQL input format
 * Used for creating minimal PayStub records that can be populated later
 */
export function convertDomainToAddEmptyPayStubInput(timesheetId: string, employeeId: string): { timeSheetId: string; employeeId: string } {
    return {
        timeSheetId: timesheetId, // Pass Global ID directly
        employeeId: employeeId // Pass Global ID directly
    };
}

// =============================================================================
// REVERSE CONVERSION FUNCTIONS (GraphQL → Domain)
// =============================================================================

/**
 * Converts GraphQL query results back to domain models
 * Used when receiving data from server queries
 */
export function convertGraphQLToPayStubDomain(
    graphqlPayStub: any // GraphQL fragment data
): PayStubDomainModel {
    return {
        id: graphqlPayStub.id?.toString() || '',
        employeeId: graphqlPayStub.employee?.id?.toString() || '',
        employeeName: graphqlPayStub.employee?.name || '',
        name: graphqlPayStub.name,

        hours: {
            standard: graphqlPayStub.stHours || 0,
            overtime: graphqlPayStub.otHours || 0,
            doubletime: graphqlPayStub.dtHours || 0,
            total: (graphqlPayStub.stHours || 0) + (graphqlPayStub.otHours || 0) + (graphqlPayStub.dtHours || 0)
        },

        amounts: {
            bonus: graphqlPayStub.bonus || 0,
            expenses: graphqlPayStub.expenses || 0
        },

        details: graphqlPayStub.details?.map(convertGraphQLToPayStubDetailDomain) || [],

        employee: {
            id: graphqlPayStub.employee?.id?.toString() || '',
            firstName: graphqlPayStub.employee?.firstName,
            lastName: graphqlPayStub.employee?.lastName,
            fullName: graphqlPayStub.employee?.name || `${graphqlPayStub.employee?.lastName}, ${graphqlPayStub.employee?.firstName}`,
            externalEmployeeId: graphqlPayStub.employee?.externalEmployeeId,
            active: graphqlPayStub.employee?.active ?? true
        },

        ui: {
            expanded: false,
            isEditing: false,
            hasErrors: false,
            isSelected: false,
        }
    };
}

/**
 * Converts GraphQL PayStubDetail to domain model
 */
export function convertGraphQLToPayStubDetailDomain(
    graphqlDetail: any // GraphQL fragment data
): PayStubDetailDomainModel {
    return {
        id: graphqlDetail.id?.toString() || '',
        payStubId: graphqlDetail.payStubId?.toString() || '',
        reportLineItemId: graphqlDetail.reportLineItemId,

        workDate: graphqlDetail.workDate,
        dayName: getDayNameFromDate(graphqlDetail.workDate),
        name: graphqlDetail.name,

        hours: {
            standard: graphqlDetail.stHours || 0,
            overtime: graphqlDetail.otHours || 0,
            doubletime: graphqlDetail.dtHours || 0,
            total: (graphqlDetail.stHours || 0) + (graphqlDetail.otHours || 0) + (graphqlDetail.dtHours || 0)
        },

        job: {
            jobCode: graphqlDetail.jobCode,
            costCenter: graphqlDetail.costCenter,
            hourlyRate: graphqlDetail.hourlyRate
        },

        agreements: {
            agreementId: graphqlDetail.agreementId,
            classificationId: graphqlDetail.classificationId,
            subClassificationId: graphqlDetail.subClassificationId
        },

        amounts: {
            bonus: graphqlDetail.bonus || 0,
            expenses: graphqlDetail.expenses || 0
        },

        earnings: {
            earningsCode: graphqlDetail.earningsCode,
            earningsCodeText: graphqlDetail.earningsCodeText
        },

        employeeId: graphqlDetail.employeeId?.toString() || '',

        ui: {
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            validationErrors: []
        }
    };
}

// =============================================================================
// VALIDATION FUNCTIONS
// =============================================================================

/**
 * Validates domain model before GraphQL conversion
 * Ensures all required fields are present and properly formatted
 */
export function validatePayStubForGraphQLConversion(payStub: Partial<PayStubDomainModel>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payStub.id) {
        errors.push('PayStub ID is required');
    }

    if (!payStub.employee?.id && !payStub.employeeId) {
        errors.push('Employee ID is required');
    }

    // Validate numeric fields
    if (payStub.hours) {
        if (payStub.hours.standard !== undefined && (payStub.hours.standard < 0 || payStub.hours.standard > 24)) {
            errors.push('Standard hours must be between 0 and 24');
        }
        if (payStub.hours.overtime !== undefined && (payStub.hours.overtime < 0 || payStub.hours.overtime > 24)) {
            errors.push('Overtime hours must be between 0 and 24');
        }
        if (payStub.hours.doubletime !== undefined && (payStub.hours.doubletime < 0 || payStub.hours.doubletime > 24)) {
            errors.push('Doubletime hours must be between 0 and 24');
        }
    }

    if (payStub.amounts) {
        if (payStub.amounts.bonus !== undefined && payStub.amounts.bonus < 0) {
            errors.push('Bonus amount cannot be negative');
        }
        if (payStub.amounts.expenses !== undefined && payStub.amounts.expenses < 0) {
            errors.push('Expenses amount cannot be negative');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validates PayStubDetail before GraphQL conversion
 */
export function validatePayStubDetailForGraphQLConversion(detail: Partial<PayStubDetailDomainModel>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!detail.workDate) {
        errors.push('Work date is required');
    } else if (!isValidDateString(detail.workDate)) {
        errors.push('Work date must be in YYYY-MM-DD format');
    }

    // Validate numeric fields
    if (detail.hours) {
        if (detail.hours.standard !== undefined && (detail.hours.standard < 0 || detail.hours.standard > 24)) {
            errors.push('Standard hours must be between 0 and 24');
        }
        if (detail.hours.overtime !== undefined && (detail.hours.overtime < 0 || detail.hours.overtime > 24)) {
            errors.push('Overtime hours must be between 0 and 24');
        }
        if (detail.hours.doubletime !== undefined && (detail.hours.doubletime < 0 || detail.hours.doubletime > 24)) {
            errors.push('Doubletime hours must be between 0 and 24');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Converts date string to day name
 * Helper function for domain model population
 */
function getDayNameFromDate(dateString: string): string {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { weekday: 'long' });
    } catch {
        return 'Unknown';
    }
}

/**
 * Validates date string format (YYYY-MM-DD)
 */
function isValidDateString(dateString: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) return false;

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Safe number conversion with validation
 * Ensures numeric fields are properly converted to numbers
 */
export function safeNumberConversion(value: unknown): number | undefined {
    if (value === null || value === undefined || value === '') {
        return undefined;
    }

    const num = typeof value === 'string' ? parseFloat(value) : Number(value);
    return isNaN(num) ? undefined : num;
}

/**
 * Safe integer conversion for ID fields
 */
export function safeIntegerConversion(value: unknown): number | undefined {
    if (value === null || value === undefined || value === '') {
        return undefined;
    }

    const num = typeof value === 'string' ? parseInt(value, 10) : Number(value);
    return isNaN(num) ? undefined : num;
}

// =============================================================================
// EXPORT TYPE UTILITIES
// =============================================================================

/**
 * Type utility to ensure conversion function completeness
 * This helps catch missing conversion implementations at compile time
 */
export type ConversionFunctionMap = {
    domainToGraphQL: {
        timesheet: typeof convertTimesheetDomainToGraphQL;
        payStub: typeof convertPayStubDomainToGraphQL;
        payStubDetail: typeof convertPayStubDetailDomainToGraphQL;
        payStubArray: typeof convertPayStubDomainArrayToGraphQL;
        detailArray: typeof convertPayStubDetailDomainArrayToGraphQL;
        // Phase 2: New conversion functions
        addPayStub: typeof convertDomainToAddInput;
        addPayStubDetail: typeof convertDomainToAddDetailInput;
        deletePayStub: typeof convertDomainToDeleteInput;
        deletePayStubDetail: typeof convertDomainToDeleteDetailInput;
        movePayStub: typeof convertDomainToMoveInput;
        addEmptyPayStub: typeof convertDomainToAddEmptyPayStubInput;
    };
    graphQLToDomain: {
        payStub: typeof convertGraphQLToPayStubDomain;
        payStubDetail: typeof convertGraphQLToPayStubDetailDomain;
    };
    validation: {
        payStub: typeof validatePayStubForGraphQLConversion;
        payStubDetail: typeof validatePayStubDetailForGraphQLConversion;
    };
};

/**
 * Default export object providing organized access to all conversion functions
 */
export const DomainGraphQLConverters: ConversionFunctionMap = {
    domainToGraphQL: {
        timesheet: convertTimesheetDomainToGraphQL,
        payStub: convertPayStubDomainToGraphQL,
        payStubDetail: convertPayStubDetailDomainToGraphQL,
        payStubArray: convertPayStubDomainArrayToGraphQL,
        detailArray: convertPayStubDetailDomainArrayToGraphQL,
        // Phase 2: New conversion functions
        addPayStub: convertDomainToAddInput,
        addPayStubDetail: convertDomainToAddDetailInput,
        deletePayStub: convertDomainToDeleteInput,
        deletePayStubDetail: convertDomainToDeleteDetailInput,
        movePayStub: convertDomainToMoveInput,
        addEmptyPayStub: convertDomainToAddEmptyPayStubInput
    },
    graphQLToDomain: {
        payStub: convertGraphQLToPayStubDomain,
        payStubDetail: convertGraphQLToPayStubDetailDomain
    },
    validation: {
        payStub: validatePayStubForGraphQLConversion,
        payStubDetail: validatePayStubDetailForGraphQLConversion
    }
};

export default DomainGraphQLConverters;
