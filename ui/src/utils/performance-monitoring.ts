/**
 * Performance Monitoring & Baselines - Phase 3 Implementation
 *
 * Comprehensive performance monitoring setup for production deployment
 * as specified in the Phase 3 implementation plan.
 */

import * as React from 'react';

/**
 * Performance metrics interface for timesheet operations
 */
export interface TimesheetPerformanceMetrics {
    // Component render metrics
    componentRenderTime: number;
    componentRenderCount: number;
    fragmentResolutionTime: number;

    // State management metrics
    zustandOperationTime: number;
    localStorageOperationTime: number;
    stateUpdateTime: number;

    // Memory metrics
    memoryUsage: number;
    memoryGrowth: number;

    // Network/mutation metrics
    mutationResponseTime: number;
    fragmentLoadTime: number;

    // User interaction metrics
    expandOperationTime: number;
    editOperationTime: number;
    saveOperationTime: number;

    // Timestamps
    timestamp: number;
    sessionId: string;
}

/**
 * Performance thresholds for monitoring alerts
 */
export const PERFORMANCE_THRESHOLDS = {
    // Component performance (ms)
    MAX_RENDER_TIME: 16, // ~60fps
    MAX_FRAGMENT_RESOLUTION_TIME: 50,
    MAX_COMPONENT_RENDERS_PER_SECOND: 10,

    // State management (ms)
    MAX_ZUSTAND_OPERATION_TIME: 5,
    MAX_LOCALSTORAGE_OPERATION_TIME: 10,
    MAX_STATE_UPDATE_TIME: 20,

    // Memory (bytes)
    MAX_MEMORY_USAGE: 50 * 1024 * 1024, // 50MB
    MAX_MEMORY_GROWTH_PER_OPERATION: 1024 * 1024, // 1MB

    // Network (ms)
    MAX_MUTATION_RESPONSE_TIME: 3000,
    MAX_FRAGMENT_LOAD_TIME: 1000,

    // User interactions (ms)
    MAX_EXPAND_OPERATION_TIME: 100,
    MAX_EDIT_OPERATION_TIME: 50,
    MAX_SAVE_OPERATION_TIME: 5000,

    // localStorage size
    MAX_LOCALSTORAGE_SIZE: 4 * 1024 * 1024 // 4MB browser limit
} as const;

/**
 * Performance monitoring class for timesheet operations
 */
export class TimesheetPerformanceMonitor {
    private metrics: TimesheetPerformanceMetrics[] = [];
    private sessionId: string;
    private performanceObserver?: PerformanceObserver;
    private memoryMonitoringInterval?: ReturnType<typeof setTimeout>;

    constructor() {
        this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.initializeMonitoring();
    }

    /**
     * Initialize performance monitoring systems
     */
    private initializeMonitoring(): void {
        // Set up Performance Observer for detailed metrics
        if (typeof PerformanceObserver !== 'undefined') {
            this.performanceObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                this.processPerformanceEntries(entries);
            });

            try {
                this.performanceObserver.observe({
                    entryTypes: ['measure', 'navigation', 'paint', 'mark']
                });
            } catch (error) {
                console.warn('Performance Observer setup failed:', error);
            }
        }

        // Set up memory monitoring
        if ('memory' in performance) {
            this.memoryMonitoringInterval = setInterval(() => {
                this.recordMemoryMetrics();
            }, 10000); // Check every 10 seconds
        }

        // Set up localStorage size monitoring
        this.monitorLocalStorageSize();
    }

    /**
     * Process performance entries from Performance Observer
     */
    private processPerformanceEntries(entries: PerformanceEntry[]): void {
        entries.forEach((entry) => {
            if (entry.name.includes('timesheet') || entry.name.includes('paystub')) {
                console.log(`Performance entry: ${entry.name} - ${entry.duration}ms`);

                // Check against thresholds
                if (entry.duration > PERFORMANCE_THRESHOLDS.MAX_RENDER_TIME) {
                    this.alertPerformanceIssue('render_time', entry.name, entry.duration);
                }
            }
        });
    }

    /**
     * Record memory usage metrics
     */
    private recordMemoryMetrics(): void {
        if ('memory' in performance) {
            const memoryInfo = (performance as { memory: { usedJSHeapSize: number } }).memory;
            const currentUsage = memoryInfo.usedJSHeapSize;

            // Check for memory growth
            const lastMetric = this.metrics[this.metrics.length - 1];
            if (lastMetric) {
                const memoryGrowth = currentUsage - lastMetric.memoryUsage;
                if (memoryGrowth > PERFORMANCE_THRESHOLDS.MAX_MEMORY_GROWTH_PER_OPERATION) {
                    this.alertPerformanceIssue('memory_growth', 'memory_monitoring', memoryGrowth);
                }
            }

            // Check total memory usage
            if (currentUsage > PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE) {
                this.alertPerformanceIssue('memory_usage', 'memory_monitoring', currentUsage);
            }
        }
    }

    /**
     * Monitor localStorage size and quota usage
     */
    private monitorLocalStorageSize(): void {
        try {
            const timesheetData = localStorage.getItem('timesheet-ui-storage');
            if (timesheetData) {
                const sizeInBytes = new Blob([timesheetData]).size;

                if (sizeInBytes > PERFORMANCE_THRESHOLDS.MAX_LOCALSTORAGE_SIZE * 0.8) {
                    this.alertPerformanceIssue('localstorage_size', 'storage_monitoring', sizeInBytes);
                }

                // Log size for monitoring
                console.log(`Timesheet localStorage size: ${(sizeInBytes / 1024).toFixed(2)} KB`);
            }
        } catch (error) {
            console.warn('localStorage size monitoring failed:', error);
        }
    }

    /**
     * Measure component render performance
     */
    measureComponentRender<T>(componentName: string, operation: () => T): T {
        const startTime = performance.now();
        performance.mark(`${componentName}_render_start`);

        try {
            const result = operation();

            const endTime = performance.now();
            performance.mark(`${componentName}_render_end`);
            performance.measure(`${componentName}_render_duration`, `${componentName}_render_start`, `${componentName}_render_end`);

            const duration = endTime - startTime;

            // Check threshold
            if (duration > PERFORMANCE_THRESHOLDS.MAX_RENDER_TIME) {
                this.alertPerformanceIssue('render_time', componentName, duration);
            }

            return result;
        } catch (error) {
            performance.mark(`${componentName}_render_error`);
            throw error;
        }
    }

    /**
     * Measure Zustand store operation performance
     */
    measureZustandOperation<T>(operationName: string, operation: () => T): T {
        const startTime = performance.now();
        performance.mark(`zustand_${operationName}_start`);

        const result = operation();

        const endTime = performance.now();
        performance.mark(`zustand_${operationName}_end`);
        performance.measure(`zustand_${operationName}_duration`, `zustand_${operationName}_start`, `zustand_${operationName}_end`);

        const duration = endTime - startTime;

        // Check threshold
        if (duration > PERFORMANCE_THRESHOLDS.MAX_ZUSTAND_OPERATION_TIME) {
            this.alertPerformanceIssue('zustand_operation', operationName, duration);
        }

        return result;
    }

    /**
     * Measure localStorage operation performance
     */
    measureLocalStorageOperation<T>(operationName: string, operation: () => T): T {
        const startTime = performance.now();

        const result = operation();

        const endTime = performance.now();
        const duration = endTime - startTime;

        // Check threshold
        if (duration > PERFORMANCE_THRESHOLDS.MAX_LOCALSTORAGE_OPERATION_TIME) {
            this.alertPerformanceIssue('localstorage_operation', operationName, duration);
        }

        return result;
    }

    /**
     * Measure user interaction performance
     */
    measureUserInteraction<T>(interactionType: 'expand' | 'edit' | 'save', operation: () => T): T {
        const startTime = performance.now();
        performance.mark(`user_${interactionType}_start`);

        const result = operation();

        const endTime = performance.now();
        performance.mark(`user_${interactionType}_end`);
        performance.measure(`user_${interactionType}_duration`, `user_${interactionType}_start`, `user_${interactionType}_end`);

        const duration = endTime - startTime;

        // Check appropriate threshold
        let threshold: number;
        switch (interactionType) {
            case 'expand':
                threshold = PERFORMANCE_THRESHOLDS.MAX_EXPAND_OPERATION_TIME;
                break;
            case 'edit':
                threshold = PERFORMANCE_THRESHOLDS.MAX_EDIT_OPERATION_TIME;
                break;
            case 'save':
                threshold = PERFORMANCE_THRESHOLDS.MAX_SAVE_OPERATION_TIME;
                break;
        }

        if (duration > threshold) {
            this.alertPerformanceIssue('user_interaction', interactionType, duration);
        }

        return result;
    }

    /**
     * Measure fragment resolution performance
     */
    measureFragmentResolution<T>(fragmentName: string, operation: () => T): T {
        const startTime = performance.now();
        performance.mark(`fragment_${fragmentName}_start`);

        const result = operation();

        const endTime = performance.now();
        performance.mark(`fragment_${fragmentName}_end`);
        performance.measure(`fragment_${fragmentName}_duration`, `fragment_${fragmentName}_start`, `fragment_${fragmentName}_end`);

        const duration = endTime - startTime;

        // Check threshold
        if (duration > PERFORMANCE_THRESHOLDS.MAX_FRAGMENT_RESOLUTION_TIME) {
            this.alertPerformanceIssue('fragment_resolution', fragmentName, duration);
        }

        return result;
    }

    /**
     * Alert on performance issues
     */
    private alertPerformanceIssue(type: string, operation: string, value: number): void {
        const alert = {
            type,
            operation,
            value,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            threshold: this.getThresholdForType(type)
        };

        console.warn('Performance threshold exceeded:', alert);

        // In production, send to monitoring service
        if (process.env.NODE_ENV === 'production') {
            this.sendToMonitoringService(alert);
        }
    }

    /**
     * Get threshold value for alert type
     */
    private getThresholdForType(type: string): number {
        switch (type) {
            case 'render_time':
                return PERFORMANCE_THRESHOLDS.MAX_RENDER_TIME;
            case 'zustand_operation':
                return PERFORMANCE_THRESHOLDS.MAX_ZUSTAND_OPERATION_TIME;
            case 'localstorage_operation':
                return PERFORMANCE_THRESHOLDS.MAX_LOCALSTORAGE_OPERATION_TIME;
            case 'user_interaction':
                return PERFORMANCE_THRESHOLDS.MAX_EXPAND_OPERATION_TIME;
            case 'fragment_resolution':
                return PERFORMANCE_THRESHOLDS.MAX_FRAGMENT_RESOLUTION_TIME;
            case 'memory_usage':
                return PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE;
            case 'memory_growth':
                return PERFORMANCE_THRESHOLDS.MAX_MEMORY_GROWTH_PER_OPERATION;
            case 'localstorage_size':
                return PERFORMANCE_THRESHOLDS.MAX_LOCALSTORAGE_SIZE;
            default:
                return 0;
        }
    }

    /**
     * Send performance alert to monitoring service
     */
    private sendToMonitoringService(alert: object): void {
        // Implementation would depend on monitoring service (e.g., DataDog, New Relic, etc.)
        try {
            // Example: send to monitoring endpoint
            fetch('/api/performance-alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(alert)
            }).catch((error) => {
                console.warn('Failed to send performance alert:', error);
            });
        } catch (error) {
            console.warn('Failed to send performance alert:', error);
        }
    }

    /**
     * Get performance summary for current session
     */
    getPerformanceSummary(): object {
        const recentMetrics = this.metrics.slice(-100); // Last 100 metrics

        if (recentMetrics.length === 0) {
            return { message: 'No performance metrics collected yet' };
        }

        return {
            sessionId: this.sessionId,
            metricsCount: recentMetrics.length,
            averageRenderTime: this.calculateAverage(recentMetrics, 'componentRenderTime'),
            averageStateUpdateTime: this.calculateAverage(recentMetrics, 'stateUpdateTime'),
            averageMemoryUsage: this.calculateAverage(recentMetrics, 'memoryUsage'),
            totalRenderCount: recentMetrics.reduce((sum, m) => sum + m.componentRenderCount, 0),
            timeRange: {
                start: new Date(recentMetrics[0].timestamp).toISOString(),
                end: new Date(recentMetrics[recentMetrics.length - 1].timestamp).toISOString()
            }
        };
    }

    /**
     * Calculate average for a metric field
     */
    private calculateAverage(metrics: TimesheetPerformanceMetrics[], field: keyof TimesheetPerformanceMetrics): number {
        const values = metrics.map((m) => m[field] as number).filter((v) => typeof v === 'number');
        return values.length > 0 ? values.reduce((sum, v) => sum + v, 0) / values.length : 0;
    }

    /**
     * Cleanup monitoring resources
     */
    destroy(): void {
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
        }

        if (this.memoryMonitoringInterval) {
            clearInterval(this.memoryMonitoringInterval);
        }
    }
}

// Global performance monitor instance
export const timesheetPerformanceMonitor = new TimesheetPerformanceMonitor();

/**
 * React hook for component performance monitoring
 */
export function usePerformanceMonitoring(componentName: string) {
    const [renderCount, setRenderCount] = React.useState(0);

    React.useEffect(() => {
        setRenderCount((count: number) => count + 1);

        // Record render in performance monitor
        const startTime = performance.now();

        return () => {
            const endTime = performance.now();
            const renderTime = endTime - startTime;

            if (renderTime > PERFORMANCE_THRESHOLDS.MAX_RENDER_TIME) {
                console.warn(`Component ${componentName} render exceeded threshold: ${renderTime}ms`);
            }
        };
    }, [componentName]);

    return {
        renderCount,
        measureOperation: <T>(operationName: string, operation: () => T): T => {
            return timesheetPerformanceMonitor.measureComponentRender(`${componentName}_${operationName}`, operation);
        }
    };
}

/**
 * Performance monitoring decorator for class methods
 */
export function performanceMonitored(target: object, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value as (...args: unknown[]) => unknown;

    descriptor.value = function (this: unknown, ...args: unknown[]) {
        const className = (target as { constructor: { name: string } }).constructor.name;
        const operationName = `${className}.${propertyName}`;

        return timesheetPerformanceMonitor.measureComponentRender(operationName, () => {
            return (method as (...args: unknown[]) => unknown).apply(this, args);
        });
    };
}
