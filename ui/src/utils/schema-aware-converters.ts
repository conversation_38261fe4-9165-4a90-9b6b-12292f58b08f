/**
 * Schema-Aware Converters
 * 
 * Bidirectional converters between schema-aware flat types and domain models.
 * These converters enable gradual migration from domain models to flat types
 * while maintaining backward compatibility.
 * 
 * Red-Team Recommendations Implemented:
 * - No shared object references (always return new objects)
 * - Strict field list reference via DETAIL_COLUMN_MAP
 * - Deep-equality preservation
 * - Unit test compatibility
 */

import type { 
  FlatPayStubDetailDraft, 
  FlatPayStubDraft,
  BaseUIFields,
  DetailUIFields,
  DETAIL_COLUMN_MAP,
  UI_FIELD_MAP 
} from '@/src/types';
import type { 
  PayStubDomainModel, 
  PayStubDetailDomainModel 
} from '@/src/types/timesheet-domain';
import type { TimeSheetDetailRow_payStubDetail$data } from '@/relay/__generated__/TimeSheetDetailRow_payStubDetail.graphql';
import type { PayStubTable_payStub$data } from '@/relay/__generated__/PayStubTable_payStub.graphql';

// =============================================================================
// GRAPHQL TO FLAT CONVERTERS
// =============================================================================

/**
 * Convert GraphQL fragment data to flat draft format
 * Used when initializing drafts from server data
 * Red-Team Recommendation: Reference DETAIL_COLUMN_MAP for field mapping
 */
export function graphqlToFlatDetail(
  graphqlData: TimeSheetDetailRow_payStubDetail$data
): FlatPayStubDetailDraft {
  // Red-Team Recommendation: Always return new objects to avoid mutations
  return {
    // Direct mapping from GraphQL fields using DETAIL_COLUMN_MAP reference
    id: graphqlData.id,
    payStubId: graphqlData.payStubId,
    workDate: graphqlData.workDate,
    jobCode: graphqlData.jobCode,
    agreementId: graphqlData.agreementId,
    classificationId: graphqlData.classificationId,
    subClassificationId: graphqlData.subClassificationId,
    hourlyRate: graphqlData.hourlyRate,
    stHours: graphqlData.stHours,
    otHours: graphqlData.otHours,
    dtHours: graphqlData.dtHours,
    bonus: graphqlData.bonus,
    expenses: graphqlData.expenses,
    earningsCode: graphqlData.earningsCode,
    costCenter: graphqlData.costCenter,
    
    // Initialize UI state with safe defaults
    _uiDelete: false,
    _uiValidationErrors: [],
    _uiLastModified: Date.now()
  };
}

/**
 * Convert GraphQL PayStub fragment to flat draft format
 * Used for PayStub-level operations
 */
export function graphqlToFlatPayStub(
  graphqlData: PayStubTable_payStub$data
): FlatPayStubDraft {
  // Red-Team Recommendation: Always return new objects
  return {
    // Direct mapping from GraphQL fields
    id: graphqlData.id,
    employeeId: graphqlData.employeeId,
    name: graphqlData.name,
    // Note: PayStub-level fields would be mapped here when available
    
    // Initialize UI state with safe defaults
    _uiDelete: false,
    _uiValidationErrors: [],
    _uiLastModified: Date.now(),
    _uiExpanded: false,
    _uiSelected: false,
    _uiEditingMode: 'view'
  };
}

// =============================================================================
// FLAT TO DOMAIN MODEL CONVERTERS
// =============================================================================

/**
 * Convert flat draft data to nested domain model format
 * Used when components need the traditional nested structure
 * Red-Team Recommendation: No shared object references
 */
export function flatToNestedPayStubDetail(
  flat: FlatPayStubDetailDraft
): Partial<PayStubDetailDomainModel> {
  // Red-Team Recommendation: Always return new objects via spreads
  return {
    id: flat.id || '',
    payStubId: flat.payStubId || '',
    reportLineItemId: undefined, // Not stored in flat draft
    
    // Date and naming
    workDate: flat.workDate || '',
    dayName: '', // Computed field - not stored in draft
    name: undefined, // Name field not available in flat draft
    
    // Hours worked - reconstruct nested structure
    hours: {
      standard: flat.stHours ?? 0,
      overtime: flat.otHours ?? 0,
      doubletime: flat.dtHours ?? 0,
      total: (flat.stHours ?? 0) + (flat.otHours ?? 0) + (flat.dtHours ?? 0)
    },
    
    // Job/classification information
    job: {
      jobCode: flat.jobCode || undefined,
      costCenter: flat.costCenter || undefined,
      hourlyRate: flat.hourlyRate || undefined
    },
    
    // Agreement and classification IDs
    agreements: {
      agreementId: flat.agreementId || undefined,
      classificationId: flat.classificationId || undefined,
      subClassificationId: flat.subClassificationId || undefined
    },
    
    // Monetary amounts
    amounts: {
      bonus: flat.bonus ?? 0,
      expenses: flat.expenses ?? 0
    },
    
    // Earnings classification
    earnings: {
      earningsCode: flat.earningsCode || undefined,
      earningsCodeText: undefined // Not stored in draft
    },
    
    // Employee reference (extracted from draft context)
    employeeId: '', // Employee ID handled at PayStub level, not detail level
    
    // UI state (convert from flat UI fields)
    ui: {
      isEditing: false, // EditingMode not available at detail level
      hasErrors: (flat._uiValidationErrors?.length ?? 0) > 0,
      isSelected: false, // Not applicable at detail level
      validationErrors: flat._uiValidationErrors || []
    }
  };
}

/**
 * Convert nested domain model to flat draft format
 * @deprecated This function is deprecated as part of Phase 5 cleanup.
 * Domain model conversion is no longer needed with direct flat types usage.
 * Will be removed in Phase 6.
 * Use flat types directly from @/src/types instead.
 */
export function nestedToFlatPayStubDetail(
  nested: Partial<PayStubDetailDomainModel>
): Partial<FlatPayStubDetailDraft> {
  // Red-Team Recommendation: Always return new objects
  return {
    id: nested.id,
    payStubId: nested.payStubId,
    workDate: nested.workDate,
    
    // Flatten nested fields using DETAIL_COLUMN_MAP reference
    stHours: nested.hours?.standard,
    otHours: nested.hours?.overtime,
    dtHours: nested.hours?.doubletime,
    
    jobCode: nested.job?.jobCode,
    costCenter: nested.job?.costCenter,
    hourlyRate: nested.job?.hourlyRate,
    
    agreementId: nested.agreements?.agreementId,
    classificationId: nested.agreements?.classificationId,
    subClassificationId: nested.agreements?.subClassificationId,
    
    bonus: nested.amounts?.bonus,
    expenses: nested.amounts?.expenses,
    
    earningsCode: nested.earnings?.earningsCode,
    
    // Extract UI state using UI_FIELD_MAP reference
    _uiDelete: false, // Deletion handled separately
    _uiValidationErrors: nested.ui?.validationErrors,
    
    // Add metadata
    _uiLastModified: Date.now(),
    _uiModifiedBy: 'system' // Could be from auth context
  };
}

// =============================================================================
// FLAT TO GRAPHQL INPUT CONVERTERS
// =============================================================================

/**
 * Convert flat draft to GraphQL mutation input
 * Used when sending changes to the server
 * Red-Team Recommendation: Remove UI-only fields before sending
 */
export function flatToGraphQLInput(
  flat: FlatPayStubDetailDraft
): Omit<FlatPayStubDetailDraft, keyof DetailUIFields> {
  // Red-Team Recommendation: Remove all UI-only fields before sending to server
  const {
    _uiDelete,
    _uiValidationErrors,
    _uiLastModified,
    _uiModifiedBy,
    _uiDraftId,
    _uiEditingCells,
    _uiIsAutoFilled,
    _uiAutoFillSource,
    ...graphqlFields
  } = flat;
  
  // Red-Team Recommendation: Always return new objects
  return { ...graphqlFields };
}

/**
 * Phase 2: Direct flat types to GraphQL mutation converter
 * Replaces legacy convertDomainToModifyInput for improved performance
 * 
 * This function converts flat PayStub and detail drafts directly to GraphQL input
 * without the intermediate domain model conversion step.
 */
export function flatToModifyInput(
  payStubDrafts: Map<string, FlatPayStubDraft>,
  detailDrafts: Map<string, FlatPayStubDetailDraft>,
  timesheetPrefix: string
): any[] {
  const result: any[] = [];

  // Group detail drafts by payStubId for efficient lookup
  const detailsByPayStubId = new Map<string, FlatPayStubDetailDraft[]>();
  for (const draft of Array.from(detailDrafts.values())) {
    if (draft.payStubId) {
      if (!detailsByPayStubId.has(draft.payStubId)) {
        detailsByPayStubId.set(draft.payStubId, []);
      }
      detailsByPayStubId.get(draft.payStubId)!.push(draft);
    }
  }

  // Create a unified set of all payStubIds that have changes
  const allPayStubIdsWithChanges = new Set<string>();
  for (const scopedKey of Array.from(payStubDrafts.keys())) {
    if (scopedKey.startsWith(timesheetPrefix)) {
      allPayStubIdsWithChanges.add(scopedKey.substring(timesheetPrefix.length));
    }
  }
  for (const detail of Array.from(detailDrafts.values())) {
    if (detail.payStubId) {
      allPayStubIdsWithChanges.add(detail.payStubId);
    }
  }

  // Iterate over the unified set of payStubIds
  for (const payStubId of Array.from(allPayStubIdsWithChanges)) {
    const scopedKey = `${timesheetPrefix}${payStubId}`;
    const payStubDraft = payStubDrafts.get(scopedKey) || {};
    const relatedDetails = detailsByPayStubId.get(payStubId) || [];

    // Convert flat PayStub to GraphQL input
    const payStubInput = {
      id: parsePayStubId(payStubId), // Use the ID from the unified set
      employeeId: payStubDraft.employeeId,
      name: payStubDraft.name,
      delete: payStubDraft._uiDelete === true,
      details: relatedDetails.map(detail => ({
        id: parseDetailId(detail.id),
        payStubId: parsePayStubId(detail.payStubId),
        workDate: detail.workDate,
        stHours: detail.stHours,
        otHours: detail.otHours,
        dtHours: detail.dtHours,
        bonus: detail.bonus,
        expenses: detail.expenses,
        jobCode: detail.jobCode,
        earningsCode: detail.earningsCode,
        agreementId: detail.agreementId,
        classificationId: detail.classificationId,
        subClassificationId: detail.subClassificationId,
        costCenter: detail.costCenter,
        hourlyRate: detail.hourlyRate,
        delete: detail._uiDelete === true
      }))
    };

    result.push(payStubInput);
  }

  return result;
}

/**
 * Parse PayStub ID to numeric format for GraphQL
 * Handles both temp IDs and actual numeric IDs
 */
function parsePayStubId(id: string | undefined): number {
  if (!id) return 0;
  
  // Handle temporary IDs (temp-timestamp-random)
  if (id.startsWith('temp-')) {
    return 0; // GraphQL will assign new ID
  }
  
  const numId = parseInt(id, 10);
  return Number.isFinite(numId) ? numId : 0;
}

/**
 * Parse Detail ID to numeric format for GraphQL
 * Handles both temp IDs and actual numeric IDs
 */
function parseDetailId(id: string | undefined): number {
  if (!id) return 0;
  
  // Handle temporary IDs
  if (id.startsWith('temp-')) {
    return 0; // GraphQL will assign new ID
  }
  
  const numId = parseInt(id, 10);
  return Number.isFinite(numId) ? numId : 0;
}

// =============================================================================
// BATCH CONVERSION UTILITIES
// =============================================================================

/**
 * Convert multiple flat drafts to domain models
 * Used for bulk operations
 */
export function batchFlatToNested(
  flats: FlatPayStubDetailDraft[]
): PayStubDetailDomainModel[] {
  // Red-Team Recommendation: No shared references
  return flats.map(flat => flatToNestedPayStubDetail(flat) as PayStubDetailDomainModel);
}

/**
 * Convert multiple domain models to flat drafts
 * Used for bulk initialization
 */
export function batchNestedToFlat(
  nesteds: PayStubDetailDomainModel[]
): FlatPayStubDetailDraft[] {
  // Red-Team Recommendation: No shared references
  return nesteds.map(nested => nestedToFlatPayStubDetail(nested) as FlatPayStubDetailDraft);
}

// =============================================================================
// VALIDATION AND SAFETY UTILITIES
// =============================================================================

/**
 * Validate that conversion preserves data integrity
 * Used in development for debugging
 */
export function validateConversionIntegrity(
  original: PayStubDetailDomainModel,
  converted: FlatPayStubDetailDraft
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate critical data fields are preserved
  if (original.id !== converted.id) {
    errors.push(`ID mismatch: ${original.id} !== ${converted.id}`);
  }
  
  if (original.hours?.standard !== converted.stHours) {
    errors.push(`Standard hours mismatch: ${original.hours?.standard} !== ${converted.stHours}`);
  }
  
  if (original.hours?.overtime !== converted.otHours) {
    errors.push(`Overtime hours mismatch: ${original.hours?.overtime} !== ${converted.otHours}`);
  }
  
  // Add more validation as needed...
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Deep clone utility to ensure no shared references
 * Red-Team Recommendation: Prevent accidental mutations
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T;
  }
  
  if (obj instanceof Set) {
    return new Set(Array.from(obj).map(item => deepClone(item))) as T;
  }
  
  if (obj instanceof Map) {
    const cloned = new Map();
    for (const [key, value] of Array.from(obj.entries())) {
      cloned.set(deepClone(key), deepClone(value));
    }
    return cloned as T;
  }
  
  // For plain objects
  const cloned = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

// =============================================================================
// TYPE COMPATIBILITY HELPERS
// =============================================================================

/**
 * Check if an object is a flat draft
 * Used for runtime type checking
 */
export function isFlatDraft(obj: unknown): obj is FlatPayStubDetailDraft {
  if (!obj || typeof obj !== 'object') return false;
  
  const draft = obj as Record<string, unknown>;
  
  // Check for flat structure (no nested objects except arrays)
  return Object.values(draft).every(value => 
    value === null ||
    value === undefined ||
    typeof value !== 'object' ||
    Array.isArray(value)
  );
}

/**
 * Check if an object is a domain model
 * Used for runtime type checking
 */
export function isDomainModel(obj: unknown): obj is PayStubDetailDomainModel {
  if (!obj || typeof obj !== 'object') return false;
  
  const model = obj as Record<string, unknown>;
  
  // Check for nested structure
  return (
    typeof model.hours === 'object' ||
    typeof model.amounts === 'object' ||
    typeof model.job === 'object'
  );
}