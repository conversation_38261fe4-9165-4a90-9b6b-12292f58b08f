/**
 * PayStub Validation Utilities
 * 
 * ## Overview
 * Comprehensive validation utilities for PayStub data consistency and integrity across the application.
 * This module centralizes the "meaningful data" detection logic that was previously duplicated across
 * multiple files, providing a single source of truth for PayStub validation rules.
 * 
 * ## Key Features
 * - **Security-First**: All validation includes security checks for injection attacks
 * - **Type Safety**: Full TypeScript integration with proper type guards
 * - **Performance**: Optimized algorithms to prevent O(n²) complexity
 * - **Comprehensive**: Supports both detail-based and header-only PayStub workflows
 * - **Extensible**: Easy to add new validation rules and error types
 * 
 * ## Validation Philosophy
 * The validation system follows a multi-layered approach:
 * 1. **Structure Validation**: Ensures data has the correct shape and types
 * 2. **Security Validation**: Prevents injection attacks and malicious data
 * 3. **Business Logic Validation**: Enforces domain-specific rules
 * 4. **Meaningful Data Detection**: Identifies PayStubs worth saving vs. empty placeholders
 * 
 * ## Usage Examples
 * 
 * ### Basic PayStub Validation
 * ```typescript
 * const payStub: PayStubUI = { name: "<PERSON>", details: [] };
 * 
 * if (isValidPayStub(payStub)) {
 *   // PayStub has meaningful data and can be saved
 *   await savePayStub(payStub);
 * } else {
 *   // PayStub is empty or invalid
 *   showEmptyPayStubWarning(payStub.name);
 * }
 * ```
 * 
 * ### Detailed Validation with Error Handling
 * ```typescript
 * const payStubs: PayStubUI[] = [{ name: "Employee1" }];
 * const errors = validatePayStubs(payStubs);
 * 
 * if (errors.length > 0) {
 *   const errorMessage = formatValidationErrorMessage(errors);
 *   showUserFriendlyError(errorMessage);
 * }
 * ```
 * 
 * ### Meaningful Data Detection
 * ```typescript
 * const detail: PayStubDetailUI = { id: "1", stHours: 8 };
 * 
 * if (hasDetailMeaningfulData(detail)) {
 *   // Detail has hours, rates, or other significant data
 *   includeInMutation(detail);
 * } else {
 *   // Detail is empty placeholder
 *   excludeFromMutation(detail);
 * }
 * ```
 * 
 * <AUTHOR> 3 - Enhanced security and validation
 * <AUTHOR> 5 - Final polish and documentation
 * @since 1.0.0
 * @version 2.1.0 - Enhanced with security validations and comprehensive documentation
 */

import type { PayStubUI, PayStubDetailUI } from '../types/timesheet-detail';
import { LABEL_TEXT } from '../constants/text';
import { sanitizeUserInput, sanitizeNumericInput, validateForSQLInjection, sanitizeEmployeeName } from './securityUtils';

// Validation configuration constants with security and performance considerations

/**
 * Validates string fields for meaningful data content and security threats.
 * 
 * This function performs comprehensive validation including:
 * - Null/undefined safety checks
 * - Empty string detection after trimming
 * - SQL injection pattern detection
 * - XSS attack pattern identification
 * 
 * @param value - String value to validate
 * @returns true if the string contains meaningful, safe data
 * 
 * @example
 * ```typescript
 * validateStringField("  ") // false - empty after trim
 * validateStringField("ABC123") // true - valid content
 * validateStringField("'; DROP TABLE --") // false - SQL injection
 * validateStringField(null) // false - null value
 * ```
 * 
 * @since 2.0.0
 */
function validateStringField(value: string | null | undefined): boolean {
    if (!value || typeof value !== 'string') {
        return false;
    }
    
    const trimmed = value.trim();
    if (trimmed === '') {
        return false;
    }
    
    // Check for SQL injection patterns
    if (!validateForSQLInjection(value)) {
        console.warn('[SECURITY] Potential SQL injection detected in string field:', value.slice(0, 50));
        return false;
    }
    
    return true;
}

/**
 * Validates reference ID values for database compatibility and security.
 * 
 * Ensures that reference IDs (like agreementId, classificationId) are:
 * - Valid positive integers
 * - Within safe integer bounds
 * - Not potentially malicious values
 * 
 * @param value - Numeric ID value to validate
 * @returns true if the ID is valid and safe for database operations
 * 
 * @example
 * ```typescript
 * isValidReferenceId(123) // true - valid positive integer
 * isValidReferenceId(0) // false - zero is not valid
 * isValidReferenceId(-1) // false - negative values not allowed
 * isValidReferenceId(Number.MAX_SAFE_INTEGER + 1) // false - too large
 * isValidReferenceId(null) // false - null value
 * ```
 * 
 * @since 2.0.0
 */
function isValidReferenceId(value: number | null | undefined): boolean {
    if (value === null || value === undefined) {
        return false;
    }
    
    if (typeof value !== 'number' || !Number.isInteger(value)) {
        return false;
    }
    
    // Ensure ID is within safe bounds
    if (value < 0 || value > Number.MAX_SAFE_INTEGER) {
        return false;
    }
    
    return true;
}

/**
 * Centralized validation constants and configuration values.
 * 
 * ## Purpose
 * This configuration object centralizes all validation thresholds, limits, and magic numbers
 * to ensure consistency across the application and make maintenance easier.
 * 
 * ## Configuration Categories
 * 
 * ### Numeric Thresholds
 * - **MIN_MEANINGFUL_VALUE**: Minimum value for numbers to be considered significant
 * - **MAX_EMPLOYEE_NAME_LENGTH**: Security limit for employee name fields
 * - **MAX_ERRORS_TO_SHOW**: UI limit for displayed validation errors
 * 
 * ### System Limits
 * - **DEFAULT_GRAPHQL_FIRST_LIMIT**: Standard pagination limit for GraphQL queries
 * - **CLIENT_ID_PREFIX**: Identifier prefix for client-generated temporary IDs
 * 
 * ### Field Classifications
 * - **MEANINGFUL_DETAIL_FIELDS**: Fields that make a detail record significant
 * - **MEANINGFUL_REFERENCE_FIELDS**: Reference ID fields that indicate meaningful relationships
 * 
 * ## Configuration Benefits
 * - **Consistency**: Same validation rules across all components
 * - **Maintainability**: Single place to update business rules
 * - **Testability**: Easy to modify for testing different scenarios
 * - **Documentation**: Self-documenting validation logic
 * 
 * @since 1.0.0
 * @version 2.1.0 - Expanded with additional validation constants
 */
export const VALIDATION_CONSTANTS = {
    /**
     * Minimum threshold for numeric values to be considered meaningful
     */
    MIN_MEANINGFUL_VALUE: 0,
    
    /**
     * Maximum length for employee names in validation
     */
    MAX_EMPLOYEE_NAME_LENGTH: 100,
    
    /**
     * Maximum number of validation errors to show in detailed messages
     */
    MAX_ERRORS_TO_SHOW: 5,
    
    /**
     * Default limit for GraphQL first parameter in queries
     */
    DEFAULT_GRAPHQL_FIRST_LIMIT: 500,
    
    /**
     * Client ID prefix used for temporary/optimistic updates
     */
    CLIENT_ID_PREFIX: 'client:',
    
    /**
     * Fields that are considered when determining if a PayStub detail has meaningful data
     */
    MEANINGFUL_DETAIL_FIELDS: [
        'stHours',
        'otHours', 
        'dtHours',
        'bonus',
        'expenses',
        'jobCode',
        'costCenter',
        'earningsCode'
    ] as const,
    
    /**
     * Fields that are considered when determining if a PayStub has meaningful total hours
     * Note: Deprecated - header-only PayStubs no longer supported
     */
    MEANINGFUL_HEADER_FIELDS: [
        // 'totalHours' - removed as header-only PayStubs are no longer supported
    ] as const,
    
    /**
     * Reference ID fields that are meaningful when set (any selection is meaningful)
     */
    MEANINGFUL_REFERENCE_FIELDS: [
        'agreementId',
        'classificationId', 
        'subClassificationId'
    ] as const
} as const;

export type MeaningfulDetailField = typeof VALIDATION_CONSTANTS.MEANINGFUL_DETAIL_FIELDS[number];
export type MeaningfulHeaderField = typeof VALIDATION_CONSTANTS.MEANINGFUL_HEADER_FIELDS[number];

/**
 * Checks if a PayStub detail contains meaningful data.
 * 
 * A detail is considered to have meaningful data if it has:
 * - Any hours > 0 (ST, OT, DT)
 * - Any financial values > 0 (bonus, expenses)
 * - Any non-empty string fields (jobCode, costCenter, earningsCode)
 * - Any reference IDs (agreementId, classificationId, subClassificationId)
 * 
 * @param detail - The PayStub detail to check
 * @returns true if the detail contains meaningful data, false otherwise
 */
export function hasDetailMeaningfulData(detail: PayStubDetailUI): boolean {
    if (!detail) {
        return false;
    }

    // Safely validate and sanitize numeric fields
    const hasNumericData = (
        (sanitizeNumericInput(detail.stHours) ?? 0) > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE ||
        (sanitizeNumericInput(detail.otHours) ?? 0) > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE ||
        (sanitizeNumericInput(detail.dtHours) ?? 0) > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE ||
        (sanitizeNumericInput(detail.bonus) ?? 0) > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE ||
        (sanitizeNumericInput(detail.expenses) ?? 0) > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE
    );

    // Check string fields (non-empty, valid, and not SQL injection attempts)
    const hasStringData = (
        (detail.jobCode && validateStringField(detail.jobCode)) ||
        (detail.costCenter && validateStringField(detail.costCenter)) ||
        (detail.earningsCode && validateStringField(detail.earningsCode))
    );

    // Check reference IDs with safe validation
    const hasReferenceData = (
        isValidReferenceId(detail.agreementId) ||
        isValidReferenceId(detail.classificationId) ||
        isValidReferenceId(detail.subClassificationId)
    );

    return hasNumericData || hasStringData || hasReferenceData;
}

/**
 * Checks if a PayStub has meaningful details.
 * 
 * @param payStub - The PayStub to check
 * @returns true if the PayStub has at least one detail with meaningful data, false otherwise
 */
export function hasPayStubMeaningfulDetails(payStub: PayStubUI): boolean {
    if (!payStub.details || payStub.details.length === 0) {
        return false;
    }

    return payStub.details.some(detail => hasDetailMeaningfulData(detail));
}

/**
 * Checks if a PayStub has meaningful header-level data.
 * Supports the new header-only PayStub workflow where hours can be entered at the header level.
 * 
 * @param payStub - The PayStub to check
 * @returns true if the PayStub has meaningful header-level data
 */
export function hasPayStubMeaningfulHeaderData(payStub: PayStubUI): boolean {
    if (!payStub) {
        return false;
    }

    // Check for meaningful header-level hours or financial data
    // In the new workflow, these can be populated from detail totals or entered directly
    const hasHeaderHours = (
        isMeaningfulNumber(payStub.stHours) ||
        isMeaningfulNumber(payStub.otHours) ||
        isMeaningfulNumber(payStub.dtHours)
    );

    const hasHeaderFinancials = (
        isMeaningfulNumber(payStub.bonus) ||
        isMeaningfulNumber(payStub.expenses)
    );

    return hasHeaderHours || hasHeaderFinancials;
}

/**
 * Legacy function for backward compatibility.
 * @deprecated Use hasPayStubMeaningfulHeaderData instead
 */
export function hasPayStubMeaningfulTotalHours(payStub: PayStubUI): boolean {
    return hasPayStubMeaningfulHeaderData(payStub);
}

/**
 * Validates if a PayStub has meaningful data (header-level or details).
 * PayStubs are valid if they have either:
 * 1. Meaningful detail rows, OR
 * 2. Meaningful header-level data (supporting header-only workflow)
 * 
 * @param payStub - The PayStub to validate
 * @returns true if the PayStub has meaningful data, false otherwise
 */
export function isValidPayStub(payStub: PayStubUI): boolean {
    // Skip validation for PayStubs marked for deletion
    if (payStub.delete) {
        return true;
    }

    // PayStub is valid if it has meaningful details OR meaningful header data
    return hasPayStubMeaningfulDetails(payStub) || hasPayStubMeaningfulHeaderData(payStub);
}

/**
 * Checks if a PayStub is completely empty (no header data AND no detail data).
 * This is used to detect PayStubs that should be flagged for user attention.
 * 
 * @param payStub - The PayStub to check
 * @returns true if the PayStub is completely empty, false otherwise
 */
export function isCompletelyEmptyPayStub(payStub: PayStubUI): boolean {
    // Skip check for PayStubs marked for deletion
    if (payStub.delete) {
        return false;
    }

    return !hasPayStubMeaningfulDetails(payStub) && !hasPayStubMeaningfulHeaderData(payStub);
}

/**
 * Validation error details for better error reporting.
 */
export interface PayStubValidationError {
    index: number;
    employeeName: string;
    message: string;
    type: 'error' | 'warning';
}

/**
 * Validates multiple PayStubs and returns detailed error information.
 * 
 * @param payStubs - Array of PayStubs to validate
 * @returns Array of validation errors, empty if all PayStubs are valid
 */
export function validatePayStubs(payStubs: readonly PayStubUI[]): PayStubValidationError[] {
    const errors: PayStubValidationError[] = [];

    if (!Array.isArray(payStubs)) {
        errors.push({
            index: -1,
            employeeName: 'System',
            message: 'Invalid payStubs data: Expected array',
            type: 'error'
        });
        return errors;
    }

    payStubs.forEach((payStub, index) => {
        // Validate payStub structure and sanitize data
        if (!payStub || typeof payStub !== 'object') {
            errors.push({
                index,
                employeeName: 'Unknown',
                message: 'Invalid payStub structure',
                type: 'error'
            });
            return;
        }

        if (!isValidPayStub(payStub)) {
            const sanitizedEmployeeName = sanitizeEmployeeName(payStub.name);
            const baseMessage = 'PayStub for ' + sanitizedEmployeeName + ' ' + LABEL_TEXT.PAYSTUB_MEANINGFUL_DATA_MESSAGE.toLowerCase();
            const helpMessage = '\n\nHint: ' + LABEL_TEXT.PAYSTUB_NO_MEANINGFUL_DATA_HINT;
            
            errors.push({
                index,
                employeeName: sanitizedEmployeeName,
                message: sanitizeUserInput(baseMessage + helpMessage),
                type: 'error'
            });
        }
    });

    return errors;
}

/**
 * Creates a detailed error message from validation errors.
 * 
 * @param errors - Array of validation errors
 * @param context - Additional context for the error message
 * @returns Formatted error message string
 */
export function formatValidationErrorMessage(
    errors: PayStubValidationError[], 
    context: string = LABEL_TEXT.PAYSTUB_VALIDATION_FAILED
): string {
    if (errors.length === 0) {
        return '';
    }

    const errorMessages = errors.map(error => '• ' + error.message).join('\n\n');
    const helpText = '\n\n' + LABEL_TEXT.PAYSTUB_VALIDATION_HELP;
    return context + ':\n\n' + errorMessages + helpText;
}

/**
 * Type guard to check if a value represents meaningful numeric data.
 * 
 * @param value - The value to check
 * @returns true if the value is a meaningful number (> 0), false otherwise
 */
export function isMeaningfulNumber(value: number | null | undefined): value is number {
    return typeof value === 'number' && value > VALIDATION_CONSTANTS.MIN_MEANINGFUL_VALUE;
}

/**
 * Type guard to check if a value represents meaningful string data.
 * 
 * @param value - The value to check
 * @returns true if the value is a non-empty string (after trimming), false otherwise
 */
export function isMeaningfulString(value: string | null | undefined): value is string {
    return typeof value === 'string' && value.trim() !== '';
}

/**
 * Gets a summary of what makes a PayStub meaningful for debugging purposes.
 * 
 * @param payStub - The PayStub to analyze
 * @returns Object with boolean flags indicating what meaningful data was found
 */
export function getPayStubMeaningfulDataSummary(payStub: PayStubUI) {
    return {
        hasMeaningfulDetails: hasPayStubMeaningfulDetails(payStub),
        hasMeaningfulTotalHours: hasPayStubMeaningfulTotalHours(payStub),
        isValid: isValidPayStub(payStub),
        detailCount: payStub.details?.length ?? 0,
        meaningfulDetailCount: payStub.details?.filter(hasDetailMeaningfulData).length ?? 0,
        // totalHours: computed from details on backend
    };
}

/**
 * Interface for payload objects used in mutation inputs.
 * This covers both AddPayStubDetailInput and ModifyPayStubDetailInput types.
 */
export interface PayStubDetailPayload {
    id?: string | undefined;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    hourlyRate?: number | null;
    jobCode?: string | null;
    costCenter?: string | null;
    earningsCode?: string | null;
    agreementId?: number | null;
    classificationId?: number | null;
    subClassificationId?: number | null;
}

/**
 * Interface for PayStub payloads used in mutation inputs.
 * This covers both AddPayStubInput and ModifyPayStubInput types.
 * Now supports header-level fields for the header-only PayStub workflow.
 */
export interface PayStubPayload {
    id?: string | undefined;
    employeeId?: string; // Global ID format (e.g., "RW1wbG95ZWU6MQ==")
    name?: string | null;
    // Header-level fields for header-only PayStub support
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    // totalHours removed - now computed from details on backend
    details?: PayStubDetailPayload[] | null;
    delete?: boolean;
}

/**
 * Checks if a detail payload contains meaningful data for mutation purposes.
 * This is the extracted logic that was previously duplicated in useTimesheetSaver.
 * 
 * @param payload - The detail payload to check
 * @returns true if the payload contains meaningful data, false otherwise
 */
export function hasDetailPayloadMeaningfulData(payload: PayStubDetailPayload): boolean {
    if (!payload) {
        return false;
    }

    // Check numeric fields for meaningful values
    const hasNumericData = (
        isMeaningfulNumber(payload.stHours) ||
        isMeaningfulNumber(payload.otHours) ||
        isMeaningfulNumber(payload.dtHours) ||
        isMeaningfulNumber(payload.bonus) ||
        isMeaningfulNumber(payload.expenses) ||
        isMeaningfulNumber(payload.hourlyRate)
    );

    // Check string fields for meaningful values
    const hasStringData = (
        isMeaningfulString(payload.jobCode) ||
        isMeaningfulString(payload.costCenter) ||
        isMeaningfulString(payload.earningsCode)
    );

    // Check reference IDs (any selection is meaningful)
    const hasReferenceData = (
        payload.agreementId !== null && payload.agreementId !== undefined ||
        payload.classificationId !== null && payload.classificationId !== undefined ||
        payload.subClassificationId !== null && payload.subClassificationId !== undefined
    );

    return hasNumericData || hasStringData || hasReferenceData;
}

/**
 * Checks if a PayStub payload has meaningful header-level data for mutation purposes.
 * Supports the new header-only PayStub workflow.
 * 
 * @param payload - The PayStub payload to check
 * @returns true if the payload has meaningful header-level data
 */
export function hasPayStubPayloadMeaningfulHeaderData(payload: PayStubPayload): boolean {
    if (!payload) {
        return false;
    }

    // Check for meaningful header-level hours or financial data
    const hasHeaderHours = (
        isMeaningfulNumber(payload.stHours) ||
        isMeaningfulNumber(payload.otHours) ||
        isMeaningfulNumber(payload.dtHours)
    );

    const hasHeaderFinancials = (
        isMeaningfulNumber(payload.bonus) ||
        isMeaningfulNumber(payload.expenses)
    );

    return hasHeaderHours || hasHeaderFinancials;
}

/**
 * Legacy function for backward compatibility.
 * @deprecated Use hasPayStubPayloadMeaningfulHeaderData instead
 */
export function hasPayStubPayloadMeaningfulTotalHours(payload: PayStubPayload): boolean {
    return hasPayStubPayloadMeaningfulHeaderData(payload);
}

/**
 * Checks if a PayStub payload is completely empty (no header data AND no detail data).
 * 
 * @param payload - The PayStub payload to check
 * @returns true if the payload is completely empty, false otherwise
 */
export function isCompletelyEmptyPayStubPayload(payload: PayStubPayload): boolean {
    if (!payload) {
        return true;
    }

    // Skip check for PayStubs marked for deletion
    if (payload.delete) {
        return false;
    }

    // Check if has meaningful header data
    if (hasPayStubPayloadMeaningfulHeaderData(payload)) {
        return false;
    }

    // Check if has meaningful details
    if (payload.details && payload.details.length > 0) {
        return !payload.details.some(detail => hasDetailPayloadMeaningfulData(detail));
    }

    return true;
}

/**
 * Checks if a detail ID represents a database-generated ID (not client-generated).
 * 
 * @param id - The ID to check
 * @returns true if the ID is from the database, false if client-generated or missing
 */
export function isDatabaseId(id: string | undefined): boolean {
    return !!id && !id.startsWith(VALIDATION_CONSTANTS.CLIENT_ID_PREFIX);
}

/**
 * Checks if an employeeId is in valid Global ID format.
 * Global IDs are base64-encoded strings like "RW1wbG95ZWU6MQ=="
 * 
 * @param employeeId - The employee ID to check
 * @returns true if the ID is in Global ID format, false otherwise
 */
export function isGlobalIdFormat(employeeId: string | number | null | undefined): employeeId is string {
    if (typeof employeeId !== 'string' || !employeeId) {
        return false;
    }

    // Basic check for base64-like string (contains letters, numbers, +, /, and may end with =)
    const base64Pattern = /^[A-Za-z0-9+/]+(=*)$/;
    return base64Pattern.test(employeeId) && employeeId.length > 4;
}

/**
 * Normalizes an employee ID to Global ID format if needed.
 * 
 * @param employeeId - The employee ID to normalize
 * @returns Global ID string or null if invalid
 */
export function normalizeEmployeeIdToGlobalId(employeeId: string | number | null | undefined): string | null {
    if (!employeeId) {
        return null;
    }

    // If already in Global ID format, return as-is
    if (isGlobalIdFormat(employeeId)) {
        return employeeId;
    }

    // For numeric IDs, we can't convert without the RelayIdService
    // This should be handled at the component level
    if (typeof employeeId === 'number') {
        console.warn('[PayStub Validation] Numeric employee ID needs conversion to Global ID format:', employeeId);
        return String(employeeId); // Return as string for now
    }

    return String(employeeId);
}

/**
 * Determines if a detail payload should be included in a mutation based on 
 * whether it's a new timesheet or modification and whether it has meaningful data.
 * 
 * @param payload - The detail payload to check
 * @param isNewTimesheet - Whether this is for a new timesheet (true) or modification (false)
 * @returns true if the detail should be included in the mutation, false otherwise
 */
export function shouldIncludeDetailInMutation(
    payload: PayStubDetailPayload, 
    isNewTimesheet: boolean
): boolean {
    if (isNewTimesheet) {
        // For new timesheets, only send details with meaningful data
        return hasDetailPayloadMeaningfulData(payload);
    } else {
        // For modify operations: Keep if it has a DB ID OR if it has meaningful data
        return isDatabaseId(payload.id) || hasDetailPayloadMeaningfulData(payload);
    }
}

/**
 * Determines if a PayStub payload should be included in a mutation.
 * Supports both detail-based and header-only PayStub workflows.
 * 
 * @param payload - The PayStub payload to check
 * @param isNewTimesheet - Whether this is for a new timesheet
 * @returns true if the PayStub should be included, false if it should be filtered
 */
export function shouldIncludePayStubInMutation(
    payload: PayStubPayload, 
    isNewTimesheet: boolean
): boolean {
    // Always include PayStubs marked for deletion
    if (payload.delete) {
        return true;
    }

    // Include if has meaningful header data
    if (hasPayStubPayloadMeaningfulHeaderData(payload)) {
        return true;
    }

    // Include if has meaningful details
    if (payload.details && payload.details.length > 0) {
        return payload.details.some(detail => hasDetailPayloadMeaningfulData(detail));
    }

    // For modify operations, include PayStubs with database IDs even if empty
    // (so they can be updated/deleted on the server)
    if (!isNewTimesheet && isDatabaseId(payload.id)) {
        return true;
    }

    // Otherwise exclude completely empty PayStubs
    return false;
}