/**
 * Legacy Bridge Module
 * 
 * This module contains domain model conversion utilities that will be removed in Phase 5.
 * It bridges the gap between the old domain model types and the new flat types during 
 * the migration period.
 * 
 * ⚠️ TEMPORARY MODULE - WILL BE DELETED IN PHASE 5
 * 
 * Critical Review Fix: Extracted from timesheetUIStore.ts to isolate domain type dependencies
 * and prepare for eventual removal of PayStubDomainModel imports.
 */

import type { PayStubDomainModel } from '@/src/types/timesheet-domain';
import type { FlatPayStubDraft, FlatPayStubDetailDraft } from '@/src/types';

// =============================================================================
// DOMAIN MODEL TO FLAT TYPE CONVERTERS
// =============================================================================

/**
 * Convert domain model PayStub draft to flat PayStub draft
 * 
 * ⚠️ LEGACY FUNCTION - Will be removed in Phase 5
 * 
 * Critical Review Fix: Improved employeeId handling with validation
 * Note: Only PayStub-level fields, not detail fields
 */
export function convertDomainToFlatPayStub(
    domain: Partial<PayStubDomainModel>
): FlatPayStubDraft {
    // Parse employeeId safely - handle both string and number inputs
    const employeeIdNum = typeof domain.employeeId === 'string' 
        ? parseInt(domain.employeeId, 10) 
        : (domain.employeeId || 0);
    
    // Validate the result to ensure it's a valid number
    const validEmployeeId = Number.isFinite(employeeIdNum) ? employeeIdNum : 0;
    
    return {
        id: domain.id || '',
        employeeId: domain.employeeId || '', // Keep as Global ID string
        name: domain.name || '',
        totalHours: domain.hours?.total || 0,
        // Details array is read-only from GraphQL and should not be in drafts
        _uiLastModified: Date.now()
    };
}

// migrateLegacyDraftChanges was removed - unused function

/**
 * Migration utility for legacy PayStub drafts - convert domain model to flat types
 * 
 * ⚠️ LEGACY FUNCTION - Will be removed in Phase 5
 */
export function migrateLegacyPayStubDrafts(
    legacyPayStubDrafts: Map<string, Partial<PayStubDomainModel>>
): Map<string, FlatPayStubDraft> {
    const migrated = new Map<string, FlatPayStubDraft>();
    
    // Use Array.from to avoid iterator issues
    const entries = Array.from(legacyPayStubDrafts.entries());
    for (const [key, domainDraft] of entries) {
        const flatDraft = convertDomainToFlatPayStub(domainDraft);
        migrated.set(key, flatDraft);
    }
    
    return migrated;
}

// =============================================================================
// EMPLOYEE ID UTILITIES
// =============================================================================

/**
 * Safe employee ID parsing for addNewPayStub operations
 * 
 * ⚠️ LEGACY FUNCTION - Will be removed in Phase 5
 * 
 * Critical Review Fix: Handles both string IDs and numeric IDs safely
 */
export function parseEmployeeIdSafely(employeeId: string): number {
    const employeeIdNum = parseInt(employeeId, 10);
    return Number.isFinite(employeeIdNum) ? employeeIdNum : 0;
}

// =============================================================================
// TYPE GUARDS FOR MIGRATION
// =============================================================================

/**
 * Type guard to check if data is in domain model format
 * 
 * ⚠️ LEGACY FUNCTION - Will be removed in Phase 5
 */
export function isDomainModelDraft(obj: unknown): obj is Partial<PayStubDomainModel> {
    if (!obj || typeof obj !== 'object') return false;
    
    const draft = obj as Record<string, unknown>;
    
    // Check for domain model structure (nested objects)
    return (
        typeof draft.hours === 'object' ||
        typeof draft.amounts === 'object' ||
        typeof draft.job === 'object' ||
        typeof draft.ui === 'object'
    );
}

/**
 * Type guard to check if data is in flat draft format
 * 
 * ⚠️ LEGACY FUNCTION - Will be removed in Phase 5
 */
export function isFlatDraft(obj: unknown): obj is FlatPayStubDraft {
    if (!obj || typeof obj !== 'object') return false;
    
    const draft = obj as Record<string, unknown>;
    
    // Check for flat structure (no nested objects except arrays)
    return Object.values(draft).every(value => 
        value === null ||
        value === undefined ||
        typeof value !== 'object' ||
        Array.isArray(value)
    );
}

// =============================================================================
// DEPRECATION WARNINGS
// =============================================================================

/**
 * Log deprecation warning for legacy function usage
 * Only logs in development to avoid production noise
 */
function logDeprecationWarning(functionName: string): void {
    if (process.env.NODE_ENV === 'development') {
        console.warn(
            `[LEGACY-BRIDGE] ⚠️  ${functionName} is deprecated and will be removed in Phase 5. ` +
            'Consider migrating to flat types directly.'
        );
    }
}

// Add deprecation warnings to exported functions
const originalConvert = convertDomainToFlatPayStub;
export const convertDomainToFlatPayStubWithWarning = (domain: Partial<PayStubDomainModel>): FlatPayStubDraft => {
    logDeprecationWarning('convertDomainToFlatPayStub');
    return originalConvert(domain);
};