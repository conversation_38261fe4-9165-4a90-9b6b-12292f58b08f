/**
 * Simple hash utility for creating deterministic hashes of strings
 * Used for PII correlation without exposing actual values
 */

/**
 * Creates a simple hash of a string using djb2 algorithm
 * This is not cryptographically secure but sufficient for correlation
 */
export function hash(str: string): string {
    let hash = 5381;
    
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) + hash) + char; // hash * 33 + char
        hash = hash & hash; // Convert to 32-bit integer
    }
    
    // Convert to positive hex string, padded to 8 characters
    return Math.abs(hash).toString(16).toUpperCase().padStart(8, '0');
}

/**
 * Creates a consistent hash for any value by converting to string first
 */
export function hashValue(value: any): string {
    if (value == null) {
        return hash('null');
    }
    
    if (typeof value === 'object') {
        // Use stable JSON stringify for objects
        return hash(JSON.stringify(value, Object.keys(value).sort()));
    }
    
    return hash(String(value));
}