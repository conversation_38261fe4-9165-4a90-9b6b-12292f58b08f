/**
 * Concurrency Utilities
 * 
 * Utilities to prevent race conditions, manage concurrent operations,
 * and ensure data consistency in timesheet operations.
 */

import { useRef, useCallback, useEffect, useState } from 'react';

/**
 * Mutex implementation for JavaScript to prevent race conditions
 */
export class Mutex {
    private locked = false;
    private queue: Array<() => void> = [];
    
    async acquire(): Promise<() => void> {
        return new Promise<() => void>((resolve) => {
            if (!this.locked) {
                this.locked = true;
                resolve(() => this.release());
            } else {
                this.queue.push(() => resolve(() => this.release()));
            }
        });
    }
    
    private release(): void {
        const next = this.queue.shift();
        if (next) {
            next();
        } else {
            this.locked = false;
        }
    }
    
    isLocked(): boolean {
        return this.locked;
    }
    
    getQueueLength(): number {
        return this.queue.length;
    }
}

/**
 * Read-Write lock implementation for better concurrency
 */
export class ReadWriteLock {
    private readers = 0;
    private writing = false;
    private writerQueue: Array<() => void> = [];
    private readerQueue: Array<() => void> = [];
    
    async acquireRead(): Promise<() => void> {
        return new Promise<() => void>((resolve) => {
            if (!this.writing && this.writerQueue.length === 0) {
                this.readers++;
                resolve(() => this.releaseRead());
            } else {
                this.readerQueue.push(() => {
                    this.readers++;
                    resolve(() => this.releaseRead());
                });
            }
        });
    }
    
    async acquireWrite(): Promise<() => void> {
        return new Promise<() => void>((resolve) => {
            if (!this.writing && this.readers === 0) {
                this.writing = true;
                resolve(() => this.releaseWrite());
            } else {
                this.writerQueue.push(() => {
                    this.writing = true;
                    resolve(() => this.releaseWrite());
                });
            }
        });
    }
    
    private releaseRead(): void {
        this.readers--;
        if (this.readers === 0 && this.writerQueue.length > 0) {
            const next = this.writerQueue.shift();
            if (next) next();
        }
    }
    
    private releaseWrite(): void {
        this.writing = false;
        
        // Priority to waiting writers to prevent writer starvation
        if (this.writerQueue.length > 0) {
            const nextWriter = this.writerQueue.shift();
            if (nextWriter) nextWriter();
        } else {
            // Allow all waiting readers
            while (this.readerQueue.length > 0) {
                const nextReader = this.readerQueue.shift();
                if (nextReader) nextReader();
            }
        }
    }
    
    getStats(): { readers: number; writing: boolean; writerQueue: number; readerQueue: number } {
        return {
            readers: this.readers,
            writing: this.writing,
            writerQueue: this.writerQueue.length,
            readerQueue: this.readerQueue.length
        };
    }
}

/**
 * Operation queue to serialize operations and prevent race conditions
 */
export class OperationQueue {
    private queue: Array<() => Promise<void>> = [];
    private processing = false;
    
    async enqueue<T>(operation: () => Promise<T>): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            this.queue.push(async () => {
                try {
                    const result = await operation();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
            
            this.process();
        });
    }
    
    private async process(): Promise<void> {
        if (this.processing || this.queue.length === 0) {
            return;
        }
        
        this.processing = true;
        
        while (this.queue.length > 0) {
            const operation = this.queue.shift();
            if (operation) {
                try {
                    await operation();
                } catch (error) {
                    console.error('[CONCURRENCY] Operation failed in queue:', error);
                }
            }
        }
        
        this.processing = false;
    }
    
    getQueueLength(): number {
        return this.queue.length;
    }
    
    isProcessing(): boolean {
        return this.processing;
    }
    
    clear(): void {
        this.queue = [];
    }
}

/**
 * Optimistic locking implementation with version control
 */
export class OptimisticLock<T> {
    private data: T;
    private version: number = 0;
    private observers: Array<(data: T, version: number) => void> = [];
    
    constructor(initialData: T) {
        this.data = initialData;
    }
    
    read(): { data: T; version: number } {
        return {
            data: this.data,
            version: this.version
        };
    }
    
    write(newData: T, expectedVersion: number): boolean {
        if (this.version !== expectedVersion) {
            return false; // Version conflict
        }
        
        this.data = newData;
        this.version++;
        
        // Notify observers
        this.observers.forEach(observer => observer(this.data, this.version));
        
        return true;
    }
    
    observe(callback: (data: T, version: number) => void): () => void {
        this.observers.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.observers.indexOf(callback);
            if (index !== -1) {
                this.observers.splice(index, 1);
            }
        };
    }
    
    getVersion(): number {
        return this.version;
    }
}

/**
 * Conflict resolution strategies for concurrent operations
 */
export enum ConflictResolution {
    LAST_WRITER_WINS = 'last_writer_wins',
    FIRST_WRITER_WINS = 'first_writer_wins',
    MERGE = 'merge',
    MANUAL = 'manual'
}

export interface ConflictResolver<T> {
    resolve(local: T, remote: T): T;
}

/**
 * State synchronizer with conflict resolution
 */
export class StateSynchronizer<T> {
    private localState: T;
    private remoteState: T;
    private conflictResolver: ConflictResolver<T>;
    private version: number = 0;
    private pendingOperations = new Set<string>();
    
    constructor(
        initialState: T,
        conflictResolver: ConflictResolver<T>
    ) {
        this.localState = initialState;
        this.remoteState = initialState;
        this.conflictResolver = conflictResolver;
    }
    
    updateLocal(newState: T, operationId: string): void {
        this.localState = newState;
        this.pendingOperations.add(operationId);
        this.version++;
    }
    
    updateRemote(newState: T): T {
        this.remoteState = newState;
        
        // Check for conflicts
        if (this.pendingOperations.size > 0) {
            const resolved = this.conflictResolver.resolve(this.localState, this.remoteState);
            this.localState = resolved;
            this.version++;
            return resolved;
        }
        
        this.localState = newState;
        this.version++;
        return newState;
    }
    
    acknowledgeOperation(operationId: string): void {
        this.pendingOperations.delete(operationId);
    }
    
    getState(): { local: T; remote: T; version: number; hasPendingOperations: boolean } {
        return {
            local: this.localState,
            remote: this.remoteState,
            version: this.version,
            hasPendingOperations: this.pendingOperations.size > 0
        };
    }
}

/**
 * Debounced state manager to prevent race conditions in rapid updates
 */
export class DebouncedStateManager<T> {
    private currentState: T;
    private pendingState: T | null = null;
    private timeoutId: ReturnType<typeof setTimeout> | null = null;
    private callbacks: Array<(state: T) => void> = [];
    private debounceMs: number;
    
    constructor(initialState: T, debounceMs = 300) {
        this.currentState = initialState;
        this.debounceMs = debounceMs;
    }
    
    setState(newState: T): void {
        this.pendingState = newState;
        
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
        
        this.timeoutId = setTimeout(() => {
            if (this.pendingState) {
                this.currentState = this.pendingState;
                this.pendingState = null;
                this.callbacks.forEach(callback => callback(this.currentState));
            }
            this.timeoutId = null;
        }, this.debounceMs);
    }
    
    getState(): T {
        return this.currentState;
    }
    
    getPendingState(): T | null {
        return this.pendingState;
    }
    
    subscribe(callback: (state: T) => void): () => void {
        this.callbacks.push(callback);
        
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index !== -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }
    
    flush(): void {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        
        if (this.pendingState) {
            this.currentState = this.pendingState;
            this.pendingState = null;
            this.callbacks.forEach(callback => callback(this.currentState));
        }
    }
}

/**
 * React hook for mutex-protected operations
 */
export function useMutex(): {
    mutex: Mutex;
    withLock: <T>(operation: () => Promise<T>) => Promise<T>;
    isLocked: boolean;
} {
    const mutex = useRef(new Mutex()).current;
    
    const withLock = useCallback(async <T>(operation: () => Promise<T>): Promise<T> => {
        const release = await mutex.acquire();
        try {
            return await operation();
        } finally {
            release();
        }
    }, [mutex]);
    
    return {
        mutex,
        withLock,
        isLocked: mutex.isLocked()
    };
}

/**
 * React hook for operation queue
 */
export function useOperationQueue(): {
    enqueue: <T>(operation: () => Promise<T>) => Promise<T>;
    isProcessing: boolean;
    queueLength: number;
    clear: () => void;
} {
    const queue = useRef(new OperationQueue()).current;
    const [isProcessing, setIsProcessing] = useState(false);
    const [queueLength, setQueueLength] = useState(0);
    
    useEffect(() => {
        const interval = setInterval(() => {
            setIsProcessing(queue.isProcessing());
            setQueueLength(queue.getQueueLength());
        }, 100);
        
        return () => clearInterval(interval);
    }, [queue]);
    
    return {
        enqueue: queue.enqueue.bind(queue),
        isProcessing,
        queueLength,
        clear: queue.clear.bind(queue)
    };
}

/**
 * React hook for optimistic locking
 */
export function useOptimisticLock<T>(initialData: T): {
    data: T;
    version: number;
    update: (newData: T, expectedVersion: number) => boolean;
    subscribe: (callback: (data: T, version: number) => void) => () => void;
} {
    const lock = useRef(new OptimisticLock(initialData)).current;
    const [state, setState] = useState(() => lock.read());
    
    useEffect(() => {
        const unsubscribe = lock.observe((data, version) => {
            setState({ data, version });
        });
        
        return unsubscribe;
    }, [lock]);
    
    const update = useCallback((newData: T, expectedVersion: number) => {
        return lock.write(newData, expectedVersion);
    }, [lock]);
    
    return {
        data: state.data,
        version: state.version,
        update,
        subscribe: lock.observe.bind(lock)
    };
}

/**
 * Transaction manager for atomic operations
 */
export class TransactionManager {
    private activeTransactions = new Map<string, () => void>();
    private rollbackActions = new Map<string, Array<() => Promise<void>>>();
    
    async execute<T>(
        transactionId: string,
        operation: (rollback: (action: () => Promise<void>) => void) => Promise<T>
    ): Promise<T> {
        if (this.activeTransactions.has(transactionId)) {
            throw new Error(`Transaction ${transactionId} is already active`);
        }
        
        const rollbackList: Array<() => Promise<void>> = [];
        this.rollbackActions.set(transactionId, rollbackList);
        
        const rollback = (action: () => Promise<void>) => {
            rollbackList.push(action);
        };
        
        this.activeTransactions.set(transactionId, () => {
            this.rollback(transactionId);
        });
        
        try {
            const result = await operation(rollback);
            this.commit(transactionId);
            return result;
        } catch (error) {
            await this.rollback(transactionId);
            throw error;
        }
    }
    
    private commit(transactionId: string): void {
        this.activeTransactions.delete(transactionId);
        this.rollbackActions.delete(transactionId);
    }
    
    private async rollback(transactionId: string): Promise<void> {
        const rollbackList = this.rollbackActions.get(transactionId);
        if (rollbackList) {
            // Execute rollback actions in reverse order
            for (let i = rollbackList.length - 1; i >= 0; i--) {
                try {
                    await rollbackList[i]();
                } catch (error) {
                    console.error(`[TRANSACTION] Rollback action failed:`, error);
                }
            }
        }
        
        this.activeTransactions.delete(transactionId);
        this.rollbackActions.delete(transactionId);
    }
    
    isActive(transactionId: string): boolean {
        return this.activeTransactions.has(transactionId);
    }
    
    getActiveTransactions(): string[] {
        return Array.from(this.activeTransactions.keys());
    }
}

// Global transaction manager instance
export const globalTransactionManager = new TransactionManager();