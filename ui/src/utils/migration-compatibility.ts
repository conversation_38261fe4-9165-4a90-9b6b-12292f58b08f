/**
 * Migration Compatibility Layer - DEPRECATED
 * 
 * ⚠️ THIS FILE HAS BEEN DEPRECATED IN PHASE 5
 * 
 * The migration to flat types is now complete. This compatibility layer
 * is no longer needed and should not be used in new code.
 * 
 * Migration Path:
 * - Replace domain model usage with flat types from @/src/types
 * - Use useFlatPayStub instead of useMergedPayStub
 * - Use flat-type-utilities.ts for common data access patterns
 * 
 * This file will be completely removed in Phase 6.
 */

// Re-export minimal types for backwards compatibility during transition
export type { 
  FlatPayStubDetailDraft, 
  FlatPayStubDraft 
} from '@/src/types';

// Deprecated functions - DO NOT USE
export function migrateLegacyDetailDraft(): never {
  throw new Error('migrateLegacyDetailDraft is deprecated. Use flat types directly.');
}

export function migrateLegacyPayStubDraft(): never {
  throw new Error('migrateLegacyPayStubDraft is deprecated. Use flat types directly.');
}

export function migrateDomainModelToFlat(): never {
  throw new Error('migrateDomainModelToFlat is deprecated. Use flat types directly.');
}

export class LegacyDraftAdapter {
  constructor() {
    throw new Error('LegacyDraftAdapter is deprecated. Use flat types directly.');
  }
}

/**
 * @deprecated Migration is complete. This function is no longer needed.
 */
export function validateMigration(): never {
  throw new Error('validateMigration is deprecated. Migration is complete.');
}

/**
 * @deprecated Migration is complete. This class is no longer needed.
 */
export class MigrationTracker {
  static markAsMigrated(): never {
    throw new Error('MigrationTracker is deprecated. Migration is complete.');
  }
  
  static markAsLegacy(): never {
    throw new Error('MigrationTracker is deprecated. Migration is complete.');
  }
  
  static getProgress(): never {
    throw new Error('MigrationTracker is deprecated. Migration is complete.');
  }
  
  static printProgress(): never {
    throw new Error('MigrationTracker is deprecated. Migration is complete.');
  }
}

// Additional deprecated exports
export function migrateMapToflatDrafts(): never {
  throw new Error('migrateMapToflatDrafts is deprecated. Use flat types directly.');
}

export function isLegacyStructure(): never {
  throw new Error('isLegacyStructure is deprecated. Use flat types directly.');
}

export function autoMigrate(): never {
  throw new Error('autoMigrate is deprecated. Use flat types directly.');
}