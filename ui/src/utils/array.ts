/**
 * Array utility functions
 */

/**
 * Removes null and undefined values from an array
 * @param arr - Array potentially containing null/undefined values
 * @returns Array with null/undefined values filtered out
 * 
 * @example
 * const mixed = [1, null, 2, undefined, 3];
 * const clean = compact(mixed); // [1, 2, 3]
 * 
 * @example
 * const details = items.map(transform).filter(compact);
 */
export function compact<T>(value: T | null | undefined): value is T {
    return value !== null && value !== undefined;
}

/**
 * Alternative implementation that works as a method
 * @param arr - Array to compact
 * @returns Compacted array
 */
export function compactArray<T>(arr: (T | null | undefined)[]): T[] {
    return arr.filter((item): item is T => item !== null && item !== undefined);
}