/**
 * Safe ID Conversion Utilities
 * 
 * Phase 2.2 Implementation: Explicit Error Handling
 * 
 * This module provides utilities for safely converting ID values with explicit 
 * error handling instead of silent casting patterns like String(id || '').
 * 
 * These utilities throw descriptive errors when conversion fails, making 
 * debugging easier and preventing silent failures.
 */

import { GlobalIDConverters, GlobalIDGuards } from '../types/GlobalID';

/**
 * Safe ID conversion errors
 */
export class SafeIdConversionError extends Error {
  constructor(
    message: string,
    public readonly value: unknown,
    public readonly expectedType: string,
    public readonly context?: string
  ) {
    super(message);
    this.name = 'SafeIdConversionError';
  }
}

/**
 * Options for safe ID conversion
 */
export interface SafeIdConversionOptions {
  /** Context for error messages (e.g., 'PayStubDetail.id', 'TimeSheet.employeeId') */
  context?: string;
  /** Allow empty string as valid result */
  allowEmpty?: boolean;
  /** Fallback value if conversion fails (only used if allowEmpty is true) */
  fallback?: string;
}

/**
 * Safe ID conversion utilities with explicit error handling
 */
export class SafeIdConverter {
  /**
   * Safely converts an unknown value to a string ID with validation
   * 
   * @param value - The value to convert
   * @param options - Conversion options
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails
   */
  static toStringId(value: unknown, options: SafeIdConversionOptions = {}): string {
    const { context = 'ID', allowEmpty = false, fallback = '' } = options;

    // Handle null/undefined
    if (value === null || value === undefined) {
      if (allowEmpty) {
        return fallback;
      }
      throw new SafeIdConversionError(
        `${context} is null or undefined`,
        value,
        'string',
        context
      );
    }

    // Handle empty string
    if (value === '') {
      if (allowEmpty) {
        return fallback;
      }
      throw new SafeIdConversionError(
        `${context} is empty string`,
        value,
        'non-empty string',
        context
      );
    }

    // Handle string values
    if (typeof value === 'string') {
      return value;
    }

    // Handle number values
    if (typeof value === 'number') {
      if (isNaN(value) || !isFinite(value)) {
        throw new SafeIdConversionError(
          `${context} is not a valid number (NaN or Infinity)`,
          value,
          'valid number',
          context
        );
      }
      return String(value);
    }

    // Handle boolean values (convert to string)
    if (typeof value === 'boolean') {
      return String(value);
    }

    // All other types are invalid
    throw new SafeIdConversionError(
      `${context} has unsupported type: ${typeof value}`,
      value,
      'string, number, or boolean',
      context
    );
  }

  /**
   * Safely converts PayStubDetail ID with validation
   * 
   * @param value - The ID value from PayStubDetail
   * @param options - Conversion options
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails
   */
  static toPayStubDetailId(value: unknown, options: SafeIdConversionOptions = {}): string {
    const context = options.context || 'PayStubDetail.id';
    return this.toStringId(value, { ...options, context });
  }

  /**
   * Safely converts PayStub ID with validation
   * 
   * @param value - The ID value from PayStub
   * @param options - Conversion options
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails
   */
  static toPayStubId(value: unknown, options: SafeIdConversionOptions = {}): string {
    const context = options.context || 'PayStub.id';
    return this.toStringId(value, { ...options, context });
  }

  /**
   * Safely converts Employee ID with validation
   * 
   * @param value - The ID value from Employee
   * @param options - Conversion options
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails
   */
  static toEmployeeId(value: unknown, options: SafeIdConversionOptions = {}): string {
    const context = options.context || 'Employee.id';
    return this.toStringId(value, { ...options, context });
  }

  /**
   * Safely converts TimeSheet ID with validation
   * 
   * @param value - The ID value from TimeSheet
   * @param options - Conversion options
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails
   */
  static toTimeSheetId(value: unknown, options: SafeIdConversionOptions = {}): string {
    const context = options.context || 'TimeSheet.id';
    return this.toStringId(value, { ...options, context });
  }

  /**
   * Safely converts ID with strict validation (no empty values allowed)
   * 
   * @param value - The ID value to convert
   * @param context - Context for error messages
   * @returns String ID
   * @throws SafeIdConversionError if conversion fails or value is empty
   */
  static toRequiredId(value: unknown, context: string): string {
    return this.toStringId(value, { context, allowEmpty: false });
  }

  /**
   * Safely converts ID with lenient validation (allows empty values)
   * 
   * @param value - The ID value to convert
   * @param context - Context for error messages
   * @param fallback - Fallback value for empty inputs
   * @returns String ID or fallback
   */
  static toOptionalId(value: unknown, context: string, fallback = ''): string {
    return this.toStringId(value, { context, allowEmpty: true, fallback });
  }

  /**
   * Validates that a value is a valid ID without converting it
   * 
   * @param value - The value to validate
   * @param context - Context for error messages
   * @returns True if valid
   * @throws SafeIdConversionError if validation fails
   */
  static validateId(value: unknown, context: string): asserts value is string | number {
    this.toStringId(value, { context, allowEmpty: false });
  }

  /**
   * Attempts to convert an ID safely, returning null if conversion fails
   * 
   * @param value - The value to convert
   * @param options - Conversion options
   * @returns String ID or null if conversion fails
   */
  static tryToStringId(value: unknown, options: SafeIdConversionOptions = {}): string | null {
    try {
      return this.toStringId(value, options);
    } catch (error) {
      if (error instanceof SafeIdConversionError) {
        console.warn(`Safe ID conversion failed: ${error.message}`, { value, context: options.context });
        return null;
      }
      // Re-throw unexpected errors
      throw error;
    }
  }

  /**
   * Batch converts multiple IDs safely
   * 
   * @param values - Array of values to convert
   * @param options - Conversion options
   * @returns Array of converted string IDs
   * @throws SafeIdConversionError if any conversion fails
   */
  static batchToStringIds(values: unknown[], options: SafeIdConversionOptions = {}): string[] {
    return values.map((value, index) => 
      this.toStringId(value, { 
        ...options, 
        context: `${options.context || 'ID'}[${index}]` 
      })
    );
  }

  /**
   * Batch converts multiple IDs safely, filtering out failed conversions
   * 
   * @param values - Array of values to convert
   * @param options - Conversion options
   * @returns Array of successfully converted string IDs
   */
  static batchToStringIdsSafe(values: unknown[], options: SafeIdConversionOptions = {}): string[] {
    return values
      .map((value, index) => 
        this.tryToStringId(value, { 
          ...options, 
          context: `${options.context || 'ID'}[${index}]` 
        })
      )
      .filter((id): id is string => id !== null);
  }
}

/**
 * Convenience functions for common ID conversion patterns
 */

/**
 * Safely converts a PayStubDetail ID from fragment data
 * Replaces: String(detailData.id || '')
 */
export const safePayStubDetailId = (detailData: { id?: unknown }): string => {
  return SafeIdConverter.toPayStubDetailId(detailData.id, {
    context: 'PayStubDetail.id from fragment',
    allowEmpty: false
  });
};

/**
 * Safely converts a PayStub ID from fragment data
 * Replaces: String(payStubData.id || '')
 */
export const safePayStubId = (payStubData: { id?: unknown }): string => {
  return SafeIdConverter.toPayStubId(payStubData.id, {
    context: 'PayStub.id from fragment',
    allowEmpty: false
  });
};

/**
 * Safely converts a PayStub ID from fragment data (lenient)
 * Replaces: String(detailData?.payStubId || '')
 */
export const safePayStubIdLenient = (data: { payStubId?: unknown }): string => {
  return SafeIdConverter.toPayStubId(data.payStubId, {
    context: 'PayStub.id from fragment (lenient)',
    allowEmpty: true,
    fallback: ''
  });
};

/**
 * Safely converts an Employee ID from fragment data
 * Replaces: String(employee?.id || '')
 */
export const safeEmployeeId = (employee: { id?: unknown }): string => {
  return SafeIdConverter.toEmployeeId(employee.id, {
    context: 'Employee.id from fragment',
    allowEmpty: false
  });
};

/**
 * Safely converts an Employee ID from fragment data (lenient)
 * Replaces: String(employee?.id || '')
 */
export const safeEmployeeIdLenient = (employee: { id?: unknown } | null | undefined): string => {
  return SafeIdConverter.toEmployeeId(employee?.id, {
    context: 'Employee.id from fragment (lenient)',
    allowEmpty: true,
    fallback: ''
  });
};

/**
 * Safely converts a TimeSheet ID from fragment data
 * Replaces: String(timeSheet?.id || '')
 */
export const safeTimeSheetId = (timeSheet: { id?: unknown }): string => {
  return SafeIdConverter.toTimeSheetId(timeSheet.id, {
    context: 'TimeSheet.id from fragment',
    allowEmpty: false
  });
};

/**
 * Safely converts a TimeSheet ID from fragment data (lenient)
 * Replaces: String(timeSheet?.id || '')
 */
export const safeTimeSheetIdLenient = (timeSheet: { id?: unknown } | null | undefined): string => {
  return SafeIdConverter.toTimeSheetId(timeSheet?.id, {
    context: 'TimeSheet.id from fragment (lenient)',
    allowEmpty: true,
    fallback: ''
  });
};