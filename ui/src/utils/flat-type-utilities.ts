/**
 * Flat Type Utilities
 * 
 * Utility functions to help with the transition to flat types and
 * provide common patterns for working with flat data structures.
 * 
 * These utilities reduce code duplication and provide consistent
 * patterns for data access across components.
 */

import type { FlatPayStubDetailDraft } from '@/src/types';

// =============================================================================
// DATA ACCESS UTILITIES
// =============================================================================

/**
 * Get effective value from server data with draft override
 * Provides consistent pattern for accessing data with draft fallback
 * Used for UI display where draft values should show user edits
 * 
 * @param serverValue - Value from GraphQL server data
 * @param draftValue - Value from draft data (may be undefined)
 * @returns Effective value (draft if available, otherwise server)
 */
export function getEffectiveValue<T>(
  serverValue: T,
  draftValue: T | undefined
): T {
  return draftValue !== undefined ? draftValue : serverValue;
}

/**
 * Get effective value for totals calculation, preferring draft overrides
 * Draft values represent user edits that should override server data for accurate totals
 * When no draft exists, fall back to server data
 * 
 * @param serverValue - Value from GraphQL server data
 * @param draftValue - Value from draft data (may be undefined)
 * @returns Effective value (draft if available, otherwise server)
 */
export function getEffectiveValueForTotals<T>(
  serverValue: T,
  draftValue: T | undefined
): T {
  return draftValue !== undefined ? draftValue : serverValue;
}

/**
 * Consistent null-to-zero conversion for numeric calculations
 * Ensures all calculation paths handle null/undefined consistently
 * 
 * @param value - Numeric value that might be null/undefined
 * @returns Number or 0 if null/undefined
 */
export function safeNumericValue(value: number | null | undefined): number {
  if (value === null || value === undefined) return 0;
  const numValue = Number(value);
  return isNaN(numValue) ? 0 : numValue;
}

/**
 * Pre-index drafts by ID for O(1) lookups
 * Prevents expensive find() calls in render loops
 * 
 * @param detailDrafts - Array of detail drafts
 * @returns Map with detailId as key and draft as value
 */
export function indexDraftsByDetailId(
  detailDrafts: readonly FlatPayStubDetailDraft[]
): Record<string, FlatPayStubDetailDraft> {
  const index: Record<string, FlatPayStubDetailDraft> = {};
  detailDrafts.forEach(draft => {
    if (draft.id) index[draft.id] = draft;
  });
  return index;
}

/**
 * Safe field accessor that handles null/undefined gracefully
 * Provides null-safe access to potentially undefined draft fields
 * 
 * @param draft - Draft object (may be undefined)
 * @param field - Field name to access
 * @param defaultValue - Default value if field is not available
 * @returns Field value or default
 */
export function safeFieldAccess<T>(
  draft: FlatPayStubDetailDraft | undefined,
  field: keyof FlatPayStubDetailDraft,
  defaultValue: T
): T {
  return (draft?.[field] as T) ?? defaultValue;
}

// =============================================================================
// VALIDATION UTILITIES
// =============================================================================

/**
 * Check if a detail has any draft changes
 * Useful for showing draft indicators in UI
 * 
 * @param detailId - ID of the detail to check
 * @param draftsByDetailId - Pre-indexed drafts map
 * @returns True if detail has draft changes
 */
export function hasDetailDraftChanges(
  detailId: string,
  draftsByDetailId: Record<string, FlatPayStubDetailDraft>
): boolean {
  return detailId in draftsByDetailId;
}

/**
 * Check if a detail is marked for deletion
 * Provides consistent deletion state checking
 * 
 * @param detailId - ID of the detail to check
 * @param draftsByDetailId - Pre-indexed drafts map
 * @returns True if detail is marked for deletion
 */
export function isDetailMarkedForDeletion(
  detailId: string,
  draftsByDetailId: Record<string, FlatPayStubDetailDraft>
): boolean {
  return draftsByDetailId[detailId]?._uiDelete === true;
}

/**
 * Get validation errors for a detail
 * Provides consistent error state access
 * 
 * @param detailId - ID of the detail to check
 * @param draftsByDetailId - Pre-indexed drafts map
 * @returns Array of validation errors (empty if none)
 */
export function getDetailValidationErrors(
  detailId: string,
  draftsByDetailId: Record<string, FlatPayStubDetailDraft>
): string[] {
  return draftsByDetailId[detailId]?._uiValidationErrors || [];
}

// =============================================================================
// PERFORMANCE UTILITIES
// =============================================================================

// Removed createStableReference stub - was providing false sense of optimization
// Use React's useMemo directly with proper dependencies instead

/**
 * Calculate total hours from individual hour components
 * Uses consistent null handling with safeNumericValue
 * 
 * @param stHours - Standard hours
 * @param otHours - Overtime hours  
 * @param dtHours - Double-time hours
 * @returns Total hours sum
 */
export function calculateTotalHours(
  stHours?: number | null,
  otHours?: number | null,
  dtHours?: number | null
): number {
  const standard = safeNumericValue(stHours);
  const overtime = safeNumericValue(otHours);
  const doubletime = safeNumericValue(dtHours);
  
  return standard + overtime + doubletime;
}

/**
 * Collect orphan draft details that don't have corresponding server details
 * Helper function for calculateHourTotals to improve readability
 * Enhanced with functional programming approach for better performance
 * 
 * @param details - Server detail data from GraphQL
 * @param draftsByDetailId - Pre-indexed drafts for fast lookup
 * @returns Array of orphan draft details
 */
export function collectOrphanDraftDetails(
  details: readonly any[],
  draftsByDetailId: Record<string, FlatPayStubDetailDraft>
): FlatPayStubDetailDraft[] {
  // Create set of existing server detail IDs for fast lookup
  const serverDetailIds = new Set(details.map(detail => detail.id).filter(Boolean));
  
  // Use functional approach for better performance and readability
  return Object.entries(draftsByDetailId)
    .filter(([detailId, draft]) => {
      // Filter out deleted drafts
      if (draft._uiDelete === true) return false;
      
      // Filter out drafts that correspond to existing server details
      if (serverDetailIds.has(detailId)) return false;
      
      return true; // This is an orphan draft
    })
    .map(([, draft]) => draft); // Extract just the draft objects
}

/**
 * Calculate aggregated hour totals from details with draft overlays and orphan drafts
 * Enhanced to merge server details with orphan draft details for complete totals
 * 
 * @param details - Server detail data from GraphQL
 * @param draftsByDetailId - Pre-indexed drafts for fast lookup
 * @param hourField - Which hour field to aggregate ('stHours', 'otHours', 'dtHours')
 * @returns Total hours for the specified field including orphan drafts
 */
export function calculateHourTotals(
  details: readonly any[],
  draftsByDetailId: Record<string, FlatPayStubDetailDraft>,
  hourField: 'stHours' | 'otHours' | 'dtHours'
): number {
  if (!details && Object.keys(draftsByDetailId).length === 0) return 0;
  
  // Collect orphan draft details
  const orphanDrafts = collectOrphanDraftDetails(details || [], draftsByDetailId);
  
  // Process server details with draft overlays
  let total = 0;
  const processedIds = new Set<string>();
  
  // Process server details first (deduplication by preferring server version)
  if (details && details.length > 0) {
    for (const detail of details) {
      // Skip if detail is marked for deletion in drafts
      const draft = draftsByDetailId[detail.id];
      if (draft?._uiDelete === true) continue;
      
      // Skip if already processed (deduplication)
      if (processedIds.has(detail.id)) continue;
      processedIds.add(detail.id);
      
      // Use draft value if available, otherwise server value for live updates
      // Consistent null-to-zero conversion (U-4)
      const serverValue = safeNumericValue(detail[hourField]);
      const draftValue = draft?.[hourField];
      const effectiveValue = getEffectiveValueForTotals(serverValue, draftValue);
      
      total += safeNumericValue(effectiveValue);
    }
  }
  
  // Process orphan drafts (those not in server data)
  for (const orphanDraft of orphanDrafts) {
    // Skip if already processed (additional safety check)
    if (processedIds.has(orphanDraft.id || '')) continue;
    if (orphanDraft.id) processedIds.add(orphanDraft.id);
    
    // Consistent null-to-zero conversion (U-4)
    const draftValue = orphanDraft[hourField];
    total += safeNumericValue(draftValue);
  }
  
  return total;
}

// =============================================================================
// MIGRATION HELPERS
// =============================================================================

/**
 * Extract scalar values from complex objects for memo dependencies
 * Prevents stale closures when objects are mutated in-place
 * 
 * @param obj - Object to extract scalars from
 * @returns Object with only scalar/primitive values
 */
export function extractScalarDependencies(obj: any): Record<string, unknown> {
  if (!obj || typeof obj !== 'object') return {};
  
  const scalars: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      scalars[key] = value;
    } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      scalars[key] = value;
    } else if (Array.isArray(value)) {
      scalars[`${key}Length`] = value.length;
    }
  }
  
  return scalars;
}

/**
 * Check if two flat draft objects are equivalent
 * Useful for preventing unnecessary updates
 * 
 * @param draft1 - First draft to compare
 * @param draft2 - Second draft to compare
 * @returns True if drafts have the same data
 */
export function areDraftsEquivalent(
  draft1: FlatPayStubDetailDraft | undefined,
  draft2: FlatPayStubDetailDraft | undefined
): boolean {
  if (draft1 === draft2) return true;
  if (!draft1 || !draft2) return false;
  
  // Compare key fields that affect UI
  const keyFields: (keyof FlatPayStubDetailDraft)[] = [
    'stHours', 'otHours', 'dtHours', 'bonus', 'expenses', 
    'jobCode', 'costCenter', 'hourlyRate', 'agreementId',
    'classificationId', 'subClassificationId', 'earningsCode',
    '_uiDelete'
  ];
  
  return keyFields.every(field => draft1[field] === draft2[field]);
}

// =============================================================================
// DEBUGGING UTILITIES (Development Only)
// =============================================================================

/**
 * Log draft state for debugging (development only)
 * Helps with troubleshooting draft data issues
 * 
 * @param context - Context string for the log
 * @param detailId - Detail ID being debugged
 * @param draft - Draft data to log
 */
export function debugDraftState(
  context: string,
  detailId: string,
  draft: FlatPayStubDetailDraft | undefined
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[FLAT-TYPES DEBUG] ${context}:`, {
      detailId,
      draft: draft ? {
        ...draft,
        // Truncate long arrays for readability
        _uiValidationErrors: draft._uiValidationErrors?.slice(0, 3)
      } : null
    });
  }
}

/**
 * Performance monitoring for flat type operations (development only)
 * Helps identify performance bottlenecks
 * 
 * @param operation - Operation name
 * @param fn - Function to monitor
 * @returns Result of the function
 */
export function withPerformanceMonitoring<T>(
  operation: string,
  fn: () => T
): T {
  if (process.env.NODE_ENV !== 'development') {
    return fn();
  }
  
  const startTime = performance.now();
  const result = fn();
  const endTime = performance.now();
  
  const duration = endTime - startTime;
  if (duration > 1) { // Only log operations that take more than 1ms
    console.log(`[FLAT-TYPES PERF] ${operation} took ${duration.toFixed(2)}ms`);
  }
  
  return result;
}