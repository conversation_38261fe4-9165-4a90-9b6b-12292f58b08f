/**
 * DEPRECATED - Validation Utils
 * 
 * This file contains deprecated validation functions that have been replaced
 * with more maintainable implementations. These functions are kept for reference
 * but should NOT be used in production code.
 * 
 * @deprecated Since version 2.0.0 - Use the main validationUtils.ts instead
 */

import type { PayStub as OriginalPayStub } from '@/src/types/relay-ui-extensions';
import type { ValidationError } from '../validationUtils';

// Validation-specific interface that extends Relay type with UI state fields
interface ValidationPayStubDetail {
    id: string;
    payStubId?: string | number | null;
    workDate?: string | null;
    agreementId?: number | null;
    classificationId?: number | null;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    jobCode?: string | null;
    costCenter?: string | null;
    bonus?: number | null;
    expenses?: number | null;
    hourlyRate?: number | null;
    earningsCode?: string | null;
    subClassificationId?: number | null;
    delete?: boolean;
}

// Validation-specific interface for timesheet
interface ValidationTimeSheet {
    id?: string;
    payStubs?: ReadonlyArray<OriginalPayStub> | Array<OriginalPayStub>;
}

/**
 * @deprecated This function has been deprecated due to:
 * 1. Hard-coded magic numbers throughout the implementation
 * 2. Complex logic that's difficult to maintain
 * 3. Performance optimizations that make the code hard to understand
 * 4. Mixing of concerns (validation logic with performance optimization)
 * 
 * Use the refactored validateTimesheet() function from validationUtils.ts instead.
 * 
 * Performance-optimized validation for large datasets
 * Uses early termination and optimized loops
 * @param timeSheetData The timesheet data
 * @param payStubs Array of PayStub objects
 * @param options Performance options
 * @returns Validation errors
 */
export function validateTimesheetOptimized(
    timeSheetData: ValidationTimeSheet, 
    payStubs: OriginalPayStub[],
    options: {
        maxErrors?: number;
        skipWarnings?: boolean;
        earlyTermination?: boolean;
    } = {}
): ValidationError[] {
    throw new Error(
        'validateTimesheetOptimized is deprecated. ' +
        'Please use validateTimesheet from validationUtils.ts instead. ' +
        'The new implementation maintains the same performance characteristics ' +
        'while being more maintainable and using centralized constants.'
    );
}

/**
 * Migration guide:
 * 
 * Replace:
 * ```typescript
 * import { validateTimesheetOptimized } from './validationUtils';
 * const errors = validateTimesheetOptimized(timesheet, payStubs, {
 *   maxErrors: 1000,
 *   skipWarnings: false,
 *   earlyTermination: true
 * });
 * ```
 * 
 * With:
 * ```typescript
 * import { validateTimesheet } from './validationUtils';
 * const errors = validateTimesheet(timesheet, payStubs);
 * ```
 * 
 * The new validateTimesheet function includes the same optimizations
 * but with better code organization and maintainability.
 */