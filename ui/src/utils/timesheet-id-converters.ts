/**
 * Type-Safe ID Conversion Utilities for Timesheet Operations
 * 
 * Phase 2 Implementation: Complete Global-ID Handling
 * 
 * This file provides type-safe ID conversion utilities that validate
 * GraphQL Global ID types before conversion, preventing runtime errors
 * from invalid ID formats or wrong entity types.
 * 
 * All timesheet mutations MUST use these converters instead of parseInt
 * to ensure type safety and proper error handling.
 */

import { RelayIdService } from '../services/RelayIdService';

/**
 * Type-safe ID conversion utilities with validation
 * These functions validate the Global ID type before conversion
 * and throw descriptive errors for invalid inputs
 */
export class TimesheetIdConverter {
    /**
     * Converts PayStub Global ID to numeric ID with type validation
     * @param payStubId - PayStub Global ID (e.g., "UGF5U3R1YjoxMjM=")
     * @returns Numeric PayStub ID
     * @throws Error if ID is not a valid PayStub Global ID
     */
    static payStubToNumeric(payStubId: string): number {
        try {
            const { type, id } = RelayIdService.fromGlobalId(payStubId);
            if (type !== 'PayStub') {
                throw new Error(`Expected PayStub ID, got ${type}`);
            }
            const numericId = parseInt(id, 10);
            if (isNaN(numericId)) {
                throw new Error(`Invalid numeric ID in PayStub Global ID: ${id}`);
            }
            return numericId;
        } catch (error) {
            throw new Error(`Failed to convert PayStub ID "${payStubId}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Converts Employee Global ID to numeric ID with type validation
     * @deprecated Use Global IDs directly - backend now accepts ID! type for employeeId
     * @param employeeId - Employee Global ID
     * @returns Numeric Employee ID
     * @throws Error if ID is not a valid Employee Global ID
     */
    static employeeToNumeric(employeeId: string): number {
        try {
            const { type, id } = RelayIdService.fromGlobalId(employeeId);
            if (type !== 'Employee') {
                throw new Error(`Expected Employee ID, got ${type}`);
            }
            const numericId = parseInt(id, 10);
            if (isNaN(numericId)) {
                throw new Error(`Invalid numeric ID in Employee Global ID: ${id}`);
            }
            return numericId;
        } catch (error) {
            throw new Error(`Failed to convert Employee ID "${employeeId}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Converts TimeSheet Global ID to numeric ID with type validation
     * @param timesheetId - TimeSheet Global ID
     * @returns Numeric TimeSheet ID
     * @throws Error if ID is not a valid TimeSheet Global ID
     */
    static timesheetToNumeric(timesheetId: string): number {
        try {
            const { type, id } = RelayIdService.fromGlobalId(timesheetId);
            if (type !== 'TimeSheet') {
                throw new Error(`Expected TimeSheet ID, got ${type}`);
            }
            const numericId = parseInt(id, 10);
            if (isNaN(numericId)) {
                throw new Error(`Invalid numeric ID in TimeSheet Global ID: ${id}`);
            }
            return numericId;
        } catch (error) {
            throw new Error(`Failed to convert TimeSheet ID "${timesheetId}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Converts PayStubDetail Global ID to numeric ID with type validation
     * @param detailId - PayStubDetail Global ID
     * @returns Numeric PayStubDetail ID
     * @throws Error if ID is not a valid PayStubDetail Global ID
     */
    static payStubDetailToNumeric(detailId: string): number {
        try {
            const { type, id } = RelayIdService.fromGlobalId(detailId);
            if (type !== 'PayStubDetail') {
                throw new Error(`Expected PayStubDetail ID, got ${type}`);
            }
            const numericId = parseInt(id, 10);
            if (isNaN(numericId)) {
                throw new Error(`Invalid numeric ID in PayStubDetail Global ID: ${id}`);
            }
            return numericId;
        } catch (error) {
            throw new Error(`Failed to convert PayStubDetail ID "${detailId}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Validates that a string is a properly formatted Global ID for a specific type
     * @param globalId - The Global ID to validate
     * @param expectedType - The expected entity type (e.g., 'PayStub', 'Employee')
     * @returns True if valid, false otherwise
     */
    static isValidGlobalId(globalId: string, expectedType: string): boolean {
        try {
            const { type } = RelayIdService.fromGlobalId(globalId);
            return type === expectedType;
        } catch {
            return false;
        }
    }

    /**
     * Safely converts any Global ID to numeric with type checking
     * @param globalId - The Global ID to convert
     * @param expectedType - The expected entity type
     * @returns Numeric ID
     * @throws Error if conversion fails or type doesn't match
     */
    static safeToNumeric(globalId: string, expectedType: string): number {
        try {
            const { type, id } = RelayIdService.fromGlobalId(globalId);
            if (type !== expectedType) {
                throw new Error(`Expected ${expectedType} ID, got ${type}`);
            }
            const numericId = parseInt(id, 10);
            if (isNaN(numericId)) {
                throw new Error(`Invalid numeric ID in ${expectedType} Global ID: ${id}`);
            }
            return numericId;
        } catch (error) {
            throw new Error(`Failed to convert ${expectedType} ID "${globalId}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}

/**
 * Convenience functions for common ID conversions
 * These provide shorter syntax for frequently used conversions
 */

/**
 * Convert PayStub Global ID to numeric (shorthand)
 */
export const payStubIdToNumeric = (id: string): number => TimesheetIdConverter.payStubToNumeric(id);

/**
 * Convert Employee Global ID to numeric (shorthand)
 * @deprecated Use Global IDs directly - backend now accepts ID! type for employeeId
 */
export const employeeIdToNumeric = (id: string): number => TimesheetIdConverter.employeeToNumeric(id);

/**
 * Convert TimeSheet Global ID to numeric (shorthand)
 */
export const timesheetIdToNumeric = (id: string): number => TimesheetIdConverter.timesheetToNumeric(id);

/**
 * Convert PayStubDetail Global ID to numeric (shorthand)
 */
export const payStubDetailIdToNumeric = (id: string): number => TimesheetIdConverter.payStubDetailToNumeric(id);

/**
 * Batch ID conversion utilities for arrays
 */
export class BatchIdConverter {
    /**
     * Convert array of PayStub Global IDs to numeric IDs
     * @param payStubIds - Array of PayStub Global IDs
     * @returns Array of numeric PayStub IDs
     * @throws Error if any ID is invalid
     */
    static payStubIdsToNumeric(payStubIds: string[]): number[] {
        return payStubIds.map(id => TimesheetIdConverter.payStubToNumeric(id));
    }

    /**
     * Convert array of Employee Global IDs to numeric IDs
     * @deprecated Use Global IDs directly - backend now accepts ID! type for employeeId
     * @param employeeIds - Array of Employee Global IDs
     * @returns Array of numeric Employee IDs
     * @throws Error if any ID is invalid
     */
    static employeeIdsToNumeric(employeeIds: string[]): number[] {
        return employeeIds.map(id => TimesheetIdConverter.employeeToNumeric(id));
    }

    /**
     * Convert array of PayStubDetail Global IDs to numeric IDs
     * @param detailIds - Array of PayStubDetail Global IDs
     * @returns Array of numeric PayStubDetail IDs
     * @throws Error if any ID is invalid
     */
    static payStubDetailIdsToNumeric(detailIds: string[]): number[] {
        return detailIds.map(id => TimesheetIdConverter.payStubDetailToNumeric(id));
    }
}

/**
 * Validation utilities for ID format checking
 */
export class IdValidationUtils {
    /**
     * Check if a string appears to be a valid Global ID (base64 encoded)
     * @param id - String to check
     * @returns True if it appears to be a Global ID
     */
    static looksLikeGlobalId(id: string): boolean {
        // Basic check: Global IDs are base64 encoded and contain ':'
        try {
            const decoded = atob(id);
            return decoded.includes(':');
        } catch {
            return false;
        }
    }

    /**
     * Check if a string appears to be a numeric ID
     * @param id - String to check
     * @returns True if it appears to be numeric
     */
    static looksLikeNumericId(id: string): boolean {
        return /^\d+$/.test(id);
    }

    /**
     * Determine the likely ID format of a string
     * @param id - String to analyze
     * @returns 'global' | 'numeric' | 'unknown'
     */
    static detectIdFormat(id: string): 'global' | 'numeric' | 'unknown' {
        if (this.looksLikeGlobalId(id)) return 'global';
        if (this.looksLikeNumericId(id)) return 'numeric';
        return 'unknown';
    }
}

export default TimesheetIdConverter;