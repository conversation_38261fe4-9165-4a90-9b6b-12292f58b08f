/**
 * Comprehensive tests for PayStub totals calculations with orphan drafts
 * Tests the enhanced calculateHourTotals and collectOrphanDraftDetails functions
 */

import { calculateHourTotals, collectOrphanDraftDetails } from '../flat-type-utilities';
import type { FlatPayStubDetailDraft } from '@/src/types';

describe('collectOrphanDraftDetails', () => {
    const mockServerDetails = [
        { id: 'detail-1', stHours: 8, otHours: 0, dtHours: 0 },
        { id: 'detail-2', stHours: 6, otHours: 2, dtHours: 0 }
    ];

    it('should return empty array when no drafts exist', () => {
        const draftsByDetailId = {};
        const result = collectOrphanDraftDetails(mockServerDetails, draftsByDetailId);
        expect(result).toEqual([]);
    });

    it('should return empty array when all drafts correspond to server details', () => {
        const draftsByDetailId = {
            'detail-1': { id: 'detail-1', stHours: 10, _uiLastModified: Date.now() },
            'detail-2': { id: 'detail-2', otHours: 3, _uiLastModified: Date.now() }
        } as Record<string, FlatPayStubDetailDraft>;

        const result = collectOrphanDraftDetails(mockServerDetails, draftsByDetailId);
        expect(result).toEqual([]);
    });

    it('should return orphan drafts that do not correspond to server details', () => {
        const draftsByDetailId = {
            'detail-1': { id: 'detail-1', stHours: 10, _uiLastModified: Date.now() },
            'detail-orphan-1': { id: 'detail-orphan-1', stHours: 4, _uiLastModified: Date.now() },
            'detail-orphan-2': { id: 'detail-orphan-2', otHours: 2, _uiLastModified: Date.now() }
        } as Record<string, FlatPayStubDetailDraft>;

        const result = collectOrphanDraftDetails(mockServerDetails, draftsByDetailId);
        expect(result).toHaveLength(2);
        expect(result.map((d) => d.id)).toEqual(['detail-orphan-1', 'detail-orphan-2']);
    });

    it('should exclude orphan drafts marked for deletion', () => {
        const draftsByDetailId = {
            'detail-orphan-1': { id: 'detail-orphan-1', stHours: 4, _uiDelete: true, _uiLastModified: Date.now() },
            'detail-orphan-2': { id: 'detail-orphan-2', otHours: 2, _uiLastModified: Date.now() }
        } as Record<string, FlatPayStubDetailDraft>;

        const result = collectOrphanDraftDetails(mockServerDetails, draftsByDetailId);
        expect(result).toHaveLength(1);
        expect(result[0].id).toBe('detail-orphan-2');
    });
});

describe('calculateHourTotals', () => {
    const mockServerDetails = [
        { id: 'detail-1', stHours: 8, otHours: 0, dtHours: 0 },
        { id: 'detail-2', stHours: 6, otHours: 2, dtHours: 0 },
        { id: 'detail-3', stHours: 4, otHours: 0, dtHours: 2 }
    ];

    describe('server-only scenarios', () => {
        it('should calculate totals from server data only when no drafts exist', () => {
            const draftsByDetailId = {};

            const stTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'stHours');
            const otTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'otHours');
            const dtTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'dtHours');

            expect(stTotal).toBe(18); // 8 + 6 + 4
            expect(otTotal).toBe(2); // 0 + 2 + 0
            expect(dtTotal).toBe(2); // 0 + 0 + 2
        });

        it('should handle empty server details', () => {
            const draftsByDetailId = {};

            const stTotal = calculateHourTotals([], draftsByDetailId, 'stHours');
            expect(stTotal).toBe(0);
        });

        it('should handle null/undefined server details', () => {
            const draftsByDetailId = {};

            const stTotal = calculateHourTotals(undefined as any, draftsByDetailId, 'stHours');
            expect(stTotal).toBe(0);
        });
    });

    describe('draft-only scenarios', () => {
        it('should calculate totals from orphan drafts only when no server data exists', () => {
            const draftsByDetailId = {
                'draft-1': { id: 'draft-1', stHours: 5, _uiLastModified: Date.now() },
                'draft-2': { id: 'draft-2', otHours: 3, _uiLastModified: Date.now() }
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals([], draftsByDetailId, 'stHours');
            const otTotal = calculateHourTotals([], draftsByDetailId, 'otHours');

            expect(stTotal).toBe(5);
            expect(otTotal).toBe(3);
        });

        it('should exclude deleted orphan drafts', () => {
            const draftsByDetailId = {
                'draft-1': { id: 'draft-1', stHours: 5, _uiDelete: true, _uiLastModified: Date.now() },
                'draft-2': { id: 'draft-2', stHours: 3, _uiLastModified: Date.now() }
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals([], draftsByDetailId, 'stHours');
            expect(stTotal).toBe(3); // Only draft-2
        });
    });

    describe('mixed scenarios (server + orphan drafts)', () => {
        it('should combine server data with orphan drafts', () => {
            const draftsByDetailId = {
                'detail-1': { id: 'detail-1', stHours: 10, _uiLastModified: Date.now() }, // Override server
                'orphan-1': { id: 'orphan-1', stHours: 5, _uiLastModified: Date.now() }, // Pure orphan
                'orphan-2': { id: 'orphan-2', otHours: 3, _uiLastModified: Date.now() } // Pure orphan
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'stHours');
            const otTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'otHours');

            // stHours: 10 (detail-1 override) + 6 (detail-2) + 4 (detail-3) + 5 (orphan-1) = 25
            expect(stTotal).toBe(25);

            // otHours: 0 (detail-1 override default) + 2 (detail-2) + 0 (detail-3) + 3 (orphan-2) = 5
            expect(otTotal).toBe(5);
        });

        it('should deduplicate server entries and use draft overrides', () => {
            const serverDetailsWithDupes = [
                { id: 'detail-1', stHours: 8 },
                { id: 'detail-1', stHours: 10 } // Duplicate server detail
            ];

            const draftsByDetailId = {
                'detail-1': { id: 'detail-1', stHours: 12, _uiLastModified: Date.now() }
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals(serverDetailsWithDupes, draftsByDetailId, 'stHours');

            // Should process detail-1 only once, using draft value of 12
            expect(stTotal).toBe(12);
        });
    });

    describe('null handling (U-4 requirement)', () => {
        it('should treat null values as 0 during aggregation', () => {
            const detailsWithNulls = [
                { id: 'detail-1', stHours: null, otHours: undefined, dtHours: 8 },
                { id: 'detail-2', stHours: 5, otHours: null, dtHours: null }
            ];

            const draftsByDetailId = {
                'orphan-1': { id: 'orphan-1', stHours: null, otHours: null, dtHours: 3, _uiLastModified: Date.now() }
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals(detailsWithNulls, draftsByDetailId, 'stHours');
            const otTotal = calculateHourTotals(detailsWithNulls, draftsByDetailId, 'otHours');
            const dtTotal = calculateHourTotals(detailsWithNulls, draftsByDetailId, 'dtHours');

            expect(stTotal).toBe(5); // 0 + 5 + 0 (null orphan treated as 0)
            expect(otTotal).toBe(0); // 0 + 0 + 0 (all nulls)
            expect(dtTotal).toBe(11); // 8 + 0 + 3
        });

        it('should handle draft values that override null server values', () => {
            const detailsWithNulls = [{ id: 'detail-1', stHours: null, otHours: null }];

            const draftsByDetailId = {
                'detail-1': { id: 'detail-1', stHours: 10, otHours: 5, _uiLastModified: Date.now() }
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals(detailsWithNulls, draftsByDetailId, 'stHours');
            const otTotal = calculateHourTotals(detailsWithNulls, draftsByDetailId, 'otHours');

            expect(stTotal).toBe(10);
            expect(otTotal).toBe(5);
        });
    });

    describe('deletion scenarios', () => {
        it('should exclude server details that are marked for deletion in drafts', () => {
            const draftsByDetailId = {
                'detail-1': { id: 'detail-1', _uiDelete: true, _uiLastModified: Date.now() },
                'detail-2': { id: 'detail-2', stHours: 12, _uiLastModified: Date.now() } // Override
            } as Record<string, FlatPayStubDetailDraft>;

            const stTotal = calculateHourTotals(mockServerDetails, draftsByDetailId, 'stHours');

            // Should exclude detail-1 (deleted) and use draft value for detail-2
            // detail-1: excluded, detail-2: 12 (draft), detail-3: 4 (server) = 16
            expect(stTotal).toBe(16);
        });
    });

    describe('edge cases', () => {
        it('should handle both undefined details and empty drafts', () => {
            const stTotal = calculateHourTotals(undefined as any, {}, 'stHours');
            expect(stTotal).toBe(0);
        });

        it('should handle non-numeric hour values gracefully', () => {
            const invalidDetails = [{ id: 'detail-1', stHours: 'invalid' as any }];

            const stTotal = calculateHourTotals(invalidDetails, {}, 'stHours');
            expect(stTotal).toBe(0); // Non-numeric values should be treated as 0
        });

        it('should handle details without IDs', () => {
            const detailsWithoutIds = [
                { stHours: 5 }, // No ID
                { id: 'detail-1', stHours: 8 }
            ];

            const stTotal = calculateHourTotals(detailsWithoutIds, {}, 'stHours');
            expect(stTotal).toBe(13); // Should still process both
        });
    });
});
