/**
 * Unit tests for validation utilities
 * 
 * These tests cover the core validation functions to ensure they work correctly
 * for various input scenarios including edge cases and error conditions.
 */

import {
    validateDetailField,
    validateDetailRow,
    validateTimesheet,
    createDebouncedValidation,
    type ValidationError
} from '../validationUtils';

describe('validateDetailField', () => {
    const mockRowData = {
        id: 'detail-1',
        payStubId: 'paystub-1',
        workDate: new Date().toISOString().split('T')[0], // Use current date to avoid warnings
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        agreementId: 1,
        classificationId: 1
    };

    it('should validate numeric fields correctly', () => {
        // Valid numeric value
        const validErrors = validateDetailField('stHours', 8, mockRowData, 'paystub-1');
        expect(validErrors).toHaveLength(0);

        // Invalid numeric value (negative)
        const invalidErrors = validateDetailField('stHours', -5, mockRowData, 'paystub-1');
        expect(invalidErrors).toHaveLength(1);
        expect(invalidErrors[0].message).toContain('cannot be negative');

        // Invalid numeric value (NaN)
        const nanErrors = validateDetailField('stHours', 'invalid', mockRowData, 'paystub-1');
        expect(nanErrors).toHaveLength(1);
        expect(nanErrors[0].message).toContain('must be a valid number');
    });

    it('should validate date fields correctly', () => {
        // Valid date (use current date to avoid date range warnings)
        const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        const validErrors = validateDetailField('workDate', currentDate, mockRowData, 'paystub-1');
        expect(validErrors).toHaveLength(0);

        // Invalid date
        const invalidErrors = validateDetailField('workDate', 'invalid-date', mockRowData, 'paystub-1');
        expect(invalidErrors).toHaveLength(1);
        expect(invalidErrors[0].message).toContain('valid date');

        // Date too far in future
        const futureDate = new Date();
        futureDate.setFullYear(futureDate.getFullYear() + 2);
        const futureErrors = validateDetailField('workDate', futureDate.toISOString(), mockRowData, 'paystub-1');
        expect(futureErrors).toHaveLength(1);
        expect(futureErrors[0].severity).toBe('warning');
    });

    it('should validate cross-field dependencies (total hours)', () => {
        const highHoursData = {
            ...mockRowData,
            stHours: 12,
            otHours: 8,
            dtHours: 6 // Total: 26 hours
        };

        const errors = validateDetailField('dtHours', 6, highHoursData, 'paystub-1');
        expect(errors).toHaveLength(1);
        expect(errors[0].message).toContain('cannot exceed 24 hours');
    });

    it('should validate agreement and classification requirements', () => {
        const dataWithTriggerFields = {
            ...mockRowData,
            stHours: 8,
            agreementId: null, // Missing required agreement
            classificationId: null
        };

        const errors = validateDetailField('stHours', 8, dataWithTriggerFields, 'paystub-1');
        expect(errors).toHaveLength(2); // Both agreement and classification required
        expect(errors.some(e => e.message.includes('Agreement is required'))).toBe(true);
        expect(errors.some(e => e.message.includes('Classification is required'))).toBe(true);
    });

    it('should skip validation for deleted rows', () => {
        const deletedRowData = {
            ...mockRowData,
            delete: true,
            stHours: -999 // Would normally cause error
        };

        const errors = validateDetailField('stHours', -999, deletedRowData, 'paystub-1');
        expect(errors).toHaveLength(0);
    });
});

describe('validateDetailRow', () => {
    it('should validate a complete detail row', () => {
        const validDetail = {
            id: 'detail-1',
            payStubId: 'paystub-1',
            workDate: '2023-01-15',
            stHours: 8,
            otHours: 0,
            dtHours: 0,
            agreementId: 1,
            classificationId: 1,
            jobCode: 'DEV',
            costCenter: 'IT'
        };

        const errors = validateDetailRow(validDetail, 'Doe, John', '2023-01-15');
        expect(errors).toHaveLength(0);
    });

    it('should detect missing required fields when trigger fields are present', () => {
        const detailWithMissingAgreement = {
            id: 'detail-1',
            stHours: 8, // Trigger field
            agreementId: null, // Missing
            classificationId: null
        };

        const errors = validateDetailRow(detailWithMissingAgreement);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors.some(e => e.message.includes('Agreement is required'))).toBe(true);
    });
});

describe('validateTimesheet', () => {
    const mockTimesheet = { id: 'timesheet-1' };
    const currentDate = new Date().toISOString().split('T')[0]; // Current date to avoid warnings
    const mockPayStubs = [
        {
            id: 'paystub-1',
            employeeId: 'employee-1',
            employee: { firstName: 'John', lastName: 'Doe' },
            details: [
                {
                    id: 'detail-1',
                    workDate: currentDate,
                    stHours: 8,
                    otHours: 0, // Explicitly set to 0 to avoid validation errors
                    dtHours: 0, // Explicitly set to 0 to avoid validation errors
                    agreementId: 1,
                    classificationId: 1
                }
            ]
        }
    ];

    it('should validate a valid timesheet', () => {
        const errors = validateTimesheet(mockTimesheet, mockPayStubs as any);
        expect(errors).toHaveLength(0);
    });

    it('should detect missing employee ID', () => {
        const invalidPayStubs = [
            {
                ...mockPayStubs[0],
                employeeId: null
            }
        ];

        const errors = validateTimesheet(mockTimesheet, invalidPayStubs as any);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors.some(e => e.message.includes('Employee is required'))).toBe(true);
    });

    it('should detect duplicate employees', () => {
        const duplicatePayStubs = [
            mockPayStubs[0],
            { ...mockPayStubs[0], id: 'paystub-2' } // Same employee, different paystub
        ];

        const errors = validateTimesheet(mockTimesheet, duplicatePayStubs as any);
        expect(errors.some(e => e.message.includes('appears multiple times'))).toBe(true);
    });

    it('should handle null timesheet data', () => {
        const errors = validateTimesheet(null as any, []);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].message).toContain('missing');
    });
});

describe.skip('validateTimesheetOptimized [DEPRECATED]', () => {
    const mockTimesheet = { id: 'timesheet-1' };
    const currentDate = new Date().toISOString().split('T')[0]; // Current date to avoid warnings
    const mockPayStubs = Array.from({ length: 100 }, (_, i) => ({
        id: `paystub-${i}`,
        employeeId: `employee-${i}`,
        employee: { firstName: 'John', lastName: `Doe${i}` },
        details: [
            {
                id: `detail-${i}`,
                workDate: currentDate,
                stHours: 8,
                otHours: 0, // Explicitly set to 0 to avoid validation errors
                dtHours: 0, // Explicitly set to 0 to avoid validation errors
                agreementId: 1,
                classificationId: 1
            }
        ]
    }));

    // TODO: Re-enable once validateTimesheetOptimized is implemented or replaced
    it.skip('should handle large datasets efficiently', () => {
        const startTime = performance.now();
        // const errors = validateTimesheetOptimized(mockTimesheet, mockPayStubs as any);
        const endTime = performance.now();

        expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
        // expect(errors).toHaveLength(0);
    });

    // TODO: Re-enable once validateTimesheetOptimized is implemented or replaced
    it.skip('should respect maxErrors limit', () => {
        const invalidPayStubs = Array.from({ length: 50 }, (_, i) => ({
            id: `paystub-${i}`,
            employeeId: null, // Invalid - will cause errors
            details: []
        }));

        // const errors = validateTimesheetOptimized(
        //     mockTimesheet,
        //     invalidPayStubs as any,
        //     { maxErrors: 10, earlyTermination: true }
        // );

        // expect(errors.length).toBeLessThanOrEqual(10);
    });

    // TODO: Re-enable once validateTimesheetOptimized is implemented or replaced
    it.skip('should skip warnings when requested', () => {
        const payStubsWithWarnings = [
            {
                id: 'paystub-1',
                employeeId: 'employee-1',
                employee: { firstName: 'John', lastName: 'Doe' },
                details: [
                    {
                        id: 'detail-1',
                        workDate: '2023-01-15',
                        stHours: 8,
                        agreementId: 1,
                        classificationId: 1,
                        hourlyRate: 1000 // Would cause warning
                    }
                ]
            }
        ];

        // const errorsWithWarnings = validateTimesheetOptimized(
        //     mockTimesheet,
        //     payStubsWithWarnings as any,
        //     { skipWarnings: false }
        // );

        // const errorsWithoutWarnings = validateTimesheetOptimized(
        //     mockTimesheet,
        //     payStubsWithWarnings as any,
        //     { skipWarnings: true }
        // );

        // expect(errorsWithWarnings.length).toBeGreaterThan(errorsWithoutWarnings.length);
    });
});

describe('createDebouncedValidation', () => {
    jest.useFakeTimers();

    it('should debounce validation calls', () => {
        const mockCallback = jest.fn();
        const debouncedValidation = createDebouncedValidation(100);

        // Call multiple times rapidly
        debouncedValidation('stHours', 8, {}, 'paystub-1', 'timesheet-1', 'detail-1', 'John Doe', mockCallback);
        debouncedValidation('stHours', 9, {}, 'paystub-1', 'timesheet-1', 'detail-1', 'John Doe', mockCallback);
        debouncedValidation('stHours', 10, {}, 'paystub-1', 'timesheet-1', 'detail-1', 'John Doe', mockCallback);

        // Should not have called callback yet
        expect(mockCallback).not.toHaveBeenCalled();

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Should have called callback only once with the last value
        expect(mockCallback).toHaveBeenCalledTimes(1);
    });

    afterEach(() => {
        jest.clearAllTimers();
    });
});