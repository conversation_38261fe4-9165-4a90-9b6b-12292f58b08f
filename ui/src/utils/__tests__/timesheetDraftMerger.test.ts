import {
    mergeDrafts,
    mergeDetailDraftsForValidation,
    mergeDetailDraftsForUI,
    buildDraftFromEdits,
    type ValidatableDetail
} from '../timesheetDraftMerger';

describe('timesheetDraftMerger', () => {
    interface TestItem {
        id: string;
        name: string;
        value: number;
    }

    it('should merge draft changes into persisted data', () => {
        const persisted: TestItem[] = [
            { id: '1', name: 'Item 1', value: 10 },
            { id: '2', name: 'Item 2', value: 20 },
            { id: '3', name: 'Item 3', value: 30 }
        ];

        const drafts: Record<string, Partial<TestItem>> = {
            '1': { value: 15 },
            '3': { name: 'Updated Item 3' }
        };

        const result = mergeDrafts(persisted, drafts);

        expect(result).toEqual([
            { id: '1', name: 'Item 1', value: 15 },
            { id: '2', name: 'Item 2', value: 20 },
            { id: '3', name: 'Updated Item 3', value: 30 }
        ]);
    });

    it('should handle empty drafts', () => {
        const persisted: TestItem[] = [{ id: '1', name: 'Item 1', value: 10 }];

        const drafts: Record<string, Partial<TestItem>> = {};

        const result = mergeDrafts(persisted, drafts);

        expect(result).toEqual(persisted);
    });

    it('should handle readonly arrays', () => {
        const persisted: readonly TestItem[] = [{ id: '1', name: 'Item 1', value: 10 }];

        const drafts: Record<string, Partial<TestItem>> = {
            '1': { value: 15 }
        };

        const result = mergeDrafts(persisted, drafts);

        expect(result).toEqual([{ id: '1', name: 'Item 1', value: 15 }]);
    });
});

describe('mergeDetailDraftsForValidation', () => {
    it('should merge ValidatableDetail objects correctly', () => {
        const persisted: ValidatableDetail[] = [
            {
                id: '1',
                workDate: '2024-01-01',
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                bonus: 0,
                expenses: 0,
                jobCode: 'JOB1',
                costCenter: 'CC1',
                agreementId: 1,
                classificationId: 1
            }
        ];

        const drafts: Record<string, Partial<ValidatableDetail>> = {
            '1': { stHours: 10, bonus: 100 }
        };

        const result = mergeDetailDraftsForValidation(persisted, drafts);

        expect(result).toEqual([
            {
                id: '1',
                workDate: '2024-01-01',
                stHours: 10,
                otHours: 0,
                dtHours: 0,
                bonus: 100,
                expenses: 0,
                jobCode: 'JOB1',
                costCenter: 'CC1',
                agreementId: 1,
                classificationId: 1
            }
        ]);
    });
});

describe('mergeDetailDraftsForUI', () => {
    it('should be a function that can be called', () => {
        // Test that the function exists and can be called
        expect(mergeDetailDraftsForUI).toBeDefined();
        expect(typeof mergeDetailDraftsForUI).toBe('function');
    });

    it('should handle empty arrays', () => {
        const result = mergeDetailDraftsForUI([], {});
        expect(result).toEqual([]);
    });
});

describe('buildDraftFromEdits', () => {
    it('should build draft from temporary edits', () => {
        const tempEdits = {
            stHours: '10',
            bonus: '100',
            jobCode: 'NEW_JOB',
            _uiDelete: true
        };

        const parseNum = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseFloat(val);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const parseIntId = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseInt(val, 10);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const result = buildDraftFromEdits(tempEdits, parseNum, parseIntId);

        expect(result.stHours).toBe(10);
        expect(result.bonus).toBe(100);
        expect(result.jobCode).toBe('NEW_JOB');
        expect(result.delete).toBe(true);
        expect(result.otHours).toBeUndefined(); // Not in tempEdits
    });

    it('should handle undefined values correctly', () => {
        const tempEdits = {
            stHours: undefined,
            jobCode: '',
            costCenter: null
        };

        const parseNum = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseFloat(val);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const parseIntId = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseInt(val, 10);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const result = buildDraftFromEdits(tempEdits, parseNum, parseIntId);

        expect(result.stHours).toBeNull();
        expect(result.jobCode).toBeNull(); // Empty string becomes null
        expect(result.costCenter).toBeNull();
    });

    it('should handle empty string values for string fields', () => {
        const tempEdits = {
            jobCode: '',
            costCenter: ''
        };

        const parseNum = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseFloat(val);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const parseIntId = (val: unknown): number | null => {
            if (typeof val === 'string') {
                const parsed = parseInt(val, 10);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        };

        const result = buildDraftFromEdits(tempEdits, parseNum, parseIntId);

        expect(result.jobCode).toBeNull(); // Empty string becomes null
        expect(result.costCenter).toBeNull(); // Empty string becomes null
    });
});
