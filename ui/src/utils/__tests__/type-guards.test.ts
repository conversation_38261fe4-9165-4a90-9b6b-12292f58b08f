/**
 * Unit tests for type guards and type conversion utilities
 * 
 * These tests ensure that type guards correctly validate objects and that
 * type conversions handle edge cases safely.
 */

import {
    isValidationCompatiblePayStub,
    isValidationCompatiblePayStubDetail,
    isModifyPayStubInput,
    convertToValidationPayStubs,
    convertToMutationInputs,
    validatePayStubsForValidation,
    isTypeValidationError
} from '../type-guards';

describe('isValidationCompatiblePayStub', () => {
    it('should validate valid PayStub objects', () => {
        const validPayStub = {
            id: 'paystub-1',
            employeeId: 'employee-1',
            details: [
                { id: 'detail-1' }
            ]
        };

        expect(isValidationCompatiblePayStub(validPayStub)).toBe(true);
    });

    it('should reject invalid PayStub objects', () => {
        const errorCollector: string[] = [];

        // Missing ID
        expect(isValidationCompatiblePayStub({ employeeId: 'employee-1', details: [] }, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('id must be'))).toBe(true);

        // Invalid details
        errorCollector.length = 0;
        expect(isValidationCompatiblePayStub({ id: 'paystub-1', employeeId: 'employee-1', details: 'invalid' }, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('details must be an array'))).toBe(true);

        // Empty ID
        errorCollector.length = 0;
        expect(isValidationCompatiblePayStub({ id: '', employeeId: 'employee-1', details: [] }, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('non-empty string'))).toBe(true);
    });

    it('should handle different employeeId types', () => {
        // String employeeId
        expect(isValidationCompatiblePayStub({
            id: 'paystub-1',
            employeeId: 'employee-1',
            details: []
        })).toBe(true);

        // Numeric employeeId
        expect(isValidationCompatiblePayStub({
            id: 'paystub-1',
            employeeId: 123,
            details: []
        })).toBe(true);

        // Null employeeId
        expect(isValidationCompatiblePayStub({
            id: 'paystub-1',
            employeeId: null,
            details: []
        })).toBe(true);

        // Invalid employeeId
        const errorCollector: string[] = [];
        expect(isValidationCompatiblePayStub({
            id: 'paystub-1',
            employeeId: {},
            details: []
        }, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('employeeId must be'))).toBe(true);
    });

    it('should reject null or non-object inputs', () => {
        expect(isValidationCompatiblePayStub(null)).toBe(false);
        expect(isValidationCompatiblePayStub(undefined)).toBe(false);
        expect(isValidationCompatiblePayStub('string')).toBe(false);
        expect(isValidationCompatiblePayStub(123)).toBe(false);
    });
});

describe('isValidationCompatiblePayStubDetail', () => {
    it('should validate valid PayStubDetail objects', () => {
        const validDetail = {
            id: 'detail-1',
            payStubId: 'paystub-1',
            workDate: '2023-01-15',
            employeeId: 'employee-1',
            stHours: 8,
            otHours: 2,
            agreementId: 1,
            classificationId: 1
        };

        expect(isValidationCompatiblePayStubDetail(validDetail)).toBe(true);
    });

    it('should validate numeric fields properly', () => {
        const errorCollector: string[] = [];

        // Invalid numeric value
        const invalidDetail = {
            id: 'detail-1',
            payStubId: 'paystub-1',
            stHours: 'not-a-number'
        };

        expect(isValidationCompatiblePayStubDetail(invalidDetail, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('stHours'))).toBe(true);
    });

    it('should validate date fields', () => {
        const errorCollector: string[] = [];

        // Invalid date
        const invalidDetail = {
            id: 'detail-1',
            workDate: 'invalid-date'
        };

        expect(isValidationCompatiblePayStubDetail(invalidDetail, errorCollector)).toBe(false);
        expect(errorCollector.some(e => e.includes('valid date'))).toBe(true);
    });

    it('should handle null and undefined values correctly', () => {
        const detailWithNulls = {
            id: 'detail-1',
            payStubId: null,
            workDate: null,
            employeeId: null,
            stHours: null,
            agreementId: null
        };

        expect(isValidationCompatiblePayStubDetail(detailWithNulls)).toBe(true);
    });
});

describe('convertToValidationPayStubs', () => {
    it('should convert valid modifiable pay stubs', () => {
        const modifiablePayStubs = [
            {
                id: 'paystub-1',
                employeeId: 'employee-1',
                name: 'John Doe',
                totalHours: 8,
                details: [
                    {
                        id: 'detail-1',
                        workDate: '2023-01-15',
                        stHours: 8,
                        agreementId: 1,
                        classificationId: 1
                    }
                ]
            }
        ];

        const result = convertToValidationPayStubs(modifiablePayStubs as any);
        
        expect(result.payStubs).toHaveLength(1);
        expect(result.conversionErrors).toHaveLength(0);
        expect(result.payStubs[0].id).toBe('paystub-1');
    });

    it('should handle conversion errors gracefully', () => {
        const invalidPayStubs = [
            null,
            { id: 'paystub-1' }, // Missing required fields
            { 
                id: 'paystub-2',
                employeeId: 'employee-1',
                details: [
                    { id: 'detail-1', stHours: 'invalid-number' } // Invalid numeric value
                ]
            }
        ];

        const result = convertToValidationPayStubs(invalidPayStubs as any);
        
        expect(result.conversionErrors.length).toBeGreaterThan(0);
        expect(result.payStubs.length).toBeLessThan(invalidPayStubs.length);
    });

    it('should handle edge cases in field conversion', () => {
        const edgeCasePayStubs = [
            {
                id: '123', // Number ID (should convert to string)
                employeeId: 456, // Number employee ID (should convert to string)
                details: [
                    {
                        id: '789', // Number detail ID
                        stHours: '8.5', // String hours (should convert to number)
                        workDate: new Date('2023-01-15'), // Date object (should convert to string)
                        agreementId: '1' // String ID (should convert to number)
                    }
                ]
            }
        ];

        const result = convertToValidationPayStubs(edgeCasePayStubs as any);
        
        expect(result.payStubs).toHaveLength(1);
        const convertedPayStub = result.payStubs[0];
        
        expect(typeof convertedPayStub.id).toBe('string');
        expect(typeof convertedPayStub.employeeId).toBe('string');
        expect(typeof convertedPayStub.details[0].stHours).toBe('number');
        expect(typeof convertedPayStub.details[0].agreementId).toBe('number');
    });

    it('should report conversion warnings for problematic values', () => {
        const problematicPayStubs = [
            {
                id: 'paystub-1',
                employeeId: 'employee-1',
                details: [
                    {
                        id: 'detail-1',
                        stHours: -5, // Negative hours (warning)
                        hourlyRate: 'invalid-rate' // Invalid rate (warning)
                    }
                ]
            }
        ];

        const result = convertToValidationPayStubs(problematicPayStubs as any);
        
        expect(result.conversionErrors.some(e => e.severity === 'warning')).toBe(true);
    });
});

describe('isTypeValidationError', () => {
    it('should identify type validation errors', () => {
        const typeError = new Error('Validation conversion failed: invalid data');
        const mutationError = new Error('Mutation conversion failed: bad input');
        const compatibilityError = new Error('PayStub is not compatible with validation');
        const genericError = new Error('Something else went wrong');

        expect(isTypeValidationError(typeError)).toBe(true);
        expect(isTypeValidationError(mutationError)).toBe(true);
        expect(isTypeValidationError(compatibilityError)).toBe(true);
        expect(isTypeValidationError(genericError)).toBe(false);
    });

    it('should handle non-Error objects', () => {
        expect(isTypeValidationError('string error')).toBe(false);
        expect(isTypeValidationError(null)).toBe(false);
        expect(isTypeValidationError(undefined)).toBe(false);
        expect(isTypeValidationError({})).toBe(false);
    });
});

describe('validatePayStubsForValidation', () => {
    it('should validate an array of valid PayStubs', () => {
        const validPayStubs = [
            {
                id: 'paystub-1',
                employeeId: 'employee-1',
                details: [{ id: 'detail-1' }]
            },
            {
                id: 'paystub-2',
                employeeId: 'employee-2',
                details: [{ id: 'detail-2' }]
            }
        ];

        const result = validatePayStubsForValidation(validPayStubs);
        
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.validPayStubs).toHaveLength(2);
    });

    it('should identify invalid PayStubs', () => {
        const invalidPayStubs = [
            { id: 'paystub-1', employeeId: 'employee-1', details: [] }, // Valid
            { employeeId: 'employee-2', details: [] }, // Missing ID
            null // Completely invalid
        ];

        const result = validatePayStubsForValidation(invalidPayStubs);
        
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.validPayStubs).toHaveLength(1);
    });
});