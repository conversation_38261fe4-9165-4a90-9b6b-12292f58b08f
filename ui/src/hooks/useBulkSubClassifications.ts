import React, { useMemo } from 'react';
import { useLazyLoadQuery, graphql } from 'react-relay';
import type { useBulkSubClassificationsQuery } from '@/relay/__generated__/useBulkSubClassificationsQuery.graphql';

/**
 * Input interface for agreement-classification pairs
 */
export interface SubClassificationPair {
  agreementId: string;
  classificationId: string;
}

/**
 * Dropdown option interface for UI components
 */
export interface DropdownOption {
  value: string;
  label: string;
}

/**
 * Hook for bulk fetching subclassifications for multiple agreement-classification pairs
 * 
 * This hook replaces the REST-based fetchSubClassifications in the bulk upload wizard
 * with a single GraphQL query that fetches all required subclassifications in one request.
 * 
 * Features:
 * - Single network request for multiple pairs
 * - Automatic caching through Relay
 * - Type-safe with generated types
 * - Optimized for bulk upload scenarios
 * - Automatic chunking for requests >200 pairs
 * 
 * @param pairs - Array of agreement-classification pairs to fetch subclassifications for
 * @returns Map of subclassifications keyed by "agreementId_classificationId"
 */
export function useBulkSubClassifications(pairs: SubClassificationPair[]): Map<string, DropdownOption[]> {
  // Convert pairs to the format expected by the GraphQL query
  const graphqlPairs = useMemo(() => {
    return pairs.map(pair => ({
      agreementId: pair.agreementId,
      classificationId: pair.classificationId
    }));
  }, [pairs]);

  // Handle chunking for large requests (>200 pairs)
  const chunkedPairs = useMemo(() => {
    const maxBatchSize = 200;
    const chunks: typeof graphqlPairs[] = [];
    
    for (let i = 0; i < graphqlPairs.length; i += maxBatchSize) {
      chunks.push(graphqlPairs.slice(i, i + maxBatchSize));
    }
    
    return chunks;
  }, [graphqlPairs]);

  // Execute multiple queries for large datasets
  const chunkResults = chunkedPairs.map((chunk, index) => {
    // Colocated GraphQL query following project patterns
    return useLazyLoadQuery<useBulkSubClassificationsQuery>(
      graphql`
        query useBulkSubClassificationsQuery($pairs: [SubClassificationPairInput!]!) {
          subClassificationsByPairs(input: { pairs: $pairs }) {
            agreementId
            classificationId
            nodes {
              id
              name
            }
          }
        }
      `,
      { pairs: chunk },
      {
        fetchPolicy: 'network-only', // Always fetch fresh data for bulk operations
        fetchKey: `bulk-subclassifications-${index}` // Unique key for each chunk
      }
    );
  });

  // Transform the GraphQL responses from all chunks into a map for efficient lookups
  return useMemo(() => {
    const resultMap = new Map<string, DropdownOption[]>();

    // Process results from all chunks
    chunkResults.forEach(chunkData => {
      if (!chunkData?.subClassificationsByPairs) {
        return;
      }

      chunkData.subClassificationsByPairs.forEach(batch => {
        const key = `${batch.agreementId}_${batch.classificationId}`;
        const options = batch.nodes.map(node => ({
          value: node.id,
          label: node.name
        }));
        resultMap.set(key, options);
      });
    });

    return resultMap;
  }, [chunkResults]);
}

/**
 * Helper hook for feature flag support
 * 
 * This hook provides a way to gradually roll out the GraphQL-based bulk subclassifications
 * by checking an environment variable or feature flag.
 * 
 * @returns boolean indicating whether to use the new GraphQL hook
 */
export function useSubClassificationMigrationFlag(): boolean {
  return useMemo(() => {
    // Check for environment variable or feature flag
    const envFlag = process.env.REACT_APP_USE_GQL_CLASS_CACHE === 'true';
    
    // For now, default to false for gradual rollout
    return envFlag;
  }, []);
}

/**
 * Unified hook that chooses between REST and GraphQL based on feature flag
 * 
 * This hook provides a seamless migration path by automatically choosing
 * the appropriate data fetching method based on the feature flag.
 * 
 * @param pairs - Array of agreement-classification pairs
 * @returns Map of subclassifications with consistent interface
 */
export function useSubClassificationsWithMigration(pairs: SubClassificationPair[]): {
  data: Map<string, DropdownOption[]>;
  isUsingGraphQL: boolean;
} {
  const useGraphQL = useSubClassificationMigrationFlag();
  const graphqlData = useBulkSubClassifications(pairs);
  
  return useMemo(() => {
    if (useGraphQL) {
      return {
        data: graphqlData,
        isUsingGraphQL: true
      };
    } else {
      // For REST fallback, return empty map - the existing REST logic will handle it
      return {
        data: new Map<string, DropdownOption[]>(),
        isUsingGraphQL: false
      };
    }
  }, [useGraphQL, graphqlData]);
}