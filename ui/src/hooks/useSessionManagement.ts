import { useEffect } from 'react';
import { sessionService } from '@/src/services/session-management';
import { useAuthDialogStore } from '@/src/store/authDialog';

/**
 * Custom hook to manage session tracking service
 * Automatically starts the service when the component mounts and stops it when unmounting
 */
export const useSessionManagement = () => {
    const { openAuthDialog } = useAuthDialogStore();

    useEffect(() => {
        // Initialize the session service with the auth dialog opener function
        sessionService.initialize(openAuthDialog);

        // Start the session management service
        sessionService.start();

        // Cleanup function to stop the service when component unmounts
        return () => {
            sessionService.stop();
        };
    }, [openAuthDialog]);

    return {
        sessionService,
        getSessionStatus: () => sessionService.getSessionStatus(),
        extendSession: () => sessionService.extendSession(),
        registerActivity: (url: string, method?: string) => sessionService.registerActivity(url, method)
    };
};
