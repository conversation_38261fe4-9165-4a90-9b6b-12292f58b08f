/**
 * Tests for the refined sanitization patterns in useTimesheetSaver.ts
 * Verifies that contextual sanitization works correctly without over-sanitizing
 */

// Mock the sanitizeSensitiveInfo function for testing
// In a real implementation, you'd export this function or test it directly
interface SanitizationConfig {
    allowDiagnosticValues?: boolean;
    allowedEmailDomains?: string[];
    allowedIPRanges?: string[];
}

function mockSanitizeSensitiveInfo(message: string, config: SanitizationConfig = {}): string {
    // Simplified version of the actual sanitization logic
    const SANITIZE_PATTERNS: [RegExp, string][] = [
        // Credentials and secrets - always sanitize
        [/password[=:]\s*\w+/gi, 'password=[REDACTED]'],
        [/token[=:]\s*[\w\-_.]+/gi, 'token=[REDACTED]'],
        [/key[=:]\s*[\w\-_.]+/gi, 'key=[REDACTED]'],
        [/secret[=:]\s*[\w\-_.]+/gi, 'secret=[REDACTED]'],
        [/authorization:\s*bearer\s+[\w\-_.]+/gi, 'authorization: bearer [REDACTED]'],

        // Personal identification - always sanitize
        [/\b\d{3}-\d{2}-\d{4}\b/g, 'XXX-XX-XXXX'], // SSN patterns
        [/\b\d{16}\b/g, 'XXXX-XXXX-XXXX-XXXX'], // Credit card patterns

        // Contextual email sanitization - only in key-value contexts
        [/(?<=email[=:]\s*)[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}/gi, '[EMAIL]'],
        [/(?<=user[=:]\s*)[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}/gi, '[EMAIL]'],
        [/(?<=from[=:]\s*)[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}/gi, '[EMAIL]'],
        [/(?<=to[=:]\s*)[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}/gi, '[EMAIL]'],

        // Contextual IP sanitization - only in key-value contexts
        [/(?<=ip[=:]\s*)\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/gi, '[IP_ADDRESS]'],
        [/(?<=host[=:]\s*)\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/gi, '[IP_ADDRESS]'],
        [/(?<=address[=:]\s*)\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/gi, '[IP_ADDRESS]'],
        [/(?<=client[=:]\s*)\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/gi, '[IP_ADDRESS]'],
        [/(?<=server[=:]\s*)\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/gi, '[IP_ADDRESS]']
    ];

    let sanitized = message;

    // Apply base sanitization patterns
    sanitized = SANITIZE_PATTERNS.reduce((msg, [pattern, replacement]) => msg.replace(pattern, replacement), sanitized);

    // Handle diagnostic mode
    if (config.allowDiagnosticValues) {
        sanitized = sanitized.replace(/\[EMAIL\]/g, '[DIAGNOSTIC_EMAIL]');
        sanitized = sanitized.replace(/\[IP_ADDRESS\]/g, '[DIAGNOSTIC_IP]');
    }

    return sanitized;
}

describe('Sanitization Patterns', () => {
    describe('Contextual Email Sanitization', () => {
        it('should sanitize emails in key-value contexts', () => {
            const messages = ['email=<EMAIL>', 'user: <EMAIL>', 'from: <EMAIL>', 'to: <EMAIL>'];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toContain('[EMAIL]');
                expect(sanitized).not.toContain('@');
            });
        });

        it('should NOT sanitize emails in technical contexts', () => {
            const messages = [
                'Connection failed to smtp.example.com',
                'User <EMAIL> logged in successfully',
                'Error occurred while processing request from *************',
                'Database connection to postgres@localhost:5432 established',
                'API endpoint /api/users/<EMAIL> returned 404',
                'Log entry: <EMAIL> accessed resource at ********'
            ];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toBe(message); // Should remain unchanged
            });
        });
    });

    describe('Contextual IP Sanitization', () => {
        it('should sanitize IPs in key-value contexts', () => {
            const messages = ['ip=*************', 'host: ********', 'address: **********', 'client: ************', 'server: *******'];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toContain('[IP_ADDRESS]');
                expect(sanitized).not.toMatch(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
            });
        });

        it('should NOT sanitize IPs in technical contexts', () => {
            const messages = [
                'Connection established to *************:8080',
                'Request from ******** processed successfully',
                'Network error: timeout connecting to **********',
                'DNS resolution failed for *******',
                'Load balancer routing to ************'
            ];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toBe(message); // Should remain unchanged
            });
        });
    });

    describe('Always Sanitized Patterns', () => {
        it('should always sanitize credentials and secrets', () => {
            const messages = [
                'password=secret123',
                'token: abc123def456',
                'key: private_key_here',
                'secret: confidential_data',
                'authorization: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'
            ];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toContain('[REDACTED]');
                expect(sanitized).not.toContain('secret123');
                expect(sanitized).not.toContain('abc123def456');
            });
        });

        it('should always sanitize personal identification', () => {
            const messages = ['SSN: ***********', 'Credit card: ****************', 'ID: 1234567890123456'];

            messages.forEach((message) => {
                const sanitized = mockSanitizeSensitiveInfo(message);
                expect(sanitized).toContain('XXX-XX-XXXX');
                expect(sanitized).toContain('XXXX-XXXX-XXXX-XXXX');
            });
        });
    });

    describe('Diagnostic Mode', () => {
        it('should mark sanitized values as diagnostic when enabled', () => {
            const message = 'email=<EMAIL> ip=*************';
            const sanitized = mockSanitizeSensitiveInfo(message, { allowDiagnosticValues: true });

            expect(sanitized).toContain('[DIAGNOSTIC_EMAIL]');
            expect(sanitized).toContain('[DIAGNOSTIC_IP]');
        });

        it('should use standard markers when diagnostic mode is disabled', () => {
            const message = 'email=<EMAIL> ip=*************';
            const sanitized = mockSanitizeSensitiveInfo(message, { allowDiagnosticValues: false });

            expect(sanitized).toContain('[EMAIL]');
            expect(sanitized).toContain('[IP_ADDRESS]');
        });
    });

    describe('Mixed Content', () => {
        it('should handle mixed sensitive and technical content correctly', () => {
            const message = `
                Error: Authentication failed for user=<EMAIL>
                Connection to database at *************:5432 failed
                API key: sk_live_1234567890abcdef
                Log entry: <EMAIL> accessed resource
                Server response from ********: 500 Internal Server Error
            `;

            const sanitized = mockSanitizeSensitiveInfo(message);

            // Should sanitize contextual sensitive data
            expect(sanitized).toContain('[EMAIL]'); // user=<EMAIL>
            expect(sanitized).toContain('[IP_ADDRESS]'); // database at *************
            expect(sanitized).toContain('[REDACTED]'); // API key

            // Should preserve technical details
            expect(sanitized).toContain('<EMAIL>'); // in log entry
            expect(sanitized).toContain('********'); // in server response
        });
    });
});
