/**
 * @jest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { useTimesheetSaver } from '../useTimesheetSaver';
import { useTimesheetUIStore } from '../../store/timesheetUIStore';
import { buildModifyPayStubDetailInput } from '../../mappers/payStubMappers';
import type { ModifiablePayStubDetail } from '../../types/timesheet-detail';

// Mock dependencies
jest.mock('react-relay', () => ({
    useMutation: jest.fn(() => [jest.fn(), false]),
    useRelayEnvironment: jest.fn(() => ({})),
    graphql: jest.fn((query) => query),
    ConnectionHandler: {
        getConnection: jest.fn(),
        createEdge: jest.fn(),
        insertEdgeBefore: jest.fn()
    }
}));

jest.mock('../../store/timesheetUIStore');
jest.mock('../../store/rosterFilterStore', () => ({
    useTimesheetRosterFilterStore: jest.fn(() => ({
        activeFilters: null,
        activeSortOrder: null
    }))
}));

jest.mock('@/lib', () => ({
    useStore: {
        getState: () => ({
            user: { username: 'testuser', permissions: [] }
        })
    }
}));

jest.mock('../../utils/securityUtils', () => ({
    sanitizeUserInput: jest.fn((input) => input),
    sanitizeErrorMessage: jest.fn((msg) => msg),
    sanitizeEmployeeName: jest.fn((name) => name),
    validateTimesheetAccess: jest.fn(() => true),
    mutationRateLimiter: { isAllowed: jest.fn(() => true) },
    logSecurityEvent: jest.fn()
}));

jest.mock('../../utils/validationUtils', () => ({
    validateTimesheet: jest.fn(() => []),
    convertToValidationPayStubs: jest.fn((payStubs) => ({
        payStubs,
        conversionErrors: []
    }))
}));

describe('useTimesheetSaver - Deletion Scenarios', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup default UI store state
        (useTimesheetUIStore.getState as jest.Mock).mockReturnValue({
            getDetailDraft: jest.fn(() => ({})),
            getDraftForPayStub: jest.fn(() => ({})),
            markedForDeletion: new Set(),
            clearAllMarkForDeletion: jest.fn(),
            clearAllDrafts: jest.fn(),
            getAllValidationErrors: jest.fn(() => new Map()),
            setValidationErrors: jest.fn(),
            clearValidationErrors: jest.fn()
        });
    });

    describe('Pay Stub Detail Deletion', () => {
        it('should produce empty array when deleting an unsaved detail', () => {
            // Create an unsaved detail (client-side ID)
            const unsavedDetail: ModifiablePayStubDetail = {
                id: 'temp-123',
                name: 'Test Employee',
                workDate: '2024-01-15',
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'JOB123',
                delete: true // Mark for deletion
            } as ModifiablePayStubDetail;

            // Mock the UI store to return delete flag
            (useTimesheetUIStore.getState as jest.Mock).mockReturnValue({
                getDetailDraft: jest.fn(() => ({ _uiDelete: true })),
                getDraftForPayStub: jest.fn(() => ({})),
                markedForDeletion: new Set(),
                clearAllMarkForDeletion: jest.fn(),
                clearAllDrafts: jest.fn(),
                getAllValidationErrors: jest.fn(() => new Map()),
                setValidationErrors: jest.fn(),
                clearValidationErrors: jest.fn()
            });

            const { result } = renderHook(() => useTimesheetSaver());

            // The mapper should filter out unsaved details marked for deletion
            expect(result.current).toBeDefined();
            
            // Direct test of the mapping logic (since we can't easily test the full flow)
            const detailInput = buildModifyPayStubDetailInput({
                ...unsavedDetail,
                id: 'temp-123' // Client-side ID
            });
            
            // For unsaved details marked for deletion, mapper returns null
            expect(detailInput).toBeNull();
        });

        it('should produce { id, delete: true } when deleting a saved detail', () => {
            // Create a saved detail (server-side Relay ID)
            const savedDetail: ModifiablePayStubDetail = {
                id: 'UGF5U3R1YkRldGFpbDoxMjM=', // Base64 encoded "PayStubDetail:123"
                name: 'Test Employee',
                workDate: '2024-01-15',
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'JOB123',
                delete: true // Mark for deletion
            } as ModifiablePayStubDetail;

            const detailInput = buildModifyPayStubDetailInput(savedDetail);
            
            // For saved details marked for deletion, mapper returns minimal delete payload
            expect(detailInput).toEqual({
                id: 'UGF5U3R1YkRldGFpbDoxMjM=',
                delete: true
            });
        });

        it('should handle numeric IDs correctly when deleting', () => {
            // Create a saved detail with numeric ID (legacy format)
            const savedDetail: ModifiablePayStubDetail = {
                id: '123', // Numeric ID as string
                name: 'Test Employee',
                workDate: '2024-01-15',
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'JOB123',
                delete: true // Mark for deletion
            } as ModifiablePayStubDetail;

            const detailInput = buildModifyPayStubDetailInput(savedDetail);
            
            // Mapper should convert numeric ID to Relay format
            expect(detailInput).toEqual({
                id: 'UGF5U3R1YkRldGFpbDoxMjM=', // Converted to Relay ID
                delete: true
            });
        });
    });

    describe('Pay Stub Deletion', () => {
        it('should handle pay stub marked for deletion through UI store', async () => {
            const timesheetId = 'VGltZVNoZWV0OjQ1Ng==';
            const payStubId = 'UGF5U3R1Yjo3ODk=';
            
            // Mock UI store with pay stub marked for deletion
            (useTimesheetUIStore.getState as jest.Mock).mockReturnValue({
                getDetailDraft: jest.fn(() => ({})),
                getDraftForPayStub: jest.fn(() => ({})),
                markedForDeletion: new Set([`${timesheetId}:${payStubId}`]),
                clearAllMarkForDeletion: jest.fn(),
                clearAllDrafts: jest.fn(),
                getAllValidationErrors: jest.fn(() => new Map()),
                setValidationErrors: jest.fn(),
                clearValidationErrors: jest.fn()
            });

            const { result } = renderHook(() => useTimesheetSaver());
            
            // Verify the hook is ready
            expect(result.current.isSaving).toBe(false);
            expect(result.current.saveError).toBeNull();
        });
    });

    describe('Edge Cases', () => {
        it('should handle detail with no ID marked for deletion', () => {
            // Detail without ID marked for deletion
            const detailWithoutId: ModifiablePayStubDetail = {
                id: undefined as any,
                name: 'Test Employee',
                workDate: '2024-01-15',
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'JOB123',
                delete: true
            } as ModifiablePayStubDetail;

            const detailInput = buildModifyPayStubDetailInput(detailWithoutId);
            
            // Should return null for details without ID
            expect(detailInput).toBeNull();
        });

        it('should handle empty detail object marked for deletion', () => {
            // Minimal detail marked for deletion
            const minimalDetail: ModifiablePayStubDetail = {
                id: 'UGF5U3R1YkRldGFpbDo0NTY=',
                delete: true
            } as ModifiablePayStubDetail;

            const detailInput = buildModifyPayStubDetailInput(minimalDetail);
            
            // Should still produce valid delete payload
            expect(detailInput).toEqual({
                id: 'UGF5U3R1YkRldGFpbDo0NTY=',
                delete: true
            });
        });
    });

    describe('Array Filtering', () => {
        it('should filter out null values from detail arrays', () => {
            const details: ModifiablePayStubDetail[] = [
                {
                    id: 'UGF5U3R1YkRldGFpbDoxMjM=',
                    name: 'Employee 1',
                    workDate: '2024-01-15',
                    stHours: 8,
                    otHours: 0,
                    dtHours: 0
                } as ModifiablePayStubDetail,
                {
                    id: undefined as any, // Will produce null
                    name: 'Employee 2',
                    workDate: '2024-01-15',
                    stHours: 8,
                    otHours: 0,
                    dtHours: 0,
                    delete: true
                } as ModifiablePayStubDetail,
                {
                    id: 'UGF5U3R1YkRldGFpbDo0NTY=',
                    name: 'Employee 3',
                    workDate: '2024-01-15',
                    stHours: 0,
                    otHours: 0,
                    dtHours: 0,
                    delete: true
                } as ModifiablePayStubDetail
            ];

            const mappedDetails = details
                .map(buildModifyPayStubDetailInput)
                .filter((d): d is NonNullable<typeof d> => d !== null);

            expect(mappedDetails).toHaveLength(2); // Only 2 valid details
            expect(mappedDetails[0]).toHaveProperty('workDate', '2024-01-15');
            expect(mappedDetails[1]).toEqual({
                id: 'UGF5U3R1YkRldGFpbDo0NTY=',
                delete: true
            });
        });
    });
});