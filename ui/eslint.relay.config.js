import relay from 'eslint-plugin-relay';
import { fixupPluginRules } from '@eslint/compat';
import tseslint from 'typescript-eslint';

export default [
    {
        files: ['**/*.{ts,tsx}'],
        languageOptions: {
            parser: tseslint.parser,
            parserOptions: {
                project: '../tsconfig.json',
                tsconfigRootDir: import.meta.dirname
            }
        },
        plugins: {
            relay: fixupPluginRules(relay)
        },
        rules: {
            // Only Relay rules - everything else handled by Biome
            'relay/graphql-syntax': 'error',
            'relay/graphql-naming': 'error',
            'relay/must-colocate-fragment-spreads': 'error',
            'relay/no-future-added-value': 'error',
            'relay/unused-fields': 'error',
            'relay/function-required-argument': 'error',
            'relay/hook-required-argument': 'error'
        }
    }
];
