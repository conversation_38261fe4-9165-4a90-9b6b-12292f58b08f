#!/usr/bin/env node

/**
 * Validation script for generated files clean-up & harmonisation
 * 
 * This script validates that:
 * 1. No non-canonical imports exist (@/src/relay/__generated__)
 * 2. No legacy __generated__ folders exist outside src/relay
 * 3. All generated artefacts are in the canonical location
 * 
 * Exit codes:
 * 0 - All validations passed
 * 1 - Validation failures found
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CANONICAL_GENERATED_DIR = 'src/relay/__generated__';
const NON_CANONICAL_IMPORT_PATTERN = '@/src/relay/__generated__';

let hasErrors = false;

function logError(message) {
    console.error(`❌ ${message}`);
    hasErrors = true;
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logInfo(message) {
    console.log(`ℹ️  ${message}`);
}

/**
 * Check for non-canonical imports in source files
 */
function validateImports() {
    logInfo('Validating import statements...');
    
    try {
        const result = execSync(
            `find src -name "*.ts" -o -name "*.tsx" -o -name "*.mts" -o -name "*.cts" | xargs grep -l "${NON_CANONICAL_IMPORT_PATTERN}" || true`,
            { encoding: 'utf8', cwd: __dirname }
        );
        
        const filesWithNonCanonicalImports = result.trim().split('\n').filter(line => line.length > 0);
        
        if (filesWithNonCanonicalImports.length === 0) {
            logSuccess('No non-canonical imports found');
        } else {
            logError(`Found ${filesWithNonCanonicalImports.length} files with non-canonical imports:`);
            filesWithNonCanonicalImports.forEach(file => {
                console.error(`  - ${file}`);
            });
            
            // Show examples of the problematic imports
            logInfo('Examples of non-canonical imports found:');
            try {
                const examples = execSync(
                    `find src -name "*.ts" -o -name "*.tsx" -o -name "*.mts" -o -name "*.cts" | xargs grep "${NON_CANONICAL_IMPORT_PATTERN}" | head -3`,
                    { encoding: 'utf8', cwd: __dirname }
                );
                console.error(examples);
            } catch (e) {
                // Ignore grep errors
            }
        }
    } catch (error) {
        logError(`Failed to validate imports: ${error.message}`);
    }
}

/**
 * Check for legacy __generated__ folders outside the canonical location
 */
function validateFolders() {
    logInfo('Validating folder structure...');
    
    try {
        const result = execSync(
            `find . -type d -name "__generated__" | grep -v node_modules | grep -v "${CANONICAL_GENERATED_DIR}" || true`,
            { encoding: 'utf8', cwd: __dirname }
        );
        
        const legacyFolders = result.trim().split('\n').filter(line => line.length > 0);
        
        if (legacyFolders.length === 0) {
            logSuccess('No legacy __generated__ folders found');
        } else {
            logError(`Found ${legacyFolders.length} legacy __generated__ folders:`);
            legacyFolders.forEach(folder => {
                console.error(`  - ${folder}`);
            });
        }
    } catch (error) {
        logError(`Failed to validate folders: ${error.message}`);
    }
}

/**
 * Verify the canonical generated directory exists and has content
 */
function validateCanonicalLocation() {
    logInfo('Validating canonical location...');
    
    const canonicalPath = path.join(__dirname, '..', CANONICAL_GENERATED_DIR);
    
    if (!fs.existsSync(canonicalPath)) {
        logError(`Canonical generated directory does not exist: ${CANONICAL_GENERATED_DIR}`);
        return;
    }
    
    try {
        const files = fs.readdirSync(canonicalPath);
        const generatedFiles = files.filter(file => file.endsWith('.graphql.ts'));
        
        if (generatedFiles.length === 0) {
            logError(`Canonical generated directory exists but contains no generated files: ${CANONICAL_GENERATED_DIR}`);
        } else {
            logSuccess(`Canonical generated directory contains ${generatedFiles.length} generated files`);
        }
    } catch (error) {
        logError(`Failed to read canonical directory: ${error.message}`);
    }
}

/**
 * Check for any __generated__ folders in test directories (these are allowed)
 */
function validateTestMocks() {
    logInfo('Checking for test mock __generated__ folders...');
    
    try {
        const result = execSync(
            `find . -path "*/__tests__/**/__generated__" -type d | grep -v node_modules || true`,
            { encoding: 'utf8', cwd: __dirname }
        );
        
        const testMockFolders = result.trim().split('\n').filter(line => line.length > 0);
        
        if (testMockFolders.length > 0) {
            logInfo(`Found ${testMockFolders.length} test mock __generated__ folders (allowed):`);
            testMockFolders.forEach(folder => {
                console.log(`  - ${folder}`);
            });
        }
    } catch (error) {
        // This is not critical, just informational
        logInfo(`Could not check for test mock folders: ${error.message}`);
    }
}

/**
 * Main validation function
 */
function main() {
    console.log('🔍 Generated Files Validation');
    console.log('==============================');
    
    // Change to the UI directory
    process.chdir(path.join(__dirname, '..'));
    
    validateImports();
    validateFolders();
    validateCanonicalLocation();
    validateTestMocks();
    
    console.log('\n==============================');
    
    if (hasErrors) {
        console.error('❌ Validation failed! Please fix the issues above.');
        process.exit(1);
    } else {
        console.log('✅ All validations passed!');
        process.exit(0);
    }
}

// Run the validation if this script is executed directly
if (require.main === module) {
    main();
}

module.exports = {
    validateImports,
    validateFolders,
    validateCanonicalLocation,
    validateTestMocks
};
