{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "include": ["**/*.{js,jsx,ts,tsx,json,css,scss,md}"], "ignore": ["node_modules", "dist", "build", ".next", "out", "coverage", ".cache", "public/build"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 4, "lineWidth": 140, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noExplicitAny": "error", "noArrayIndexKey": "error", "noConsole": "warn"}, "style": {"useImportType": "error", "useTemplate": "error", "useConst": "error"}, "complexity": {"noForEach": "warn", "useOptionalChain": "error", "noExcessiveCognitiveComplexity": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "none", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": true}}, "json": {"formatter": {"enabled": true}}}