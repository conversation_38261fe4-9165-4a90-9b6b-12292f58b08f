# ast-grep Primer for EPR Live Codebase

A quick-start guide for using ast-grep to perform powerful code transformations and searches in our TypeScript/React codebase.

## What is ast-grep?

ast-grep is a structural search and replace tool that works on Abstract Syntax Trees (AST) instead of plain text. Think of it as a supercharged `grep` that understands code structure, making it perfect for large-scale refactoring and precise code searches.

## Installation

ast-grep is already available on this system. Alterntively you can also install it via:

```bash
pnpm install -g @ast-grep/cli --allow-build
or
cargo install ast-grep
```

## Basic Syntax

### Pattern Matching

Use `$VARIABLE` (uppercase) as wildcards to match any AST node:

```bash
# Find all String() conversions
ast-grep --pattern 'String($A)' --lang typescript

# Find specific function calls
ast-grep --pattern 'RelayIdService.toGlobalId($TYPE, $ID)' --lang typescript
```

### Multiple Wildcards

Use `$$$` to match zero or more nodes (like function arguments):

```bash
# Find console.log with any number of arguments
ast-grep --pattern 'console.log($$$ARGS)' --lang typescript
```

## Common Use Cases in Our Codebase

### 1. Finding ID Conversion Patterns

```bash
# Find all String(numericId) patterns
ast-grep --pattern 'String($A.numericId)' --lang typescript

# Find String(RelayIdService.toNumericId()) patterns
ast-grep --pattern 'String(RelayIdService.toNumericId($A))' --lang typescript
```

### 2. React Component Patterns

```bash
# Find useState hooks
ast-grep --pattern 'useState($INITIAL)' --lang typescript

# Find specific props usage
ast-grep --pattern '<$COMPONENT id={$ID} />' --lang typescript
```

### 3. GraphQL Mutation Patterns

```bash
# Find mutation calls
ast-grep --pattern 'modifyTimeSheet($ENV, $INPUT)' --lang typescript

# Find Relay fragments
ast-grep --pattern 'useFragment($FRAGMENT, $DATA)' --lang typescript
```

### 4. Type Guard Patterns

```bash
# Find type guard functions
ast-grep --pattern 'function is$TYPE($PARAM): $PARAM is $RETURN_TYPE' --lang typescript
```

## Advanced Features

### Rewriting Code

Use `--rewrite` or `-r` to transform code:

```bash
# Replace all String(numericId) with global ID conversion
ast-grep --pattern 'String($VAR.numericId)' \
         --rewrite 'RelayIdService.toGlobalId("TimeSheet", $VAR.numericId)' \
         --lang typescript
```

### Multiple Patterns in YAML

Create a `rules.yml` file for complex searches:

```yaml
rules:
  - id: find-numeric-id-conversions
    pattern: "String($A.numericId)"
    language: typescript
    message: "Found numeric ID conversion that may need updating"

  - id: find-legacy-mutations
    pattern:
      any:
        - "id: String($ID)"
        - "id: $ID.toString()"
    language: typescript
    message: "Found legacy ID format in mutation"
```

Run with: `ast-grep scan -c rules.yml`

## Practical Examples from Recent Work

### Finding Timesheet ID Conversions

During our recent global ID migration, these patterns were invaluable:

```bash
# 1. Find all numeric ID to string conversions
ast-grep --pattern 'String($A.numericId)' --lang typescript

# 2. Find String(RelayIdService.toNumericId()) patterns
ast-grep --pattern 'String(RelayIdService.toNumericId($A))' --lang typescript

# 3. Find mutation builders with numeric IDs
ast-grep --pattern 'id: String($NUMERIC_ID)' --lang typescript
```

### React Hook Patterns

```bash
# Find useEffect with specific dependencies
ast-grep --pattern 'useEffect($CALLBACK, [$$$DEPS])' --lang typescript

# Find useState with object initializers
ast-grep --pattern 'useState({ $$$PROPS })' --lang typescript
```

### Error Handling Patterns

```bash
# Find try-catch blocks
ast-grep --pattern 'try { $$$BODY } catch ($ERROR) { $$$HANDLER }' --lang typescript

# Find error throwing
ast-grep --pattern 'throw new $ERROR_TYPE($MESSAGE)' --lang typescript
```

## Tips for Our Codebase

### 1. Language Specification

Always specify `--lang typescript` for our `.ts/.tsx` files:

```bash
ast-grep --pattern '$PATTERN' --lang typescript
```

### 2. File Filtering

Target specific directories:

```bash
# UI components only
ast-grep --pattern '$PATTERN' --lang typescript ui/src/components/

# Mutations only
ast-grep --pattern '$PATTERN' --lang typescript ui/src/mutations/
```

### 3. Combining with grep/rg

Use with ripgrep for text + structure searches:

```bash
# First find files mentioning "timesheet"
rg -l "timesheet" ui/src/ | xargs ast-grep --pattern 'String($A.id)' --lang typescript
```

### 4. Testing Changes

Before making bulk changes, use `--dry-run` equivalent by redirecting output:

```bash
ast-grep --pattern 'String($A.numericId)' --lang typescript > matches.txt
```

## Performance Tips

- ast-grep is very fast and can handle our entire codebase in seconds
- Use file globs to narrow scope: `ui/src/**/*.ts`
- For one-off searches, command line is fastest
- For repeated patterns, use YAML rules

## Common Gotchas

1. **Exact matching**: ast-grep matches structure exactly - whitespace and formatting matter
2. **Type imports**: Remember that `import type` has different AST structure than regular imports
3. **JSX**: Use appropriate patterns for JSX elements vs. function calls
4. **Generics**: Generic type parameters need special handling in patterns

## Integration with Development Workflow

### Pre-commit Checks

Add ast-grep rules to catch problematic patterns:

```bash
# Check for legacy ID patterns before commit
ast-grep --pattern 'String($A.numericId)' --lang typescript ui/src/
```

### Refactoring Workflow

1. Use ast-grep to identify all instances
2. Test changes on a small subset
3. Apply bulk transformations
4. Run tests and type checking
5. Manual review of edge cases

## Resources

- [Official ast-grep Guide](https://ast-grep.github.io/guide/quick-start.html)
- [Pattern Syntax Reference](https://ast-grep.github.io/guide/pattern-syntax.html)
- Internal examples in this codebase: search for recent commits mentioning "ast-grep"

---

**Pro Tip**: Start with simple patterns and gradually increase complexity. ast-grep's precision makes it perfect for the kind of large-scale TypeScript refactoring we do in this codebase.
