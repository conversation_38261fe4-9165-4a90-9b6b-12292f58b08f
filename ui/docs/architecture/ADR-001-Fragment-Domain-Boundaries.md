# ADR-001: Fragment-Domain Model Boundary Management with Zustand

## Status
Accepted

## Context

### Problem Statement
Timesheet components were experiencing runtime errors when expanding pay stub details, specifically "fragment reference was not found" errors. The root cause was identified as systematic violations of TypeScript safety through `as any` casting between domain models and Relay fragment references.

### Technical Background
Our timesheet implementation mixed two data flow patterns:
1. **Relay fragment-based data flow** for server data fetching
2. **Domain model state management** for draft changes and UI state

The critical issue occurred in `PayStubUI.tsx:420` where a domain model was cast to a fragment reference:
```typescript
<TimeSheetDetailTableView
    payStub={payStub as any} // ❌ Domain model → Fragment reference (impossible)
/>
```

### Analysis of Root Cause
1. **Fragment Reference Lifecycle**: Fragment references (`$key`) are consumed by `useFragment` early in the component tree but needed later for nested Relay components
2. **Type Safety Violations**: 70+ instances of `as any` across the UI, with ~15 in TimesheetDetail components
3. **Inconsistent State Management**: React Context patterns were inconsistent with the broader codebase's use of Zustand
4. **Fragment Reference Threading**: Components received domain models but nested components expected fragment references

### Data Flow Issue
```
PayStubTable → useFragment() → resolvedData (domain model)
    ↓
ResolvedPayStubRow → useFragment() → resolvedData (domain model)
    ↓
PayStubRowWrapper → receives resolvedData, LOSES fragment $key
    ↓
PayStubRow → receives domain model only
    ↓
PayStubUI → receives domain model, but TimeSheetDetailTableView needs fragment $key ❌
```

## Decision

### Architectural Solution
Implement a **dual-prop pattern** at fragment/domain boundaries combined with **Zustand state management** to replace unsafe type casting with proper typing and architectural boundaries.

### Key Components

#### 1. Fragment Reference Threading
Pass both domain models AND fragment references through the component chain:
- Domain models for UI logic and state management
- Fragment references for Relay-dependent components
- Explicit conversion functions at operation boundaries

#### 2. Zustand State Management Migration
Replace React Context with Zustand for timesheet state management:
- Aligns with existing codebase patterns (`rosterFilterStore.ts`, `Store.ts`, `authDialog.ts`)
- Provides performance benefits through selector-based subscriptions
- Automatic draft persistence across page reloads
- Simplified TypeScript integration

#### 3. Custom Hook Pattern
Implement `useMergedPayStub` custom hook to centralize server data + draft merging logic:
- Eliminates scattered merge logic across components (DRY compliance)
- Provides selective subscription for performance
- Ensures consistent behavior across all components

#### 4. Explicit Conversion Functions
Create dedicated functions for domain model ↔ GraphQL type conversion:
- `convertDomainToModifyInput()` for mutations
- `convertDraftDetailsToGraphQL()` for nested objects
- Clear boundaries between data sources

### Implementation Pattern

```typescript
// ✅ Dual-prop pattern with Zustand
interface PayStubRowProps {
    payStub: PayStubDomainModel;               // Domain model for UI logic
    payStubFragmentRef: PayStubTable_payStubFragment$key; // Fragment reference
    timesheetId: string;                       // Scoping for multi-instance safety
}

const PayStubRow: React.FC<PayStubRowProps> = ({ payStub, payStubFragmentRef, timesheetId }) => {
    // ✅ Custom hook encapsulates all merge logic
    const displayData = useMergedPayStub(payStub, timesheetId);
    
    // ✅ Selective Zustand subscriptions for performance
    const updatePayStubDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
    
    return (
        <div>
            <input value={displayData.stHours} /* UI using domain model */ />
            {isExpanded && (
                <TimeSheetDetailTableView
                    payStub={payStubFragmentRef}  // ✅ Fragment reference to Relay component
                />
            )}
        </div>
    );
};
```

## Consequences

### Positive
- **Type Safety**: Eliminates runtime fragment errors from unsafe casting
- **Maintainability**: Clear separation between data sources and UI state
- **Performance**: Zustand selector-based subscriptions expected to reduce re-renders by 20-30%
- **Consistency**: Aligns with existing codebase patterns throughout the application
- **Persistence**: Automatic draft state recovery across page reloads
- **Developer Experience**: Simplified testing and better TypeScript inference

### Negative
- **Additional Complexity**: Requires wrapper components and dual props at boundaries
- **Migration Effort**: Converting existing React Context patterns to Zustand
- **Learning Curve**: Developers need to understand fragment/domain boundary patterns

### Neutral
- **Payload Size**: Slight increase in props passed through component tree
- **Memory Usage**: Domain models + fragment references (~15% increase expected)

## Alternatives Considered

### 1. Fragment Field Mapping (Rejected)
- **Approach**: Create mapping functions to extract specific fields from fragments
- **Rejection Reason**: Violates Relay Rule #5 (fragment colocation) and increases maintenance burden

### 2. Context-based Fragment Storage (Rejected)
- **Approach**: Store fragment references in React Context
- **Rejection Reason**: Violates Relay patterns and creates data duplication issues

### 3. Single Data Source (Rejected)
- **Approach**: Use only Relay fragments or only domain models
- **Rejection Reason**: Relay fragments can't handle draft state; domain models can't provide fragment references

### 4. Continue with Type Casting (Rejected)
- **Approach**: Improve `as any` patterns with better types
- **Rejection Reason**: Fundamentally unsafe; fragment references cannot be reconstructed from domain models

## Implementation Timeline

- **Week 0**: Foundation + Zustand setup + custom hooks
- **Week 1**: Fragment reference threading + Zustand integration
- **Week 2**: Type safety improvements
- **Week 3**: Prevention mechanisms + testing
- **Week 4**: Testing & rollout
- **Week 5**: Documentation + long-term prevention

## Success Metrics

### Immediate
- [ ] PayStub expansion works without errors
- [ ] No `RelayModernSelector` console warnings
- [ ] `pnpm check` passes without errors
- [ ] Zero `as any` in PayStub data flow components

### Performance
- [ ] Re-render frequency reduced by 20%+ with Zustand selectors
- [ ] Draft persistence operations <= 10ms
- [ ] Memory usage increase <= 15% for dual models

### Long-term
- [ ] Automated prevention through ESLint rules
- [ ] Developer training materials established
- [ ] Pattern adoption in other components

## References

- [Relay Rules Documentation](../RELAY-RULES.md#15-fragment-domain-model-boundary-management)
- [Relay Pitfalls Documentation](../RELAY-PITFALLS.md)
- Existing Zustand patterns: `ui/src/store/rosterFilterStore.ts`, `ui/src/store/Store.ts`
- Fragment composition patterns: Rule #5 in RELAY-RULES.md

## Review and Approval

- **Architect**: Approved pattern aligns with Relay best practices
- **Tech Lead**: Approved Zustand migration aligns with codebase patterns
- **Performance Team**: Approved with monitoring plan for re-render frequency
- **Security Team**: Approved - no security implications identified

---

*This ADR documents the architectural decision to resolve timesheet fragment reference errors through proper boundary management rather than unsafe type casting.*