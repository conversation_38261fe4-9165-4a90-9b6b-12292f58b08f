using System.Linq;
using System.Security.Claims;
using backend.Data.Models;
using backend.Types.Inputs;
using backend.Types.Payloads;
using backend.Types.Relay;
using backend.Utils;
using FluentValidation;
using HotChocolate.Authorization;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Mutations;

[MutationType]
[Authorize]
public partial class TimesheetMutations
{
    // Inject IHttpContextAccessor via constructor
    private readonly IHttpContextAccessor _httpContextAccessor;

    // Inject INodeIdSerializer
    private readonly INodeIdSerializer _nodeIdSerializer;

    // Inject TypeAdapterConfig
    private readonly TypeAdapterConfig _mapperConfig;

    public TimesheetMutations(
        IHttpContextAccessor httpContextAccessor,
        INodeIdSerializer nodeIdSerializer,
        TypeAdapterConfig mapperConfig
    )
    {
        _httpContextAccessor =
            httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _nodeIdSerializer =
            nodeIdSerializer ?? throw new ArgumentNullException(nameof(nodeIdSerializer));
        _mapperConfig = mapperConfig ?? throw new ArgumentNullException(nameof(mapperConfig));
    }

    // Helper to get User ID (using HttpContextAccessor)
    private string? GetCurrentUserId()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null)
        {
            return null;
        }

        var isAuthenticated = httpContext.User?.Identity?.IsAuthenticated ?? false;
        if (!isAuthenticated)
        {
            return null;
        }

        // --- Find the User ID --- Prioritize 'username' claim based on logs
        string? userId = httpContext.User?.FindFirstValue("username");

        if (userId == null)
        {
            // Fallback 1: Try NameIdentifier
            userId = httpContext.User?.FindFirstValue(ClaimTypes.NameIdentifier);
        }

        if (userId == null)
        {
            // Fallback 2: Try 'sub'
            userId = httpContext.User?.FindFirstValue("sub");
        }

        return userId;
    }

    public async Task<int> DeleteTimesheetAsync(
        [ID(nameof(TimeSheet))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        // Fetch minimally to ensure it exists before delete attempt
        var timeSheetToDelete = await dbContext
            .TimeSheets.Where(ts => ts.Id == id)
            .Select(ts => new { ts.Id })
            .SingleOrDefaultAsync(cancellationToken);

        if (timeSheetToDelete == null)
        {
            throw new GraphQLException($"Timesheet with ID {id} not found for deletion.");
        }

        // Remove the timesheet - use a stub entity for efficiency
        dbContext.TimeSheets.Remove(new TimeSheet { Id = id });
        await dbContext.SaveChangesAsync(cancellationToken);

        // Return the ID of the deleted object for Relay cache updates
        return id;
    }

    public async Task<AddTimesheetPayload> AddTimesheetAsync(
        AddTimesheetInput input,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        // Map the entire input to the TimeSheet entity in a single call
        var timeSheet = input.Adapt<TimeSheet>();

        // --- BEGIN QUICK-FIX: Ensure EmployerGuid and PayPeriodEndDate are mapped correctly ---
        timeSheet.EmployerGuid = input.employerGuid;
        timeSheet.PayPeriodEndDate = input.payPeriodEndDate;
        // --- END QUICK-FIX ---

        // Ensure new timesheets have a valid initial status
        if (string.IsNullOrWhiteSpace(timeSheet.Status))
        {
            timeSheet.Status = "New";
        }

        // Set server-generated values
        var now = DateTimeOffset.UtcNow;
        var userId = GetCurrentUserId();

        timeSheet.CreationDate = now;
        timeSheet.ModificationDate = now;
        timeSheet.CreatedByUserId = userId;
        timeSheet.ModifiedByUserId = userId;

        // Check if this is a copy operation with source timesheet
        bool isCopyOperation =
            input.sourceTimesheetId.HasValue && input.sourceTimesheetId > 0 && input.isCopy == true;

        if (isCopyOperation)
        {
            await ProcessTimesheetCopyAsync(timeSheet, input, dbContext, cancellationToken);
        }

        // Generate IDs for all entities in the graph
        if (timeSheet.PayStubs != null)
        {
            foreach (var payStub in timeSheet.PayStubs)
            {
                // PayStub ID is auto-generated; EF will populate FK (TimeSheetId) once the parent is saved

                if (payStub.Details != null)
                {
                    // Only filter details if this is not a copy operation
                    // For copy operations, details will be properly handled by ProcessTimesheetCopyAsync
                    if (!isCopyOperation)
                    {
                        // Filter out invalid details
                        var validDetails = payStub
                            .Details.Where(d =>
                                d.STHours > 0
                                || d.OTHours > 0
                                || d.DTHours > 0
                                || d.Bonus > 0
                                || d.Expenses > 0
                                || !string.IsNullOrEmpty(d.JobCode)
                                || !string.IsNullOrEmpty(d.CostCenter)
                            )
                            .ToList();

                        // Clear and replace with only valid details
                        payStub.Details.Clear();
                        foreach (var detail in validDetails)
                        {
                            // PayStubDetail ID will be auto-generated by EF Core IDENTITY column
                            // PayStubId FK will be set automatically when PayStub is saved
                            payStub.Details.Add(detail);
                        }
                    }
                    else
                    {
                        // For copy operations, IDs will be auto-generated by EF Core when saved
                        // PayStubId FKs will be set automatically when PayStub is saved
                    }
                }
            }
        }

        // Add the root entity to the context
        dbContext.TimeSheets.Add(timeSheet);

        // Single SaveChanges call
        await dbContext.SaveChangesAsync(cancellationToken);

        // Generate cursor for TimeSheet (int ID works directly with default serializer)
        var cursor = _nodeIdSerializer.Format(nameof(TimeSheet), timeSheet.Id);

        // Create a dynamic edge object with the same structure as HotChocolate's edge type
        var edge = new Edge<TimeSheet> { Cursor = cursor, Node = timeSheet };

        // Return the edge in the payload
        return new AddTimesheetPayload(timeSheetEdge: edge);
    }

    /// <summary>
    /// Processes timesheet copy operation when a source timesheet is specified
    /// </summary>
    /// <param name="targetTimesheet">The target timesheet to copy data to</param>
    /// <param name="input">The input containing copy parameters</param>
    /// <param name="dbContext">Database context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ProcessTimesheetCopyAsync(
        TimeSheet targetTimesheet,
        AddTimesheetInput input,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        // Fetch the source timesheet with all its paystubs and details
        var sourceTimesheet = await dbContext
            .TimeSheets.Include(ts => ts.PayStubs)
            .ThenInclude(ps => ps.Details)
            .FirstOrDefaultAsync(ts => ts.Id == input.sourceTimesheetId!.Value, cancellationToken);

        if (sourceTimesheet == null)
        {
            // Source timesheet not found, just continue without copying
            return;
        }

        // Check if the target timesheet has any pay stubs provided by the frontend
        bool hasPayStubsFromFrontend =
            targetTimesheet.PayStubs != null && targetTimesheet.PayStubs.Any();

        // If no pay stubs are provided by the frontend, or the provided ones are empty shells,
        // copy all pay stubs from the source timesheet
        if (!hasPayStubsFromFrontend)
        {
            CopyAllPayStubs(targetTimesheet, sourceTimesheet);
        }
        else
        {
            // Input contains PayStubs - check if they need to be enhanced with details from source
            EnhancePayStubsWithSourceDetails(targetTimesheet, sourceTimesheet, input);
        }
    }

    /// <summary>
    /// Copies all pay stubs from source timesheet to target timesheet.
    /// This method is called when the frontend has not provided any pay stubs
    /// or the provided pay stubs are empty.
    /// </summary>
    /// <remarks>
    /// The algorithm works as follows:
    /// 1. Create a new PayStubs collection for the target timesheet
    /// 2. For each pay stub in the source timesheet:
    ///    a. Create a new pay stub with a new ID
    ///    b. Copy all relevant properties from the source pay stub
    ///    c. For each detail in the source pay stub, create a new detail
    ///       and add it to the new pay stub
    /// 3. Add all the new pay stubs to the target timesheet
    ///
    /// This path is intended for API or backend-driven operations where the frontend
    /// doesn't construct pay stubs but relies completely on the backend to copy them.
    /// </remarks>
    private void CopyAllPayStubs(TimeSheet targetTimesheet, TimeSheet sourceTimesheet)
    {
        // Replace any existing pay stubs with a new collection
        targetTimesheet.PayStubs = new List<PayStub>();

        // Copy PayStubs from source
        foreach (var sourcePayStub in sourceTimesheet.PayStubs)
        {
            var newPayStub = new PayStub
            {
                // ID will be auto-generated by EF Core IDENTITY column
                TimeSheetId = targetTimesheet.Id,
                EmployeeId = sourcePayStub.EmployeeId,
                Name = sourcePayStub.Name,
                Details = new List<PayStubDetail>(),
            };

            // Copy Details from source
            foreach (var sourceDetail in sourcePayStub.Details)
            {
                // Only copy details that have meaningful data
                if (!PayStubValidationUtils.HasMeaningfulData(sourceDetail))
                    continue;

                var newDetail = CreateDetailFromSource(sourceDetail);
                newPayStub.Details.Add(newDetail);
            }

            targetTimesheet.PayStubs.Add(newPayStub);
        }
    }

    /// <summary>
    /// Enhances existing pay stubs with details from the source timesheet.
    /// This method is called when the frontend has provided paystubs but they need details
    /// from the source timesheet to be fully populated.
    /// </summary>
    /// <remarks>
    /// The algorithm works as follows:
    /// 1. Create a dictionary of source paystubs by employee ID for fast lookup
    /// 2. For each target paystub:
    ///    a. Find the corresponding source paystub by employee ID
    ///    b. If not found, skip this paystub (no source data available)
    ///    c. Check if the target paystub has meaningful data:
    ///       - If it does NOT have meaningful data (empty or just placeholders),
    ///         replace its details with data from the source paystub
    ///       - If it DOES have meaningful data, keep its existing details
    ///         (the frontend has provided useful data that should be preserved)
    /// </remarks>
    private void EnhancePayStubsWithSourceDetails(
        TimeSheet targetTimesheet,
        TimeSheet sourceTimesheet,
        AddTimesheetInput input
    )
    {
        // Create a dictionary of source paystubs by employee ID for efficient lookup
        var sourcePayStubsByEmployeeId = sourceTimesheet.PayStubs.ToDictionary(ps => ps.EmployeeId);

        foreach (var payStub in targetTimesheet.PayStubs)
        {
            // Skip if we don't have a matching source paystub
            if (!sourcePayStubsByEmployeeId.TryGetValue(payStub.EmployeeId, out var sourcePayStub))
                continue;

            // If the paystub doesn't have details or only has placeholder details without meaningful data,
            // copy details from the source paystub
            if (
                payStub.Details == null
                || !payStub.Details.Any()
                || (
                    payStub.Details.Count == 1
                    && !PayStubValidationUtils.HasMeaningfulData(payStub.Details.First())
                )
            )
            {
                // Create a new details collection (clear any existing placeholder details)
                payStub.Details = new List<PayStubDetail>();

                // Copy details from source that have meaningful data
                foreach (var sourceDetail in sourcePayStub.Details)
                {
                    // Skip copying details with no meaningful data
                    if (!PayStubValidationUtils.HasMeaningfulData(sourceDetail))
                        continue;

                    // Create a new detail based on source, applying the copy options
                    var newDetail = CreateDetailFromSource(
                        sourceDetail,
                        input.includeJobCodes == true,
                        input.includeHours == true
                    );

                    payStub.Details.Add(newDetail);
                }
            }
            // If paystub already has meaningful details, they were provided by the frontend
            // and we should preserve them (not replacing with source details)
        }
    }

    /// <summary>
    /// Creates a new PayStubDetail from a source detail.
    /// </summary>
    /// <param name="payStubId">The ID of the pay stub this detail will belong to</param>
    /// <param name="sourceDetail">The source detail to copy from</param>
    /// <param name="includeJobCodes">Whether to include job codes and related fields</param>
    /// <param name="includeHours">Whether to include hours and related numeric data</param>
    /// <returns>A new PayStubDetail instance with data from the source</returns>
    /// <remarks>
    /// This method allows for selective copying based on the copy options:
    /// - When includeJobCodes is false, job code-related fields are set to null
    /// - When includeHours is false, numeric hour/rate fields are set to null
    /// The method always copies the basic fields like ID, name, and work date
    /// </remarks>
    private PayStubDetail CreateDetailFromSource(
        PayStubDetail sourceDetail,
        bool includeJobCodes = true,
        bool includeHours = true
    )
    {
        return new PayStubDetail
        {
            // ID will be auto-generated by EF Core IDENTITY column
            // PayStubId will be set automatically when added to PayStub.Details collection
            Name = sourceDetail.Name,
            WorkDate = sourceDetail.WorkDate,
            // Only copy non-numeric data if includeJobCodes is true
            JobCode = includeJobCodes ? sourceDetail.JobCode : null,
            CostCenter = includeJobCodes ? sourceDetail.CostCenter : null,
            EarningsCode = includeJobCodes ? sourceDetail.EarningsCode : null,
            AgreementId = includeJobCodes ? sourceDetail.AgreementId : null,
            ClassificationId = includeJobCodes ? sourceDetail.ClassificationId : null,
            SubClassificationId = includeJobCodes ? sourceDetail.SubClassificationId : null,
            // Only copy numeric data if includeHours is true
            OTHours = includeHours ? sourceDetail.OTHours : null,
            STHours = includeHours ? sourceDetail.STHours : null,
            DTHours = includeHours ? sourceDetail.DTHours : null,
            // TotalHours is now computed automatically from STHours + OTHours + DTHours
            HourlyRate = includeHours ? sourceDetail.HourlyRate : null,
            Bonus = includeHours ? sourceDetail.Bonus : null,
            Expenses = includeHours ? sourceDetail.Expenses : null,
            ReportLineItemId = sourceDetail.ReportLineItemId,
        };
    }

    /// <summary>
    /// Modifies an existing timesheet and its associated pay stubs and details based on the provided input.
    /// </summary>
    /// <param name="input">The input containing updated timesheet data, including pay stubs to add, modify, or delete.</param>
    /// <param name="dbContext">The database context used for data access.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>The updated <see cref="TimeSheet"/> entity.</returns>
    /// <remarks>
    /// Throws a <see cref="GraphQLException"/> if the timesheet is not found or if an unexpected error occurs. All changes are performed within a database transaction to ensure atomicity.
    /// </remarks>
    public async Task<TimeSheet> ModifyTimeSheetAsync(
        ModifyTimeSheetInput input,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        // Begin DB transaction to ensure atomicity of PayStub operations
        await using var tx = await dbContext.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            // Fetch existing graph with eager loading
            TimeSheet? timeSheet =
                await dbContext
                    .TimeSheets.Include(ts => ts.PayStubs)
                    .ThenInclude(ps => ps.Details)
                    .FirstOrDefaultAsync(ts => ts.Id == input.Id, cancellationToken)
                ?? throw new GraphQLException($"Timesheet with id {input.Id} not found.");

            // Update TimeSheet scalar properties using Mapster (respecting ignores)
            input.Adapt(timeSheet, _mapperConfig);

            // Set modification date and user ID
            timeSheet.ModificationDate = DateTimeOffset.UtcNow;
            timeSheet.ModifiedByUserId = GetCurrentUserId();

            // Update specific properties that may have been provided in the input
            if (input.status.HasValue)
                timeSheet.Status = input.status.Value ?? timeSheet.Status;
            if (input.type.HasValue)
                timeSheet.Type = input.type.Value ?? timeSheet.Type;

            // Process child collections (PayStubs using new separated arrays)
            ProcessPayStubs(
                input.addPayStubs,
                input.modifyPayStubs,
                input.deletePayStubIds,
                timeSheet,
                dbContext
            );

            // Auto-update status to "Saved" when pay stubs are added and status is not explicitly provided
            if (!input.status.HasValue && input.addPayStubs != null && input.addPayStubs.Any())
            {
                timeSheet.Status = "Saved";
            }

            // Persist all changes in a single call
            await dbContext.SaveChangesAsync(cancellationToken);
            await tx.CommitAsync(cancellationToken);

            return timeSheet;
        }
        catch (ValidationException)
        {
            await tx.RollbackAsync(cancellationToken);
            throw;
        }
        catch (Exception)
        {
            await tx.RollbackAsync(cancellationToken);
            throw new GraphQLException(
                "An unexpected error occurred while modifying the timesheet."
            );
        }
    }

    // ========================================================================
    // Helper methods for PayStub Processing within ModifyTimeSheetAsync
    // ========================================================================

    private void ProcessPayStubs(
        List<AddPayStubInput>? addPayStubs,
        List<ModifyPayStubInput>? modifyPayStubs,
        List<int>? deletePayStubIds,
        TimeSheet timeSheet,
        EPRLiveDBContext dbContext
    )
    {
        timeSheet.PayStubs ??= new List<PayStub>();

        // Build lookup of existing by int ID for quick updates
        var existingMap = timeSheet.PayStubs.ToDictionary(ps => ps.Id);

        // Create set of IDs to delete for efficient lookups
        var idsToDelete = deletePayStubIds?.ToHashSet() ?? new HashSet<int>();

        // --- UPDATE (before DELETE to avoid conflicts) ---
        if (modifyPayStubs != null)
        {
            foreach (var mod in modifyPayStubs)
            {
                // Skip modification if this PayStub is also marked for deletion
                if (idsToDelete.Contains(mod.Id))
                {
                    continue;
                }

                if (!existingMap.TryGetValue(mod.Id, out var existing))
                {
                    throw new ValidationException(
                        $"PayStub with ID {mod.Id} not found for modification."
                    );
                }

                UpdateExistingPayStub(existing, mod);

                ProcessPayStubDetails(mod.details, existing, dbContext);
            }
        }

        // --- DELETE ---
        if (deletePayStubIds != null && deletePayStubIds.Any())
        {
            var stubsToDelete = timeSheet
                .PayStubs.Where(ps => idsToDelete.Contains(ps.Id))
                .ToList();
            if (stubsToDelete.Any())
            {
                var detailsToDelete = dbContext
                    .PayStubDetails.Where(d => idsToDelete.Contains(d.PayStubId))
                    .ToList();
                if (detailsToDelete.Any())
                {
                    dbContext.PayStubDetails.RemoveRange(detailsToDelete);
                }

                dbContext.PayStubs.RemoveRange(stubsToDelete);
                foreach (var stub in stubsToDelete)
                {
                    timeSheet.PayStubs.Remove(stub);
                }
            }
        }

        // --- ADD ---
        if (addPayStubs != null)
        {
            foreach (var add in addPayStubs)
            {
                var newStub = new PayStub
                {
                    // ID will be auto-generated by EF Core IDENTITY column
                    TimeSheetId = timeSheet.Id,
                    EmployeeId = add.employeeId,
                    Name = add.name,
                    Details = new List<PayStubDetail>(),
                };

                timeSheet.PayStubs.Add(newStub);

                if (add.details != null && add.details.Any())
                {
                    foreach (var detailInput in add.details)
                    {
                        var detail = new PayStubDetail
                        {
                            // ID will be auto-generated by EF Core IDENTITY column
                            // PayStubId will be set automatically when PayStub is saved
                            Name = detailInput.name,
                            WorkDate = detailInput.workDate,
                            JobCode = detailInput.jobCode,
                            CostCenter = detailInput.costCenter,
                            EarningsCode = detailInput.earningsCode,
                            AgreementId = detailInput.agreementId,
                            ClassificationId = detailInput.classificationId,
                            SubClassificationId = detailInput.subClassificationId,
                            OTHours = detailInput.OTHours,
                            STHours = detailInput.STHours,
                            DTHours = detailInput.DTHours,
                            HourlyRate = detailInput.hourlyRate,
                            Bonus = detailInput.bonus,
                            Expenses = detailInput.expenses,
                            ReportLineItemId = detailInput.reportLineItemId,
                        };
                        newStub.Details.Add(detail);
                    }
                }
                // Header-only PayStubs are now allowed - no details required
            }
        }
    }

    // Helper to update scalar fields on an existing PayStub from ModifyPayStubInput
    private void UpdateExistingPayStub(PayStub existingPayStub, ModifyPayStubInput payStubInput)
    {
        // For now only name can be updated; consider if EmployeeId can change
        existingPayStub.Name = payStubInput.name ?? existingPayStub.Name;
        // If you decide EmployeeId is mutable uncomment:
        // existingPayStub.EmployeeId = payStubInput.employeeId;
        // Any additional fields added in future should be mapped here.
    }

    // ========================================================================
    // Helper methods for PayStubDetail Processing
    // ========================================================================

    // Helper method to process PayStubDetails for a given PayStub
    private void ProcessPayStubDetails(
        List<ModifyPayStubDetailInput>? inputDetails,
        PayStub payStub,
        EPRLiveDBContext dbContext
    )
    {
        // Ensure Details collection is initialized
        payStub.Details ??= new List<PayStubDetail>();

        if (inputDetails == null || !inputDetails.Any())
        {
            // Header-only PayStubs are allowed - remove all existing details if any exist
            RemoveAllPayStubDetails(payStub, dbContext);
            return;
        }

        // Create lookups for efficient processing
        var existingDetails = payStub.Details.ToDictionary(d => d.Id);
        var inputDetailIds = inputDetails
            .Where(d => d.id.HasValue && d.delete != true)
            .Select(d => d.id!.Value)
            .ToList();

        // Remove details not in input or marked for deletion
        RemovePayStubDetails(payStub, inputDetailIds, inputDetails, dbContext);

        // Add or Update details based on input
        AddOrUpdatePayStubDetails(payStub, inputDetails, existingDetails, dbContext);
    }

    private void RemoveAllPayStubDetails(PayStub payStub, EPRLiveDBContext dbContext)
    {
        if (payStub.Details.Any())
        {
            var detailsToRemove = payStub.Details.ToList(); // Get details to remove
            dbContext.PayStubDetails.RemoveRange(detailsToRemove); // Queue them for removal
            payStub.Details.Clear(); // Clear the navigation property
        }
    }

    private void RemovePayStubDetails(
        PayStub payStub,
        IEnumerable<int> inputDetailIds,
        List<ModifyPayStubDetailInput> inputDetails,
        EPRLiveDBContext dbContext
    )
    {
        var detailsToDelete = payStub
            .Details.Where(d =>
                !inputDetailIds.Contains(d.Id)
                || inputDetails.Any(i => i.id.HasValue && i.id.Value == d.Id && i.delete == true)
            )
            .ToList();

        // Remove the details from the PayStub
        foreach (var detailToDelete in detailsToDelete)
        {
            payStub.Details.Remove(detailToDelete); // Remove from navigation property
            dbContext.PayStubDetails.Remove(detailToDelete); // Queue for removal from DB
        }
    }

    // ========================================================================
    // Helper methods for PayStubDetail Validation
    // ========================================================================

    private bool IsPayStubDetailInputEmpty(ModifyPayStubDetailInput input)
    {
        return (input.STHours ?? 0) == 0
            && (input.OTHours ?? 0) == 0
            && (input.DTHours ?? 0) == 0
            && (input.bonus ?? 0) == 0
            && (input.expenses ?? 0) == 0
            && string.IsNullOrWhiteSpace(input.jobCode)
            && string.IsNullOrWhiteSpace(input.costCenter)
            && string.IsNullOrWhiteSpace(input.earningsCode);
    }

    // ========================================================================
    // Helper methods for PayStubDetail Add/Update
    // ========================================================================

    private void AddOrUpdatePayStubDetails(
        PayStub payStub,
        List<ModifyPayStubDetailInput> inputDetails,
        Dictionary<int, PayStubDetail> existingDetails,
        EPRLiveDBContext dbContext
    )
    {
        // Process each detail input for adds/updates
        foreach (var detailInput in inputDetails)
        {
            // Skip details marked for deletion (already handled in RemovePayStubDetails)
            if (detailInput.delete == true)
                continue;

            // *** Phase 4: Input Validation ***
            // Skip adding or updating if the input detail is considered "empty"
            if (IsPayStubDetailInputEmpty(detailInput))
            {
                // Optionally: If detailInput.id has value (meaning it's an update attempt
                // with empty data), we could consider deleting the existing detail.
                // For now, we simply skip processing empty inputs.
                continue;
            }

            // Determine if this is an update or add
            PayStubDetail? detail = null;
            if (detailInput.id.HasValue)
            {
                existingDetails.TryGetValue(detailInput.id.Value, out detail);
            }

            if (detail == null)
            {
                // Add new detail
                var newDetail = AddNewPayStubDetail(payStub, detailInput);
                payStub.Details.Add(newDetail); // Add to navigation collection
                // No need to add to dbContext explicitly if cascade add is working
                // or if adding to the parent's collection handles it.
            }
            else
            {
                // Update existing detail
                UpdateExistingPayStubDetail(detail, detailInput);
            }
        }
    }

    private PayStubDetail AddNewPayStubDetail(PayStub payStub, ModifyPayStubDetailInput detailInput)
    {
        // Manual mapping for new detail
        if (detailInput.workDate == null)
            throw new GraphQLException(
                ErrorBuilder.New()
                    .SetMessage("WorkDate is required for new PayStub details.")
                    .SetCode("INVALID_INPUT")
                    .SetExtension("field", "workDate")
                    .Build());
            
        var newDetail = new PayStubDetail
        {
            // ID will be auto-generated by EF Core IDENTITY column
            // PayStubId will be set automatically when added to PayStub.Details collection
            Name = detailInput.name,
            WorkDate = detailInput.workDate!.Value,
            OTHours = detailInput.OTHours,
            STHours = detailInput.STHours,
            DTHours = detailInput.DTHours,
            // TotalHours is now computed automatically from STHours + OTHours + DTHours
            JobCode = detailInput.jobCode,
            EarningsCode = detailInput.earningsCode,
            AgreementId = detailInput.agreementId,
            ClassificationId = detailInput.classificationId,
            SubClassificationId = detailInput.subClassificationId,
            CostCenter = detailInput.costCenter,
            HourlyRate = detailInput.hourlyRate,
            Bonus = detailInput.bonus,
            Expenses = detailInput.expenses,
            ReportLineItemId = detailInput.reportLineItemId,
        };
        return newDetail;
    }

    private void UpdateExistingPayStubDetail(
        PayStubDetail existingDetail,
        ModifyPayStubDetailInput detailInput
    )
    {
        // Manual mapping for existing detail update
        existingDetail.Name = detailInput.name;
        existingDetail.WorkDate = detailInput.workDate ?? existingDetail.WorkDate;
        existingDetail.OTHours = detailInput.OTHours;
        existingDetail.STHours = detailInput.STHours;
        existingDetail.DTHours = detailInput.DTHours;
        // TotalHours is now computed automatically from STHours + OTHours + DTHours
        existingDetail.JobCode = detailInput.jobCode;
        existingDetail.EarningsCode = detailInput.earningsCode;
        existingDetail.AgreementId = detailInput.agreementId;
        existingDetail.ClassificationId = detailInput.classificationId;
        existingDetail.SubClassificationId = detailInput.subClassificationId;
        existingDetail.CostCenter = detailInput.costCenter;
        existingDetail.HourlyRate = detailInput.hourlyRate;
        existingDetail.Bonus = detailInput.bonus;
        existingDetail.Expenses = detailInput.expenses;
        existingDetail.ReportLineItemId = detailInput.reportLineItemId;
        // Note: PayStubId and Id are not updated as they are identifiers
    }

    /// <summary>
    /// Creates an empty PayStub for a specific employee in a timesheet.
    /// This allows immediate saving of PayStub records without requiring all detail data upfront.
    /// </summary>
    /// <param name="input">Input containing timeSheetId and employeeId</param>
    /// <param name="dbContext">Database context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Payload containing the created PayStub or error information</returns>
    public async Task<AddEmptyPayStubPayload> AddEmptyPayStubAsync(
        AddEmptyPayStubInput input,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Validate timesheet exists and user has permission
            var timeSheet = await dbContext.TimeSheets.FirstOrDefaultAsync(
                ts => ts.Id == input.timeSheetId,
                cancellationToken
            );

            if (timeSheet == null)
            {
                return new AddEmptyPayStubPayload(errors: new[] { "TimeSheet not found" });
            }

            // Validate employee exists
            var employee = await dbContext.Employees.FirstOrDefaultAsync(
                e => e.Id == input.employeeId,
                cancellationToken
            );

            if (employee == null)
            {
                return new AddEmptyPayStubPayload(errors: new[] { "Employee not found" });
            }

            // Check if PayStub already exists for this employee/timesheet combination
            var existingPayStub = await dbContext.PayStubs.FirstOrDefaultAsync(
                ps => ps.TimeSheetId == input.timeSheetId && ps.EmployeeId == input.employeeId,
                cancellationToken
            );

            if (existingPayStub != null)
            {
                return new AddEmptyPayStubPayload(
                    errors: new[] { "PayStub already exists for this employee in this timesheet" }
                );
            }

            // Create minimal PayStub with required fields only
            var payStub = new PayStub
            {
                // ID will be auto-generated by EF Core IDENTITY column
                TimeSheetId = input.timeSheetId,
                EmployeeId = input.employeeId,
                Name = null, // Will be populated later if needed
                Details = new List<PayStubDetail>(), // Empty details collection
            };

            // Add to context and save
            dbContext.PayStubs.Add(payStub);
            await dbContext.SaveChangesAsync(cancellationToken);

            // Reload the PayStub with proper includes for Employee navigation properties
            // This ensures the Employee.IdNavigation is loaded so FirstName/LastName work correctly
            var payStubWithIncludes = await dbContext.PayStubs
                .Include(ps => ps.Employee)
                .ThenInclude(e => e.IdNavigation)
                .FirstOrDefaultAsync(ps => ps.Id == payStub.Id, cancellationToken);

            if (payStubWithIncludes == null)
            {
                return new AddEmptyPayStubPayload(
                    errors: new[] { "Failed to reload PayStub after creation" }
                );
            }

            // Create PayStub edge for Relay connection updates
            // Generate cursor for PayStub (int ID works directly with default serializer)
            var cursor = _nodeIdSerializer.Format(nameof(PayStub), payStubWithIncludes.Id);
            var edge = new Edge<PayStub> { Cursor = cursor, Node = payStubWithIncludes };

            // Return success payload with PayStub edge
            return new AddEmptyPayStubPayload(payStubEdge: edge);
        }
        catch (Exception ex)
        {
            // Log the exception (you might want to use a proper logging framework)
            return new AddEmptyPayStubPayload(
                errors: new[] { $"An error occurred while creating the PayStub: {ex.Message}" }
            );
        }
    }

    // Input record types moved to backend.Types.Inputs namespace
}
