using System.Data;
using backend.Data.Models;
using HotChocolate;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace backend.Types.Resolvers
{
    [ExtendObjectType(typeof(Employee))]
    public class EmployeeResolvers
    {
        [GraphQLName("ssn")]
        [Authorize(Policy = "CanViewSSN")]
        public async Task<string?> GetSsn(
            [Parent] Employee employee,
            [Service] EPRLiveDBContext db,
            [Service] ILogger<EmployeeResolvers> logger
        )
        {
            if (employee.Ssn is null || employee.Ssn.Length == 0)
                return null;

            var connection = db.Database.GetDbConnection();
            bool shouldClose = false;
            
            try
            {
                if (connection.State != ConnectionState.Open)
                {
                    await connection.OpenAsync().ConfigureAwait(false);
                    shouldClose = true;
                }

                await using var command = connection.CreateCommand();
                command.CommandText = "EXEC Core.OpenSymmetricKey; SELECT Core.Decrypt(@ssn) AS Value; EXEC Core.CloseSymmetricKey;";
                var ssnParam = command.CreateParameter();
                ssnParam.ParameterName = "@ssn";
                ssnParam.DbType = DbType.Binary;
                ssnParam.Value = employee.Ssn!;
                command.Parameters.Add(ssnParam);

                var result = await command.ExecuteScalarAsync().ConfigureAwait(false);
                return result as string;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to decrypt SSN for employee {EmployeeId}", employee.Id);
                return null;
            }
            finally
            {
                // Always ensure the connection is closed if we opened it
                if (shouldClose && connection.State == ConnectionState.Open)
                {
                    await connection.CloseAsync().ConfigureAwait(false);
                }
            }
        }
    }
}
