using backend.Data.Models;

namespace backend.Types.Outputs
{
    /// <summary>
    /// Output type for batch subclassifications query
    /// Contains subclassifications for a specific agreement-classification pair
    /// </summary>
    public class SubClassificationBatch
    {
        /// <summary>
        /// The agreement ID for this batch
        /// </summary>
        [GraphQLNonNullType]
        [ID(nameof(Agreement))]
        public required int AgreementId { get; set; }

        /// <summary>
        /// The classification ID for this batch
        /// </summary>
        [GraphQLNonNullType]
        [ID(nameof(ClassificationName))]
        public required int ClassificationId { get; set; }

        /// <summary>
        /// The subclassifications for this agreement-classification pair
        /// </summary>
        [GraphQLNonNullType]
        public required List<SubClassification> Nodes { get; set; }
    }
}