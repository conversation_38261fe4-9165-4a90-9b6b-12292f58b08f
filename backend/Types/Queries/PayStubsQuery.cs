using backend.Data.Models;
using HotChocolate;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries;

/// <summary>
/// GraphQL queries for PayStub entities.
/// Provides Node interface implementations and other PayStub-related queries.
/// </summary>
[QueryType]
[Authorize]
public static class PayStubsQuery
{
    /// <summary>
    /// Node resolver for PayStub entities used by Relay Global Object Identification.
    /// Enhanced implementation following established patterns with proper entity loading
    /// and cancellation token support.
    /// </summary>
    /// <param name="id">The PayStub ID</param>
    /// <param name="dbContext">Database context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The PayStub entity or null if not found</returns>
    [NodeResolver]
    public static async Task<PayStub?> GetPayStubNodeByIdAsync(
        [ID(nameof(PayStub))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await dbContext
            .PayStubs.AsNoTracking()
            .Include(ps => ps.Details)
            .Include(ps => ps.Employee)
            .ThenInclude(e => e.IdNavigation)
            .Include(ps => ps.TimeSheet)
            .Where(ps => ps.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// Node resolver for PayStubDetail entities used by Relay Global Object Identification.
    /// Enhanced implementation following established patterns with proper entity loading
    /// and cancellation token support.
    /// </summary>
    /// <param name="id">The PayStubDetail ID</param>
    /// <param name="dbContext">Database context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The PayStubDetail entity or null if not found</returns>
    [NodeResolver]
    public static async Task<PayStubDetail?> GetPayStubDetailNodeByIdAsync(
        [ID(nameof(PayStubDetail))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await dbContext
            .PayStubDetails.AsNoTracking()
            .Include(psd => psd.PayStub)
            .ThenInclude(ps => ps.Employee)
            .ThenInclude(e => e.IdNavigation)
            .Include(psd => psd.PayStub)
            .ThenInclude(ps => ps.TimeSheet)
            .Include(psd => psd.ReportLineItem)
            .Where(psd => psd.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }
}