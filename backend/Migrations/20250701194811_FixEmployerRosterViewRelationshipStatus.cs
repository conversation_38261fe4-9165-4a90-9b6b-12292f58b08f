﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    /// <summary>
    /// Fixes a critical data accuracy issue in EmployerRosterView where relationship status was incorrectly determined.
    /// 
    /// PROBLEM: The original view used MAX(rs.DRelationshipStatusID) which returns the highest status ID value, 
    /// not necessarily the most recent relationship status. This could return outdated status information because:
    /// - Status ID 3 (inactive) added first with RelationshipStatus.ID = 100
    /// - Status ID 1 (active) added later with RelationshipStatus.ID = 101  
    /// - MAX() would return Status ID 3 (highest number) even though Status ID 1 is more recent
    /// 
    /// SOLUTION: Uses ROW_NUMBER() OVER (PARTITION BY RelationshipID ORDER BY ID DESC) to get the most recent 
    /// relationship status record by insertion order (highest RelationshipStatus.ID = most recent).
    /// This ensures we get the chronologically latest status, not just the numerically highest status ID.
    /// </summary>
    public partial class FixEmployerRosterViewRelationshipStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var sql =
                @"
ALTER VIEW [Core].[EmployerRosterView] AS
WITH LastReportDateCTE AS (
    -- Calculates the latest report date for each employer
    SELECT
        r1.EmployerID,
        MAX(r1.PeriodEndDate) AS LastReport
    FROM Core.Reports AS r1
    GROUP BY r1.EmployerID
),
LatestRelationshipStatusCTE AS (
    -- Gets the most recent relationship status for each relationship
    SELECT
        rs.RelationshipID,
        rs.DRelationshipStatusID,
        ROW_NUMBER() OVER (PARTITION BY rs.RelationshipID ORDER BY rs.ID DESC) AS rn
    FROM Core.RelationshipStatuses AS rs
),
EmployerDataCTE AS (
    -- Gathers core employer data, payroll contact, and now relationship status ID
    SELECT
        o0.Name,
        e.EmployerIdentificationNumber,
        e.DBA,
        e.ID AS Id,
        rt.GUID,
        MAX(t.LastReport) AS LastReportedDate,
        MAX(e0.EmailAddress) AS PayrollContactEmailAddress,
        MAX(p.FirstName) AS PayrollContactFirstName,
        MAX(p.LastName) AS PayrollContactLastName,
        MAX(c.AssociationID) AS AssociationId,
        r.LeftPartyID AS ChapterId,
        lrs.DRelationshipStatusID AS RelationshipStatusID -- Added most recent Relationship Status ID to final output
    FROM Core.Relationships AS r
    INNER JOIN Core.ChapterToEmployerRelationships AS c ON r.ID = c.ID
    INNER JOIN Core.Employers AS e ON r.RightPartyID = e.ID
    INNER JOIN Core.Organizations AS o0 ON e.ID = o0.ID
    LEFT JOIN CORE.Root AS rt ON rt.ID = e.ID
    LEFT JOIN Core.Relationships AS r0 ON e.ID = r0.LeftPartyID AND r0.DRelationshipTypeID = 32 -- Payroll Contact Relationship
    LEFT JOIN Core.Persons AS p ON r0.RightPartyID = p.ID
    LEFT JOIN Core.PartiesToContactMechanisms AS p0 ON r0.RightPartyID = p0.PartyID
    LEFT JOIN Core.ContactMechanisms AS c0 ON p0.ContactMechanismID = c0.ID AND c0.DContactMechanismTypeID = 2 -- Email Type
    LEFT JOIN Core.EmailAddresses AS e0 ON c0.ID = e0.ID
    LEFT JOIN LastReportDateCTE AS t ON e.ID = t.EmployerID
    -- Join to get the most recent relationship status ID
    LEFT JOIN LatestRelationshipStatusCTE AS lrs ON r.ID = lrs.RelationshipID AND lrs.rn = 1
    WHERE r.DRelationshipTypeID = 5 -- Chapter-Employer Relationship Type
    GROUP BY
        o0.Name,
        e.EmployerIdentificationNumber,
        e.DBA,
        e.ID,
        rt.GUID,
        r.LeftPartyID,
        lrs.DRelationshipStatusID -- Include in GROUP BY since it's not aggregated
),
PhoneDataCTE AS (
    -- Gathers the first phone number found for the payroll contact
    SELECT
        e1.ID,
        p4.PhoneNumber,
        ROW_NUMBER() OVER (PARTITION BY e1.ID ORDER BY p4.ID) AS rn
    FROM Core.Relationships AS r2 -- Chapter-Employer Relationship
    INNER JOIN Core.Employers AS e1 ON r2.RightPartyID = e1.ID
    INNER JOIN Core.Relationships AS r3 ON e1.ID = r3.LeftPartyID AND r3.DRelationshipTypeID = 32 -- Payroll Contact Relationship
    INNER JOIN Core.PartiesToContactMechanisms AS p3 ON r3.RightPartyID = p3.PartyID
    INNER JOIN Core.ContactMechanisms AS c2 ON p3.ContactMechanismID = c2.ID AND c2.DContactMechanismTypeID = 1 -- Phone Type
    INNER JOIN Core.PhoneNumbers AS p4 ON c2.ID = p4.ID
    WHERE r2.DRelationshipTypeID = 5 -- Ensure we only consider employers linked to chapters
      AND p4.PhoneNumber IS NOT NULL
      AND p4.PhoneNumber <> N''
)
SELECT
    t0.Name,
    t0.EmployerIdentificationNumber AS FEIN,
    t0.DBA AS Dba,
    t0.Id,
    T0.GUID,
    t0.LastReportedDate,
    t0.PayrollContactEmailAddress,
    t0.PayrollContactFirstName,
    t0.PayrollContactLastName,
    t0.AssociationId,
    t1.PhoneNumber AS PayrollContactPhoneNumber,
    t0.ChapterId,
    t0.RelationshipStatusID -- Added most recent Relationship Status ID to final output
FROM EmployerDataCTE AS t0
LEFT JOIN PhoneDataCTE AS t1 ON t0.Id = t1.ID AND t1.rn = 1 -- Only join the first phone number

		";
            migrationBuilder.Sql(sql);
        }

        /// <inheritdoc />
        /// <summary>
        /// Rollback migration that reverts to the original FLAWED implementation.
        /// 
        /// WARNING: This rollback restores the incorrect MAX(rs.DRelationshipStatusID) logic
        /// which can return wrong relationship status data. The MAX() function returns the
        /// highest status ID value, not the most recent status record.
        /// </summary>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            var sql =
                @"
ALTER VIEW [Core].[EmployerRosterView] AS
WITH LastReportDateCTE AS (
    -- Calculates the latest report date for each employer
    SELECT
        r1.EmployerID,
        MAX(r1.PeriodEndDate) AS LastReport
    FROM Core.Reports AS r1
    GROUP BY r1.EmployerID
),  
EmployerDataCTE AS (
    -- Gathers core employer data, payroll contact, and now relationship status ID
    SELECT
        o0.Name,
        e.EmployerIdentificationNumber,
        e.DBA,
        e.ID AS Id,
        rt.GUID,
        MAX(t.LastReport) AS LastReportedDate,
        MAX(e0.EmailAddress) AS PayrollContactEmailAddress,
        MAX(p.FirstName) AS PayrollContactFirstName,
        MAX(p.LastName) AS PayrollContactLastName,
        MAX(c.AssociationID) AS AssociationId,
        r.LeftPartyID AS ChapterId,
        MAX(rs.DRelationshipStatusID) AS RelationshipStatusID -- Added Relationship Status ID
    FROM Core.Relationships AS r
    INNER JOIN Core.ChapterToEmployerRelationships AS c ON r.ID = c.ID
    INNER JOIN Core.Employers AS e ON r.RightPartyID = e.ID
    INNER JOIN Core.Organizations AS o0 ON e.ID = o0.ID
    LEFT JOIN CORE.Root AS rt ON rt.ID = e.ID
    LEFT JOIN Core.Relationships AS r0 ON e.ID = r0.LeftPartyID AND r0.DRelationshipTypeID = 32 -- Payroll Contact Relationship
    LEFT JOIN Core.Persons AS p ON r0.RightPartyID = p.ID
    LEFT JOIN Core.PartiesToContactMechanisms AS p0 ON r0.RightPartyID = p0.PartyID
    LEFT JOIN Core.ContactMechanisms AS c0 ON p0.ContactMechanismID = c0.ID AND c0.DContactMechanismTypeID = 2 -- Email Type
    LEFT JOIN Core.EmailAddresses AS e0 ON c0.ID = e0.ID
    LEFT JOIN LastReportDateCTE AS t ON e.ID = t.EmployerID
    -- Join to get the relationship status ID
    LEFT JOIN Core.RelationshipStatuses AS rs ON r.ID = rs.RelationshipID -- Join based on the Chapter-Employer Relationship ID
    WHERE r.DRelationshipTypeID = 5 -- Chapter-Employer Relationship Type
    GROUP BY
        o0.Name,
        e.EmployerIdentificationNumber,
        e.DBA,
        e.ID,
        rt.GUID,
        r.LeftPartyID
        -- MAX(rs.DRelationshipStatusID) handles the grouping. Assumes one primary status per Chapter-Employer relationship instance (r.ID)
),  
PhoneDataCTE AS (
    -- Gathers the first phone number found for the payroll contact
    SELECT
        e1.ID,
        p4.PhoneNumber,
        ROW_NUMBER() OVER (PARTITION BY e1.ID ORDER BY p4.ID) AS rn
    FROM Core.Relationships AS r2 -- Chapter-Employer Relationship
    INNER JOIN Core.Employers AS e1 ON r2.RightPartyID = e1.ID
    INNER JOIN Core.Relationships AS r3 ON e1.ID = r3.LeftPartyID AND r3.DRelationshipTypeID = 32 -- Payroll Contact Relationship
    INNER JOIN Core.PartiesToContactMechanisms AS p3 ON r3.RightPartyID = p3.PartyID
    INNER JOIN Core.ContactMechanisms AS c2 ON p3.ContactMechanismID = c2.ID AND c2.DContactMechanismTypeID = 1 -- Phone Type
    INNER JOIN Core.PhoneNumbers AS p4 ON c2.ID = p4.ID
    WHERE r2.DRelationshipTypeID = 5 -- Ensure we only consider employers linked to chapters
      AND p4.PhoneNumber IS NOT NULL
      AND p4.PhoneNumber <> N''
)  
SELECT
    t0.Name,
    t0.EmployerIdentificationNumber AS FEIN,
    t0.DBA AS Dba,
    t0.Id,
    T0.GUID,
    t0.LastReportedDate,
    t0.PayrollContactEmailAddress,
    t0.PayrollContactFirstName,
    t0.PayrollContactLastName,
    t0.AssociationId,
    t1.PhoneNumber AS PayrollContactPhoneNumber,
    t0.ChapterId,
    t0.RelationshipStatusID -- Added Relationship Status ID to final output
FROM EmployerDataCTE AS t0
LEFT JOIN PhoneDataCTE AS t1 ON t0.Id = t1.ID AND t1.rn = 1 -- Only join the first phone number

        ";
            migrationBuilder.Sql(sql);
        }
    }
}
