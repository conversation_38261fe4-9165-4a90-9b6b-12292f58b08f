using System;
using System.Net.Http.Headers;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using backend.Data.Models;
using backend.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace backend.Tests
{
    public class PayStubDetailNodeResolverTests : TestBase
    {
        private readonly TestDataBuilder _testDataBuilder;

        public PayStubDetailNodeResolverTests(TestWebApplicationFactory factory)
            : base(factory) 
        {
            _testDataBuilder = GetTestDataBuilder();
        }
        protected class PayStubDetailNodeQueryResponse
        {
            [JsonPropertyName("node")]
            public PayStubDetailNodeResponse? Node { get; set; }
        }

        protected class PayStubDetailNodeResponse
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string? Name { get; set; }

            [JsonPropertyName("totalHours")]
            public float? TotalHours { get; set; }

            [JsonPropertyName("stHours")]
            public float? STHours { get; set; }

            [JsonPropertyName("otHours")]
            public float? OTHours { get; set; }

            [JsonPropertyName("dtHours")]
            public float? DTHours { get; set; }

            [JsonPropertyName("workDate")]
            public string? WorkDate { get; set; }

            [JsonPropertyName("payStub")]
            public PayStubNode? PayStub { get; set; }
        }

        protected class PayStubNode
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string? Name { get; set; }

            [JsonPropertyName("employee")]
            public EmployeeNode? Employee { get; set; }
        }

        protected class EmployeeNode
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("firstName")]
            public string? FirstName { get; set; }

            [JsonPropertyName("lastName")]
            public string? LastName { get; set; }
        }

        [Fact]
        public async Task GetPayStubDetailAsync_WithValidId_ReturnsPayStubDetailWithRelationships()
        {
            // Arrange
            await SeedTestDataAsync();
            ClearChangeTracker();

            // Create test timesheet
            var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
                "Test Timesheet",
                "Draft", 
                "Regular",
                Guid.Parse("11111111-1111-1111-1111-111111111111")
            );

            // Create test PayStub using existing employee from test data
            var employeeId = 90002; // Using existing test employee
            var payStub = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employeeId, "Test PayStub");

            // Add PayStub details
            var details = await _testDataBuilder.CreatePayStubDetailsAsync(payStub.Id,
                (DateOnly.FromDateTime(DateTime.Now.AddDays(-1)), 8.0f, 2.0f, 0.0f)
            );

            var payStubDetailId = details[0].Id;

            var query = @"
                query GetPayStubDetail($id: ID!) {
                    node(id: $id) {
                        ... on PayStubDetail {
                            id
                            name
                            totalHours
                            stHours
                            otHours
                            dtHours
                            workDate
                            payStub {
                                id
                                name
                                employee {
                                    id
                                }
                            }
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStubDetail", payStubDetailId) };

            // Act
            var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubDetailNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Data);
            Assert.NotNull(response.Data.Node);
            Assert.Equal(CreateNodeId("PayStubDetail", payStubDetailId), response.Data.Node.Id);
            Assert.Equal(10.0f, response.Data.Node.TotalHours); // 8 + 2 + 0 = 10 hours
            Assert.Equal(8.0f, response.Data.Node.STHours);
            Assert.Equal(2.0f, response.Data.Node.OTHours);
            Assert.Equal(0.0f, response.Data.Node.DTHours);
            Assert.NotNull(response.Data.Node.WorkDate);

            // Verify PayStub relationship
            Assert.NotNull(response.Data.Node.PayStub);
            Assert.Equal(CreateNodeId("PayStub", payStub.Id), response.Data.Node.PayStub.Id);
            Assert.Equal("Test PayStub", response.Data.Node.PayStub.Name);

            // Verify Employee relationship through PayStub
            Assert.NotNull(response.Data.Node.PayStub.Employee);
            Assert.Equal(CreateNodeId("Employee", employeeId), response.Data.Node.PayStub.Employee.Id);
        }

        [Fact]
        public async Task GetPayStubDetailAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            await SeedTestDataAsync();
            
            var query = @"
                query GetPayStubDetail($id: ID!) {
                    node(id: $id) {
                        ... on PayStubDetail {
                            id
                            totalHours
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStubDetail", 999999) }; // Non-existent ID

            // Act
            var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubDetailNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Data);
            Assert.Null(response.Data.Node);
            Assert.Null(response.Errors);
        }

        [Fact]
        public async Task GetPayStubDetailAsync_WithoutAuthentication_ReturnsUnauthorized()
        {
            // Arrange
            await SeedTestDataAsync();
            
            var query = @"
                query GetPayStubDetail($id: ID!) {
                    node(id: $id) {
                        ... on PayStubDetail {
                            id
                            totalHours
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStubDetail", 1) };

            // Act - No authentication header set
            HttpClient.DefaultRequestHeaders.Authorization = null;

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubDetailNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Errors);
            Assert.Contains(response.Errors, e => e.Message.Contains("not authenticated", StringComparison.OrdinalIgnoreCase));
        }
    }
}