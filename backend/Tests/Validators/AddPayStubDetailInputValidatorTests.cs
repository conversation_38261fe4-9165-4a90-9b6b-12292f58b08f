using backend.Types.Inputs;
using backend.Validators;
using FluentValidation.TestHelper;
using Xunit;

namespace backend.Tests.Validators;

public class AddPayStubDetailInputValidatorTests
{
    private readonly AddPayStubDetailInputValidator _validator;

    public AddPayStubDetailInputValidatorTests()
    {
        _validator = new AddPayStubDetailInputValidator();
    }

    [Fact]
    public void Should_Pass_When_ValidAddPayload()
    {
        // Arrange
        var input = new AddPayStubDetailInput(
            Id: null,
            name: "Test Employee",
            workDate: new DateOnly(2024, 1, 15),
            OTHours: 2.0f,
            STHours: 8.0f,
            DTHours: 0.0f,
            jobCode: "JOB123",
            earningsCode: null,
            agreementId: 1,
            classificationId: 2,
            subClassificationId: null,
            costCenter: "CC100",
            hourlyRate: 25.50f,
            bonus: 100.0f,
            expenses: 50.0f,
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Should_Fail_When_WorkDateIsDefaultValue()
    {
        // Arrange - using default DateOnly which represents 0001-01-01
        var input = new AddPayStubDetailInput(
            Id: null,
            name: "Test Employee",
            workDate: default(DateOnly), // This should fail validation
            OTHours: null,
            STHours: 8.0f,
            DTHours: null,
            jobCode: null,
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: null,
            hourlyRate: null,
            bonus: null,
            expenses: null,
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.workDate)
            .WithErrorMessage("Work date is required.");
    }

    [Fact]
    public void Should_Pass_When_RequiredFieldsOnly()
    {
        // Arrange
        var input = new AddPayStubDetailInput(
            Id: null,
            name: null,
            workDate: new DateOnly(2024, 1, 15),
            OTHours: null,
            STHours: null,
            DTHours: null,
            jobCode: null,
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: null,
            hourlyRate: null,
            bonus: null,
            expenses: null,
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Should_Fail_When_HoursAreNegative()
    {
        // Arrange
        var input = new AddPayStubDetailInput(
            Id: null,
            name: null,
            workDate: new DateOnly(2024, 1, 15),
            OTHours: -2.0f, // Negative hours should fail
            STHours: -5.0f,
            DTHours: -1.0f,
            jobCode: null,
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: null,
            hourlyRate: null,
            bonus: null,
            expenses: null,
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.STHours)
            .WithErrorMessage("ST hours cannot be negative.");
        result.ShouldHaveValidationErrorFor(x => x.OTHours)
            .WithErrorMessage("OT hours cannot be negative.");
        result.ShouldHaveValidationErrorFor(x => x.DTHours)
            .WithErrorMessage("DT hours cannot be negative.");
    }

    [Fact]
    public void Should_Fail_When_StringFieldsExceedMaxLength()
    {
        // Arrange
        var input = new AddPayStubDetailInput(
            Id: null,
            name: null,
            workDate: new DateOnly(2024, 1, 15),
            OTHours: null,
            STHours: null,
            DTHours: null,
            jobCode: new string('A', 51), // 51 characters, exceeds limit
            earningsCode: new string('B', 51), // 51 characters, exceeds limit
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: new string('C', 51), // 51 characters, exceeds limit
            hourlyRate: null,
            bonus: null,
            expenses: null,
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.jobCode)
            .WithErrorMessage("Job code cannot exceed 50 characters.");
        result.ShouldHaveValidationErrorFor(x => x.earningsCode)
            .WithErrorMessage("Earnings code cannot exceed 50 characters.");
        result.ShouldHaveValidationErrorFor(x => x.costCenter)
            .WithErrorMessage("Cost center cannot exceed 50 characters.");
    }

    [Fact]
    public void Should_Fail_When_FinancialFieldsAreNegative()
    {
        // Arrange
        var input = new AddPayStubDetailInput(
            Id: null,
            name: null,
            workDate: new DateOnly(2024, 1, 15),
            OTHours: null,
            STHours: null,
            DTHours: null,
            jobCode: null,
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: null,
            hourlyRate: -15.0f, // Negative should fail
            bonus: -100.0f, // Negative should fail
            expenses: -50.0f, // Negative should fail
            payStubId: null,
            reportLineItemId: null,
            delete: null
        );

        // Act
        var result = _validator.TestValidate(input);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.hourlyRate)
            .WithErrorMessage("Hourly rate cannot be negative.");
        result.ShouldHaveValidationErrorFor(x => x.bonus)
            .WithErrorMessage("Bonus cannot be negative.");
        result.ShouldHaveValidationErrorFor(x => x.expenses)
            .WithErrorMessage("Expenses cannot be negative.");
    }
}