using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authentication;
using HotChocolate.Execution;
using System.Reflection;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Tool to understand service dependencies before making changes
/// Run this before any DI modifications to understand impact
/// </summary>
public static class ServiceDependencyAnalyzer
{
    public static void AnalyzeDependencies(IServiceCollection services)
    {
        var dependencyMap = new Dictionary<Type, List<Type>>();

        foreach (var service in services)
        {
            if (service.ImplementationType != null)
            {
                var constructors = service.ImplementationType.GetConstructors();
                var dependencies = constructors
                    .SelectMany(c => c.GetParameters())
                    .Select(p => p.ParameterType)
                    .Distinct()
                    .ToList();

                dependencyMap[service.ServiceType] = dependencies;
            }
        }

        // Output dependency chains for critical services
        AnalyzeServiceChain(dependencyMap, typeof(ILogger));
        AnalyzeServiceChain(dependencyMap, typeof(IAuthenticationHandler));
        AnalyzeServiceChain(dependencyMap, typeof(IRequestExecutor)); // HotChocolate
    }

    private static void AnalyzeServiceChain(Dictionary<Type, List<Type>> dependencyMap, Type serviceType)
    {
        Console.WriteLine($"\n=== Dependency Chain for {serviceType.Name} ===");
        
        if (dependencyMap.ContainsKey(serviceType))
        {
            var dependencies = dependencyMap[serviceType];
            Console.WriteLine($"Direct dependencies: {dependencies.Count}");
            
            foreach (var dependency in dependencies.Take(5)) // Limit output
            {
                Console.WriteLine($"  - {dependency.Name}");
            }
        }
        else
        {
            Console.WriteLine("No direct dependencies found or service not registered");
        }
    }

    public static void LogServiceRegistrations(IServiceCollection services, string phase)
    {
        var relevantServices = services.Where(s =>
            s.ServiceType.FullName?.Contains("Serilog") == true ||
            s.ServiceType.FullName?.Contains("Authentication") == true ||
            s.ServiceType.FullName?.Contains("Authorization") == true ||
            s.ServiceType.FullName?.Contains("HotChocolate") == true ||
            s.ServiceType.FullName?.Contains("DbContext") == true
        ).ToList();

        Console.WriteLine($"\n[{phase}] {relevantServices.Count} relevant services registered");
        foreach (var service in relevantServices.Take(15)) // Limit output
        {
            Console.WriteLine($"  {service.ServiceType.Name} -> {service.ImplementationType?.Name ?? "Factory"} ({service.Lifetime})");
        }
    }

    public static void CompareServiceCollections(IServiceCollection before, IServiceCollection after, string operation)
    {
        Console.WriteLine($"\n=== Service Collection Changes After {operation} ===");
        
        var beforeTypes = before.Select(s => s.ServiceType).ToHashSet();
        var afterTypes = after.Select(s => s.ServiceType).ToHashSet();
        
        var removed = beforeTypes.Except(afterTypes).ToList();
        var added = afterTypes.Except(beforeTypes).ToList();
        
        Console.WriteLine($"Services removed: {removed.Count}");
        foreach (var type in removed.Take(10))
        {
            Console.WriteLine($"  - {type.Name}");
        }
        
        Console.WriteLine($"Services added: {added.Count}");
        foreach (var type in added.Take(10))
        {
            Console.WriteLine($"  + {type.Name}");
        }
    }
}
