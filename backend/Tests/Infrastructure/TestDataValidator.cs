using backend.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Validates test data consistency and provides diagnostic information
/// </summary>
public class TestDataValidator
{
    public static async Task<TestDataValidationResult> ValidateTestDataAsync(
        IServiceProvider services
    )
    {
        var result = new TestDataValidationResult();

        try
        {
            using var scope = services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<EPRLiveDBContext>();

            // Validate core entities
            await ValidateRootsAsync(dbContext, result);
            await ValidateOrganizationsAsync(dbContext, result);
            await ValidateEmployersAsync(dbContext, result);
            await ValidateAgreementsAsync(dbContext, result);
            await ValidateEmployeesAsync(dbContext, result);
            await ValidateAuthenticationDataAsync(dbContext, result);

            result.IsValid = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Validation failed with exception: {ex.Message}");
            result.Exception = ex;
        }

        return result;
    }

    private static async Task ValidateRootsAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testRoots = await dbContext.Roots.Where(r => r.Id >= 90000 || r.Id == 1).ToListAsync();

        result.RootCount = testRoots.Count;

        if (testRoots.Count == 0)
        {
            result.Errors.Add("No test Root entities found");
        }

        // Check for required test root with ID 1
        var rootId1 = testRoots.FirstOrDefault(r => r.Id == 1);
        if (rootId1 == null)
        {
            result.Errors.Add("Required Root with ID 1 not found");
        }
    }

    private static async Task ValidateOrganizationsAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testOrganizations = await dbContext
            .Organizations.Where(o => o.Id >= 90000 || o.Id == 1)
            .ToListAsync();

        result.OrganizationCount = testOrganizations.Count;

        if (testOrganizations.Count == 0)
        {
            result.Errors.Add("No test Organization entities found");
        }
    }

    private static async Task ValidateEmployersAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testEmployers = await dbContext.Employers.Where(e => e.Id >= 90000).ToListAsync();

        result.EmployerCount = testEmployers.Count;

        if (testEmployers.Count == 0)
        {
            result.Errors.Add("No test Employer entities found");
        }
    }

    private static async Task ValidateAgreementsAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testAgreements = await dbContext.Agreements.Where(a => a.Id >= 90000).ToListAsync();

        result.AgreementCount = testAgreements.Count;

        if (testAgreements.Count == 0)
        {
            result.Errors.Add("No test Agreement entities found");
        }
    }

    private static async Task ValidateEmployeesAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testEmployees = await dbContext.Employees.Where(e => e.Id >= 90000).ToListAsync();

        result.EmployeeCount = testEmployees.Count;

        if (testEmployees.Count == 0)
        {
            result.Errors.Add("No test Employee entities found");
        }
    }

    private static async Task ValidateAuthenticationDataAsync(
        EPRLiveDBContext dbContext,
        TestDataValidationResult result
    )
    {
        var testUsers = await dbContext
            .AspnetUsers.Where(u => u.UserId.ToString().StartsWith("90000000"))
            .ToListAsync();

        var testMemberships = await dbContext
            .AspnetMemberships.Where(m => m.UserId.ToString().StartsWith("90000000"))
            .ToListAsync();

        var testEprUsers = await dbContext
            .Eprusers.Where(eu => eu.AspnetUserId.ToString().StartsWith("90000000"))
            .ToListAsync();

        result.AspnetUserCount = testUsers.Count;
        result.AspnetMembershipCount = testMemberships.Count;
        result.EprUserCount = testEprUsers.Count;

        if (testUsers.Count == 0)
        {
            result.Errors.Add("No test AspnetUser entities found");
        }

        if (testMemberships.Count == 0)
        {
            result.Errors.Add("No test AspnetMembership entities found");
        }

        if (testEprUsers.Count == 0)
        {
            result.Errors.Add("No test Epruser entities found");
        }

        // Check for specific test users
        var cheemaUser = testUsers.FirstOrDefault(u => u.UserName == "cheema");
        if (cheemaUser == null)
        {
            result.Errors.Add("Test user 'cheema' not found");
        }
        else
        {
            var cheemaMembership = testMemberships.FirstOrDefault(m =>
                m.UserId == cheemaUser.UserId
            );
            if (cheemaMembership == null)
            {
                result.Errors.Add("AspnetMembership for 'cheema' user not found");
            }

            var cheemaEprUser = testEprUsers.FirstOrDefault(eu =>
                eu.AspnetUserId == cheemaUser.UserId
            );
            if (cheemaEprUser == null)
            {
                result.Errors.Add("Epruser for 'cheema' user not found");
            }
        }
    }
}

public class TestDataValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public Exception? Exception { get; set; }

    // Entity counts
    public int RootCount { get; set; }
    public int OrganizationCount { get; set; }
    public int EmployerCount { get; set; }
    public int AgreementCount { get; set; }
    public int EmployeeCount { get; set; }
    public int AspnetUserCount { get; set; }
    public int AspnetMembershipCount { get; set; }
    public int EprUserCount { get; set; }

    public override string ToString()
    {
        var summary =
            $"TestDataValidation: Valid={IsValid}, "
            + $"Roots={RootCount}, Orgs={OrganizationCount}, "
            + $"Employers={EmployerCount}, Agreements={AgreementCount}, "
            + $"Employees={EmployeeCount}, Users={AspnetUserCount}, "
            + $"Memberships={AspnetMembershipCount}, EprUsers={EprUserCount}";

        if (Errors.Count > 0)
        {
            summary += $"\nErrors: {string.Join("; ", Errors)}";
        }

        return summary;
    }
}
