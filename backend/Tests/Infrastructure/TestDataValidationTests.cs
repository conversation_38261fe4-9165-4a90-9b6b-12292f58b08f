using backend.Tests.Infrastructure;
using Xunit;
using Xunit.Abstractions;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Tests to validate that test data is properly seeded and consistent
/// </summary>
public class TestDataValidationTests : TestBase
{
    private readonly ITestOutputHelper _output;

    public TestDataValidationTests(ITestOutputHelper output)
        : base(new TestWebApplicationFactory())
    {
        _output = output;
    }

    [Fact]
    public async Task TestData_Should_Be_Valid_And_Complete()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var validationResult = await TestDataValidator.ValidateTestDataAsync(Factory.Services);

        // Assert
        _output.WriteLine("=== TEST DATA VALIDATION RESULTS ===");
        _output.WriteLine(validationResult.ToString());

        if (validationResult.Exception != null)
        {
            _output.WriteLine($"Exception: {validationResult.Exception}");
        }

        foreach (var error in validationResult.Errors)
        {
            _output.WriteLine($"Error: {error}");
        }

        Assert.True(
            validationResult.IsValid,
            $"Test data validation failed. Errors: {string.Join("; ", validationResult.Errors)}"
        );
    }

    [Fact]
    public async Task TestData_Cleanup_Should_Remove_All_Test_Data()
    {
        // Arrange
        await SeedTestDataAsync();

        // Verify data exists
        var beforeValidation = await TestDataValidator.ValidateTestDataAsync(Factory.Services);
        Assert.True(beforeValidation.AspnetUserCount > 0, "Test data should exist before cleanup");

        // Act
        await CleanupTestDataAsync();

        // Assert
        var afterValidation = await TestDataValidator.ValidateTestDataAsync(Factory.Services);
        _output.WriteLine("=== AFTER CLEANUP VALIDATION ===");
        _output.WriteLine(afterValidation.ToString());

        Assert.Equal(0, afterValidation.AspnetUserCount);
        Assert.Equal(0, afterValidation.AspnetMembershipCount);
        Assert.Equal(0, afterValidation.EprUserCount);
        Assert.Equal(0, afterValidation.EmployeeCount);
        Assert.Equal(0, afterValidation.EmployerCount);
        Assert.Equal(0, afterValidation.AgreementCount);
    }

    [Fact]
    public async Task UserInfo_TestData_Should_Support_Cheema_User()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act - Try to get user info for cheema user
        var username = "cheema";
        var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));

        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var response = await HttpClient.GetAsync($"/api/UserInfo?username={username}");

        // Assert
        _output.WriteLine($"UserInfo Response Status: {response.StatusCode}");
        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            _output.WriteLine($"Error Content: {errorContent}");
        }

        Assert.True(
            response.IsSuccessStatusCode,
            $"UserInfo endpoint should work with test data. Status: {response.StatusCode}"
        );
    }

    [Fact]
    public async Task Employee_TestData_Should_Support_GraphQL_Queries()
    {
        // Arrange
        await SeedTestDataAsync();

        var query =
            @"
            query GetEmployees($employerGuid: UUID!) {
                employeesByEmployerGuidAsync(employerGuid: $employerGuid) {
                    nodes {
                        id
                        firstName
                        lastName
                        active
                    }
                    totalCount
                }
            }";

        var variables = new { employerGuid = "11111111-1111-1111-1111-111111111111" };

        // Act
        var result = await ExecuteAuthenticatedGraphQLQueryAsync<dynamic>(query, variables);

        // Assert
        _output.WriteLine($"GraphQL Result: {System.Text.Json.JsonSerializer.Serialize(result)}");
        Assert.NotNull(result);
    }
}
