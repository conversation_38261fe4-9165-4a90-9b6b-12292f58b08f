using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Validate our test infrastructure works correctly
/// These tests must pass before proceeding to subsequent phases
/// </summary>
public class BaselineValidationTests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;

    public BaselineValidationTests(TestWebApplicationFactory factory)
    {
        _factory = factory;
    }

    [Fact]
    public void TestFactory_Should_Create_Unique_Instances()
    {
        using var factory1 = new TestWebApplicationFactory();
        using var factory2 = new TestWebApplicationFactory();

        var client1 = factory1.CreateClient();
        var client2 = factory2.CreateClient();

        // Each factory should have separate service providers
        Assert.NotSame(factory1.Services, factory2.Services);
    }

    [Fact]
    public void Logger_Should_Be_Isolated_Per_Test()
    {
        // Verify no "logger frozen" exceptions occur
        using var factory = new TestWebApplicationFactory();
        var client = factory.CreateClient();

        // This should not throw
        var logger = factory.Services.GetRequiredService<ILogger<BaselineValidationTests>>();
        logger.LogInformation("Test isolation working");
    }

    [Fact]
    public void Authentication_Should_Be_Configured()
    {
        using var factory = new TestWebApplicationFactory();
        var client = factory.CreateClient();

        // Verify authentication services are available
        var authSchemeProvider = factory.Services.GetService<Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider>();
        Assert.NotNull(authSchemeProvider);
    }

    [Fact]
    public void Database_Should_Be_Available()
    {
        using var factory = new TestWebApplicationFactory();
        using var scope = factory.Services.CreateScope();
        
        var dbContext = scope.ServiceProvider.GetRequiredService<backend.Data.Models.EPRLiveDBContext>();
        Assert.NotNull(dbContext);
        
        // Should be able to ensure database is created without errors
        dbContext.Database.EnsureCreated();
    }
}
