using System;

namespace backend.Tests.Infrastructure
{
    /// <summary>
    /// Helper methods for common test assertions
    /// </summary>
    public static class AssertionHelpers
    {
        /// <summary>
        /// Checks if an error message indicates an authorization/authentication failure.
        /// Uses case-insensitive matching for common authorization error patterns.
        /// </summary>
        /// <param name="errorMessage">The error message to check</param>
        /// <returns>True if the message indicates an authorization error, false otherwise</returns>
        public static bool IsAuthorizationError(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return false;

            var lowerMessage = errorMessage.ToLower();
            
            return lowerMessage.Contains("unauthorized") ||
                   lowerMessage.Contains("not authorized") ||
                   lowerMessage.Contains("access denied") ||
                   lowerMessage.Contains("forbidden") ||
                   lowerMessage.Contains("authorization") ||
                   lowerMessage.Contains("authentication") ||
                   lowerMessage.Contains("invalid token");
        }
    }
}
