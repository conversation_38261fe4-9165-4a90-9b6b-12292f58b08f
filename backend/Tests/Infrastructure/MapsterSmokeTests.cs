using Mapster;
using Xunit;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Smoke tests to ensure Mapster configuration compiles correctly
/// </summary>
public class MapsterSmokeTests : TestBase
{
    public MapsterSmokeTests(TestWebApplicationFactory factory)
        : base(factory)
    {
    }

    [Fact]
    public void Mapster_Configuration_Should_Compile()
    {
        // Arrange & Act - The configuration should compile without throwing
        var config = Factory.Services.GetRequiredService<TypeAdapterConfig>();
        
        // Assert - Configuration should be compiled successfully
        Assert.NotNull(config);
        
        // Verify the configuration can be compiled explicitly
        var exception = Record.Exception(() => config.Compile());
        Assert.Null(exception);
    }

    [Fact]
    public void GlobalSettings_Should_Remain_Unused()
    {
        // Arrange & Act
        var globalRules = TypeAdapterConfig.GlobalSettings.RuleMap;
        
        // Assert - GlobalSettings should remain unconfigured
        Assert.False(globalRules.Any(), 
            "GlobalSettings should remain unused – rely on DI-scoped config only.");
    }

    [Fact]
    public void CustomViews_Mappings_Should_Be_Configured()
    {
        // Arrange
        var config = Factory.Services.GetRequiredService<TypeAdapterConfig>();
        
        // Act & Assert - Verify specific mappings exist
        var createCustomViewsRules = config.RuleMap
            .Where(kvp => kvp.Key.Source == typeof(backend.Types.Inputs.CreateCustomViews) 
                       && kvp.Key.Destination == typeof(backend.Data.Models.Setting))
            .ToList();
            
        var updateCustomViewsRules = config.RuleMap
            .Where(kvp => kvp.Key.Source == typeof(backend.Types.Inputs.UpdateCustomViews) 
                       && kvp.Key.Destination == typeof(backend.Data.Models.Setting))
            .ToList();
            
        var settingToCustomViewsRules = config.RuleMap
            .Where(kvp => kvp.Key.Source == typeof(backend.Data.Models.Setting) 
                       && kvp.Key.Destination == typeof(backend.Types.Outputs.CustomViews))
            .ToList();

        Assert.True(createCustomViewsRules.Any(), 
            "CreateCustomViews -> Setting mapping should be configured");
        Assert.True(updateCustomViewsRules.Any(), 
            "UpdateCustomViews -> Setting mapping should be configured");
        Assert.True(settingToCustomViewsRules.Any(), 
            "Setting -> CustomViews mapping should be configured");
    }
}