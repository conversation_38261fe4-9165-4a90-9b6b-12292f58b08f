using System;
using System.Linq;
using System.Net.Http.Headers;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using backend.Data.Models;
using backend.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace backend.Tests
{
    public class PayStubNodeResolverTests : TestBase
    {
        private readonly TestDataBuilder _testDataBuilder;

        public PayStubNodeResolverTests(TestWebApplicationFactory factory)
            : base(factory) 
        {
            _testDataBuilder = GetTestDataBuilder();
        }
        protected class PayStubNodeQueryResponse
        {
            [JsonPropertyName("node")]
            public PayStubNodeResponse? Node { get; set; }
        }

        protected class PayStubNodeResponse
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string? Name { get; set; }

            [JsonPropertyName("totalHours")]
            public float? TotalHours { get; set; }

            [JsonPropertyName("employee")]
            public EmployeeNodeResponse? Employee { get; set; }

            [JsonPropertyName("details")]
            public PayStubDetailConnection? Details { get; set; }

            [JsonPropertyName("timeSheet")]
            public TimeSheetNodeResponse? TimeSheet { get; set; }
        }

        protected class TimeSheetNodeResponse
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string? Name { get; set; }
        }

        protected class EmployeeNodeResponse
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("firstName")]
            public string? FirstName { get; set; }

            [JsonPropertyName("lastName")]
            public string? LastName { get; set; }
        }

        protected class PayStubDetailConnection
        {
            [JsonPropertyName("edges")]
            public PayStubDetailEdge[]? Edges { get; set; }
        }

        protected class PayStubDetailEdge
        {
            [JsonPropertyName("node")]
            public PayStubDetailNode? Node { get; set; }
        }

        protected class PayStubDetailNode
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("totalHours")]
            public float? TotalHours { get; set; }

            [JsonPropertyName("stHours")]
            public float? STHours { get; set; }

            [JsonPropertyName("otHours")]
            public float? OTHours { get; set; }

            [JsonPropertyName("dtHours")]
            public float? DTHours { get; set; }
        }

        [Fact]
        public async Task GetPayStubAsync_WithValidId_ReturnsPayStubWithRelationships()
        {
            // Arrange
            await SeedTestDataAsync();
            ClearChangeTracker();

            // Create test timesheet
            var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
                "Test Timesheet",
                "Draft", 
                "Regular",
                Guid.Parse("11111111-1111-1111-1111-111111111111")
            );

            // Create test PayStub using existing employee from test data
            var employeeId = 90002; // Using existing test employee
            var payStub = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employeeId, "Test PayStub");

            // Add PayStub details
            var details = await _testDataBuilder.CreatePayStubDetailsAsync(payStub.Id,
                (DateOnly.FromDateTime(DateTime.Now.AddDays(-1)), 8.0f, 2.0f, 0.0f),
                (DateOnly.FromDateTime(DateTime.Now), 8.0f, 0.0f, 1.0f)
            );

            var query = @"
                query GetPayStub($id: ID!) {
                    node(id: $id) {
                        ... on PayStub {
                            id
                            name
                            totalHours
                            employee {
                                id
                            }
                            timeSheet {
                                id
                                name
                            }
                            details {
                                edges {
                                    node {
                                        id
                                        totalHours
                                        stHours
                                        otHours
                                        dtHours
                                    }
                                }
                            }
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStub", payStub.Id) };

            // Act
            var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Data);
            Assert.NotNull(response.Data.Node);
            Assert.Equal(CreateNodeId("PayStub", payStub.Id), response.Data.Node.Id);
            Assert.Equal("Test PayStub", response.Data.Node.Name);
            Assert.Equal(19.0f, response.Data.Node.TotalHours); // 8+2+0 + 8+0+1 = 19 hours
            
            // Verify employee relationship
            Assert.NotNull(response.Data.Node.Employee);
            Assert.Equal(CreateNodeId("Employee", employeeId), response.Data.Node.Employee.Id);

            // Verify timesheet relationship
            Assert.NotNull(response.Data.Node.TimeSheet);
            Assert.Equal(CreateNodeId("TimeSheet", timeSheet.Id), response.Data.Node.TimeSheet.Id);
            Assert.Equal("Test Timesheet", response.Data.Node.TimeSheet.Name);

            // Verify details relationship
            Assert.NotNull(response.Data.Node.Details);
            Assert.NotNull(response.Data.Node.Details.Edges);
            Assert.Equal(2, response.Data.Node.Details.Edges.Length);
            var totalHours = response.Data.Node.Details.Edges.Sum(e => e.Node?.TotalHours ?? 0);
            Assert.Equal(19.0f, totalHours);
        }

        [Fact]
        public async Task GetPayStubAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            await SeedTestDataAsync();
            
            var query = @"
                query GetPayStub($id: ID!) {
                    node(id: $id) {
                        ... on PayStub {
                            id
                            name
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStub", 999999) }; // Non-existent ID

            // Act
            var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Data);
            Assert.Null(response.Data.Node);
            Assert.Null(response.Errors);
        }

        [Fact]
        public async Task GetPayStubAsync_WithWrongEntityType_ReturnsNull()
        {
            // Arrange
            await SeedTestDataAsync();
            
            var query = @"
                query GetPayStub($id: ID!) {
                    node(id: $id) {
                        ... on PayStub {
                            id
                            name
                        }
                    }
                }";

            // Use Employee ID but request PayStub type
            var variables = new { id = CreateNodeId("Employee", 90002) };

            // Act
            var token = CreateTestJwtToken(Guid.Parse("11111111-1111-1111-1111-111111111111"));
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Data);
            Assert.Null(response.Data.Node); // Should return null as types don't match
        }

        [Fact]
        public async Task GetPayStubAsync_WithoutAuthentication_ReturnsUnauthorized()
        {
            // Arrange
            await SeedTestDataAsync();
            
            var query = @"
                query GetPayStub($id: ID!) {
                    node(id: $id) {
                        ... on PayStub {
                            id
                            name
                        }
                    }
                }";

            var variables = new { id = CreateNodeId("PayStub", 1) };

            // Act - No authentication header set
            HttpClient.DefaultRequestHeaders.Authorization = null;

            var response = await ExecuteGraphQLQueryWithErrorsAsync<PayStubNodeQueryResponse>(query, variables);

            // Assert
            Assert.NotNull(response.Errors);
            Assert.Contains(response.Errors, e => e.Message.Contains("not authenticated", StringComparison.OrdinalIgnoreCase));
        }
    }
}